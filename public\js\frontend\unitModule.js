var unitModule = function() {
    var unitModuleData = function() {
        deleteSingleData(site_url + 'delete-unit-module/');
    };

    var elementOfUnitCompetency = function() {

        deleteSingleData(site_url + 'delete-element-competency/');

        $('body').on('click', '.status', function() {
            var id = $(this).attr('data-id');
            var status = $(this).attr('data-status');
            $.ajax({
                type: "POST",
                url: site_url + "subject/ajaxAction",
                headers: {'X-CSRF-TOKEN': $('input[name="_token"]').val()},
                data: {'action': 'changeStatus', 'data': {'id': id, 'status': status}},
                success: function(data) {
                    var data = jQuery.parseJSON(data);
                    setTimeout(function() {
                        sessionDisplayMessage(data['status'], data['message']);
                        location.reload();
                    }, 300);
                },
                error: function(err) {
                    //alert("error" + JSON.stringify(err));
                }
            });
        });

        $("#unitCometency").validate({
            rules: {
                unit_id: {
                    required: true
                },
                competency_criteria: {
                    required: true
                },
                notes: {
                    required: true
                }
            },
            errorPlacement: function(error, element) {
            }
        });
    };

    var unitModuleAddForm = function() {

        $('body').on('change', '#course_type', function () {
            var courseTypeText = $('#course_type :selected').text().toLowerCase();
            let flag = (jQuery.inArray(courseTypeText.trim(), specialType) != -1) ? true : false;
            $('#is_higher_education').val((flag ? 1 : ''));
            if(flag){
                $('button[type="submit"]').attr('disabled','disabled');
            }else{
                $('button[type="submit"]').removeAttr('disabled');
            }
        });

        $('#subject_name').on('change', function() {
            getCourseMethod();
        });
        getCourseMethod();
        
        function getCourseMethod() {
            var SubjectId = $("#subject_name").val();
                 if(SubjectId==""){
                    $(".nonSubject").show();
                 }else{
                     $(".nonSubject").hide();
                 }
        }
        
        radioCheckboxClass();

        $('.popoverblock').popover();
        $.validator.addMethod("unitRegex", function(value, element) {
            return this.optional(element) || /^[a-z0-9'/\-\s]+$/i.test(value);
        }, "Username must contain only letters, numbers, or dashes.");
        
        var rules1 = {
            field_education: {
                required: true
            },
            delivery_mode: {
                required: true
            },
            course_type: {
                required: true
            },
            grading_type: {
                required: true
            },
            unit_code: {
                required: true,
                alphanumeric: true
            },
            vet_unit_code: {
                required: true,
                alphanumeric: true
            },
            unit_name: {
                required: true
            },
            nominal_hours: {
                number: true
            },
            tution_fees: {
                //required: true,
                number: true
            }
        };
        handleFormValidateNew($("#addUnitModule"), rules1);
        
          $('#unit_code').autocomplete({
            
            source: site_url+"unit-autocomplete",
            minlenght: 1,
            autoFocus: true,
            search  : function(){
                $(this).parent().find('.loader').empty(),
                $(this).parent().find('.loader').append('<img src="/icon/loading.gif">')},
            open    : function(){$(this).parent().find('.loader').html('');},
            select: function(e, ui) {
            }
        });

           $('body').on('click', '#getData', function() {
            
          var nationCode = $("#unit_code").val();
            $.ajax({
                type: "POST",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                },
                url: site_url + "courses/ajaxAction",
                data: {'action': 'nationCodeApiCallUnit', 'data': {'nationCode': nationCode}},
                success: function(data) {
                    var data = JSON.parse(data);
                    var totalNominalHours = parseInt(data["totalNominalHours"])/60;
                    $('#field_education option[value='+data["fieldOfEducation"]+']').attr("selected",true);
                    $('#unit_name').val(data["unitName"]);
                    $('#nominal_hours').val(totalNominalHours);
                    $('#vet_unit_code').val(nationCode);
                    if(data["moduleUnitFlag"]=="M"){
                        $("input[name=module_unit_flag][value='0']").iCheck("toggle");
                    }
                    if(data["moduleUnitFlag"]=="C"){
                        $("input[name=module_unit_flag][value='1']").iCheck("toggle");
                    }
                    $('#field_education').select2();
                }
            });
        });
        setTimeout(function(){
            
            var that = $('.existing_unit_yes_no:checked').val();
            var checkformType = $('#unitId').val();
            if(checkformType == ''){
                if (that == 0) {
                    $('#existing_unit').hide();
                    $('#new_unit_div').show();
                } else {
                    $('#existing_unit').show();
                    $('#new_unit_div').hide();
                }
            }            
        },100);
        $('body').on('ifClicked', '.existing_unit_yes_no', function () {
            var that = $(this).val();
            if (that == 0) {
                $('#existing_unit').hide();
                $('#new_unit_div').show();
            } else {
                $('#existing_unit').show();
                $('#new_unit_div').hide();
            }
        });

        $('body').on('keyup', '.searchUnitInput ', function(e) {
            let searchText = $(this).val().toLowerCase();
            let action = $('#existingUnitList .unitNameLists');;
            action.each(function(){
                if (searchText.length > 0){
                    let label = $(this).text().toLowerCase();
                    if (label.includes(searchText))
                        $(this).fadeIn();
                    else
                        $(this).fadeOut();
                } else {
                        $(this).fadeIn();
                }
            });
        });
    };

    var searchData = function() {

        $(".clearData").on("click", function() {
            $('.searchField').val('');
            $('.searchData').attr('data-click', '1');
            $(".searchData").trigger("click");
            window.location.href = '/view-unit-module-list';
        });

        $(".searchData").on("click", function() {

            var filter_by = $('select[name=filter_by]').val();
            if (filter_by.length < 1) {
                $(".paginationView").show();
            } else {
                $(".paginationView").hide();
            }
            $.ajax({
                type: "POST",
                url: site_url + "subject/ajaxAction",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'searchUnitModule', 'data': {filter_by: filter_by}},
                success: function(data) {
                    $("#unitModuleData tbody").html(data);
                    $('#noRecords').hide();
                },
                error: function(err) {
                    //alert("error" + JSON.stringify(err));
                }
            });
        });
    };

    return{
        initUnitModule: function() {
            $('.js-example-basic-single').select2();
            unitModuleData();
            searchData();
        },
        initAddUnitModule: function() {
            $('.js-example-basic-single').select2();
            $('.select2-selection__rendered').removeAttr('title');
            unitModuleAddForm();
        },
        initElementOfUnitCompetency: function() {
            elementOfUnitCompetency();
            radioCheckboxClass();
        }
    };
}();