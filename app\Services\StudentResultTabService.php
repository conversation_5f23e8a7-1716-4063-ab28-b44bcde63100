<?php

namespace App\Services;

use App\DTO\studentProfile\AddStudentSubjectOutcome;
use App\DTO\studentProfile\AddStudentUnitAvetimissValue;
use App\DTO\studentProfile\AddStudentUnitOutcome;
use App\DTO\studentProfile\AddTcsiStudentUnitEnrollmentInformation;
use App\DTO\studentProfile\UnitAutoEnrollDTO;
use App\Exceptions\ApplicationException;
use App\Helpers\Helpers;
use App\Http\Requests\FormValidation\StudentProfile\UnitAutoEnrollRequest;
use App\Model\v2\AssessmentTask;
use App\Model\v2\AssessmentTaskUnit;
use App\Model\v2\AssignedAssessmentTask;
use App\Model\v2\CampusVenue;
use App\Model\v2\CollegeCampus;
use App\Model\v2\CourseCalendar;
use App\Model\v2\CourseType;
use App\Model\v2\ResultGrade;
use App\Model\v2\Student;
use App\Model\v2\Courses;
use App\Model\v2\StudentAssignedAssessmentTask;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\StudentSubjectEnrolmentHistory;
use App\Model\v2\StudentUnitEnrollment;
use App\Model\v2\StudyReason;
use App\Model\v2\TcsiStudentUnitEnrollment;
use App\Model\v2\Semester;
use App\Model\v2\StudentAttendance;
use App\Model\v2\Subject;
use App\Model\v2\Timetable;
use App\Model\v2\TimetableDetail;
use App\Model\v2\UnitModule;
use App\Repositories\CommonRepository;
use Domains\Moodle\Traits\MoodleStatusTrait;
use Illuminate\Http\Request;
use App\Repositories\StudentResultTabRepository;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Auth;
use App\Traits\CommonTrait;
use App\Traits\StudentProfileTrait;
use Illuminate\Support\Facades\DB;
use App\Process\StudentProfile\UpdateAssessmentProcess;

class StudentResultTabService extends CommonRepository
{
    use CommonTrait;
    use MoodleStatusTrait;
    use StudentProfileTrait;
    private $studentResultTabRepository;

    public function __construct(StudentResultTabRepository $studentResultTabRepository)
    {
        $this->studentResultTabRepository = $studentResultTabRepository;
    }
    public function getResultTabData(Request $request){

        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $studentCourseId = $request->student_course_id;

        $studentCourse = StudentCourses::where('id', '=', $studentCourseId)->get(['course_id'])->first();

        $data['totalAssessmentCount'] =  StudentAssignedAssessmentTask::query()
                    ->join('rto_assessment_tasks as rst', 'rst.id', '=', 'rto_student_assigned_assessment_tasks.assessment_task_id')
                    ->join('rto_student_subject_enrolment as rsse', 'rsse.batch', '=', 'rto_student_assigned_assessment_tasks.batch')
                    ->where([
                        'rto_student_assigned_assessment_tasks.student_id' => $studentId,
                        'rto_student_assigned_assessment_tasks.course_id' => $studentCourse->course_id
                        ])
                    ->groupBy('rto_student_assigned_assessment_tasks.assessment_task_id')
                    ->get()
                    ->count();


                    $isHigherEd = ($request->is_higher_ed == 'true') ? true : false;
                    if($isHigherEd){
                            $passResultGrade = ResultGrade::select(DB::raw('GROUP_CONCAT(DISTINCT id  SEPARATOR ", ") AS ids'))->where('final_outcome_code',20)->get()->toArray();
                            $failResultGrade = ResultGrade::select(DB::raw('GROUP_CONCAT(DISTINCT id  SEPARATOR ", ") AS ids'))->where('final_outcome_code',30)->get()->toArray();
                            $totalAndCompetentSubjects = StudentSubjectEnrolment::join('rto_student_courses', 'rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id')
                                    ->selectRaw('COUNT(*) as total_subjects, SUM(CASE WHEN mark_outcome IN ('.$passResultGrade['0']['ids'].') THEN 1 ELSE 0 END) as competent_subjects,SUM(CASE WHEN mark_outcome IN ('.$failResultGrade['0']['ids'].') THEN 1 ELSE 0 END) as failed_subjects')
                                    ->where([
                                        'rto_student_subject_enrolment.college_id' => $collegeId,
                                        'rto_student_subject_enrolment.student_id' => $studentId,
                                        'rto_student_courses.id' => $studentCourseId
                                    ])
                                    ->first();
                    }else{
                        $totalAndCompetentSubjects = StudentSubjectEnrolment::join('rto_student_courses', 'rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id')
                        ->selectRaw('COUNT(*) as total_subjects, SUM(CASE WHEN rto_student_subject_enrolment.final_outcome IN ("C", "CT", "RPL") THEN 1 ELSE 0 END) as competent_subjects,SUM(CASE WHEN rto_student_subject_enrolment.final_outcome = "NYC" THEN 1 ELSE 0 END) as failed_subjects')
                        ->where([
                            'rto_student_subject_enrolment.college_id' => $collegeId,
                            'rto_student_subject_enrolment.student_id' => $studentId,
                            'rto_student_courses.id' => $studentCourseId
                        ])
                        ->withStudentCourseId($studentCourseId)
                        ->first();
                    }
        $totalSubject = 0;
        $competentSubject = 0;

        if ($totalAndCompetentSubjects) {
            $totalSubject = $totalAndCompetentSubjects->total_subjects;
            $competentSubject = $totalAndCompetentSubjects->competent_subjects;
            $failedSubjects = $totalAndCompetentSubjects->failed_subjects;
        }

        $data['totalCompletion'] = ($totalSubject > 0 && $competentSubject) ? number_format(($competentSubject * 100 / $totalSubject), 2, '.', '') . '%' : '0.00%';
        $data['totalCompletionLabel'] = ($totalSubject > 0 && $competentSubject) ? number_format(($competentSubject * 100 / $totalSubject), 2, '.', '') . '%' : '19.00%';
        $data['failedSubjects'] = ($totalSubject > 0 && $failedSubjects > 0) ? number_format(($failedSubjects * 100 / $totalSubject), 2, '.', '') . '%' : '0.00%';
        $data['failedSubjectsLabel'] = ($totalSubject > 0 && $failedSubjects > 0) ? number_format(($failedSubjects * 100 / $totalSubject), 2, '.', '') . '%' : '19.00%';
        $data['NotStartedSubjects'] = ($totalSubject > 0) ? number_format((($totalSubject - $competentSubject - $failedSubjects) * 100 / $totalSubject), 2, '.', '') . '%' : '0.00%';
        $data['NotStartedSubjectsLabel'] = (($totalSubject - $competentSubject - $failedSubjects) > 0) ? number_format((($totalSubject - $competentSubject - $failedSubjects) * 100 / $totalSubject), 2, '.', '') . '%' : '20.00%';
        $data['passCount'] = !empty($competentSubject) ? $competentSubject:0;
        $data['failedCount'] = !empty($failedSubjects) ? $failedSubjects:0;
        $data['totalSubject'] = !empty($totalSubject) ? $totalSubject:0;

        $data['semesterData'] = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rsse.semester_id')
            ->leftjoin('rto_student_courses', 'rto_student_courses.course_id', '=', 'rsse.course_id')
            ->where(['rsse.college_id' => $collegeId, 'rsse.student_id' => $studentId,'rto_student_courses.id' => $studentCourseId])
            ->withStudentCourseId($studentCourseId)
            ->select([
                DB::raw("
                    IF(
                        rto_semester.semester_name IS NOT NULL,
                        CONCAT(rto_semester.semester_name, ' Term ', rsse.term, ' '),
                        'Without Semester'
                    ) as semester_term
                "),
                'rsse.semester_id','rsse.term', 'rto_student_courses.id as student_course_id','rsse.id'])
           ->groupBy('semester_term')
            ->orderByDesc('rto_semester.id')
            ->orderByDesc('rsse.term')
            ->get()->toArray();

        return $data;
    }
    public function studentResultUnitData($request)
    {
        $isMoodleConnect = $this->isMoodleConnected();
        $result = $this->studentResultTabRepository->studentResultUnitData($request);
        return [
            'data' => $result['data'],
            'total' => $result['total'],
            'isMoodleConnect' => $isMoodleConnect
        ];
    }
    public function studentStatusUnitData($request)
    {
         $result = $this->studentResultTabRepository->studentStatusUnitData($request);
        return [
            'data' => $result['data'],
            'total' => $result['total']
        ];
    }

    public function checkPreviousCourseFound($request)
    {
        $studentCourse = StudentCourses::find($request->student_course_id);

        $findAnotherCourse = StudentCourses::where([
            'student_id'=>$studentCourse->student_id,
            'course_id'=>$studentCourse->course_id
        ])->where('id' ,'!=',$request->student_course_id)->get()->count();

        return ($findAnotherCourse)?false:true;
    }
    public function checkPreviousEnrollUnit($request)
    {
        $unitsId = StudentProfileTrait::getCommonUnitIds($request->student_course_id, $request->college_id);
        return !empty( $unitsId);
    }

    public function studentResultSemesterData($request, $countOnly=false){

        $collegeId = Auth::user()->college_id;
        $studentId = $request->student_id;
        $studentCourseId = $request->student_course_id;
        $semesterId = $request->semester_id;
        $termId = $request->term_id;
        $finalOutcomeArr = Config::get('constants.arrSelectFinalOutcome');
        $finalOutcomeArr[''] = 'N/A';
        $finalOutcomeArr[0] = 'N/A';
        $post = ($request->input()) ? $request->input() : [];
        $studentCourseObj = StudentCourses::find($request->student_course_id);
        $courseStatus = ($studentCourseObj->status == StudentCourses::STATUS_COMPLETED);

        $isHigherEd = ($request->is_higher_ed == 'true') ? true : false;
        if($isHigherEd){
            $markOutcomeArr = $this->getResultGradeList($collegeId);
        }

        $columnArr = array(
            DB::raw("CONCAT('', rto_subject.subject_code, ':',rto_subject.subject_name) as subject_name"),
            'rsse.activity_start_date as course_activity_date',
            'rsse.activity_finish_date as actual_end_date',
            'rsse.final_outcome as final_outcome',
            'rsse.final_outcome as compentancy',
            'rsse.semester_id',
            'rsse.unit_id',
            'rsse.is_result_lock',
            'rsse.subject_id',
            'rsse.student_id',
            'rsse.course_id',
            'rsse.mark_outcome',
            'rsse.subject_attempt',
            'rsse.grade',
            'rsse.marks',
            'rsse.last_assessment_approved_date',
            'rsse.activity_start_date as start_date',
            'rsse.activity_finish_date as finish_date',
            'rsse.batch as batch',
            'rsse.id',
            'rsu.unit_code',
            'rsu.unit_name',
            DB::raw("CONCAT(rto_semester.semester_name,'(Term', rsse.term, ')') as semester_term"),
            DB::raw("CONCAT(rst.first_name,' ', rst.last_name) as teacher_name"),
            'rto_timetable.teacher_id',
            'rsse.created_at as createdUnitDate',
        );

        $query = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rsse.semester_id')
            ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rsse.subject_id')
            ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rsse.unit_id')
            ->leftjoin('rto_student_courses', 'rto_student_courses.course_id', '=', 'rsse.course_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rsse.course_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_timetable', function($join)
            {
                $join->on('rto_timetable.subject_id', '=', 'rsse.subject_id');
                $join->on('rto_timetable.batch','=', 'rsse.batch');
            })
            ->leftjoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rto_timetable.teacher_id')
            ->withStudentCourseId($studentCourseId)
            ->where([
                'rsse.college_id' => $collegeId,
                'rsse.student_id' => $studentId,
                'rto_student_courses.student_id' => $studentId,
                'rto_student_courses.id' => $studentCourseId,
                'rsse.semester_id' => $semesterId,
                'rsse.term' => $termId
            ])
            ->select($columnArr)
            ->GroupBy('rsse.id')
            ->OrderBy('rsse.created_at','DESC');

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPaginationV2($query, $post, $countOnly);

        if(!$countOnly) {
            foreach ($result['data'] as $k => $row) {
                $result['data'][$k]['batch'] = ($row['batch'] != null) ? $row['batch'] : 'No Batch';
                $result['data'][$k]['semester_term'] = ($row['semester_term'] != null) ? $row['semester_term'] : 'No Semester';
                $result['data'][$k]['SN'] = ($k+1);
                if ($isHigherEd) {
                    $finalOutcomeVal = isset($markOutcomeArr[$row['mark_outcome']]) ? $markOutcomeArr[$row['mark_outcome']] : "-";
                } else {
                    $finalOutcomeVal = $row['final_outcome'];
                }
                $result['data'][$k]['final_outcome'] = $finalOutcomeVal;
                $result['data'][$k]['marks'] = $row['marks'];
                $result['data'][$k]['final_outcome1'] = $finalOutcomeVal; //$finalOutcomeArr[$row['final_outcome']];
                $result['data'][$k]['is_higher_ed'] = $isHigherEd;
                $result['data'][$k]['is_course_complete'] = $courseStatus;
            }
        }
        return $result;
    }

    public function UpdateStudentResultSemesterData($request)
    {
        //TODO::GNG-2479
        //TODO::GNG-2481
        $updatedData = $request->input('models');
        $decodedData = json_decode($updatedData, true);
        $primaryId = $decodedData[0]['id'];

        $startDateRaw = $decodedData[0]['start_date'] ?? null;
        $finishDateRaw = $decodedData[0]['finish_date'] ?? null;

        $activityStartDate = (!empty($startDateRaw) && strtotime($startDateRaw)) ? date("Y-m-d", strtotime($startDateRaw)) : "";
        $activityFinishDate = (!empty($finishDateRaw) && strtotime($finishDateRaw)) ? date("Y-m-d", strtotime($finishDateRaw)) : "";
        $lastAssessmentApprovedDate = ((isset($decodedData[0]['last_assessment_approved_date'])) && ($decodedData[0]['last_assessment_approved_date']))?date("Y-m-d", strtotime($decodedData[0]['last_assessment_approved_date'])):'';
        $createdBy = $updatedBy = $request->user()->id;
        $isHigherEd = ($decodedData[0]['is_higher_ed']) ? true : false;
        $finalOutcomeVal = $decodedData[0]['final_outcome'];
        $existData = StudentSubjectEnrolment::find($primaryId);
        $updateDataArr = [
            'activity_start_date'   => $activityStartDate,
            'activity_finish_date'  => $activityFinishDate,
            'last_assessment_approved_date'  => ($lastAssessmentApprovedDate == "")?$existData->last_assessment_approved_date:$lastAssessmentApprovedDate
        ];

        // We only update activity start and end date in inline edit for higherEd..
        // if($isHigherEd){
        //     $collegeId = Auth::user()->college_id;

        //     $arrSubject = (new Subject())->getSubjectlistCollegeIdWise($existData->subject_id);
        //     $objResultGrade = ResultGrade::getGradFromMarks($collegeId,$arrSubject[0]['grading_type'],$decodedData[0]['marks']);
        //     $updateDataArr['final_outcome'] = null;
        //     $updateDataArr['grade'] = $finalOutcomeVal = $objResultGrade['grade'];
        //     $updateDataArr['mark_outcome'] = $objResultGrade['mark_outcome'];
        //     $updateDataArr['marks'] = $decodedData[0]['marks'];
        // }

        if(!$isHigherEd){
            $updateDataArr['final_outcome'] = $finalOutcomeVal;
        }
        try {
            DB::beginTransaction();
            $studentEnrolment = StudentSubjectEnrolment::find($primaryId);

            $checkIsFinalOutComeUpdate = false;
            if (array_key_exists('final_outcome', $updateDataArr) && $studentEnrolment->final_outcome != $updateDataArr['final_outcome']) {
                $checkIsFinalOutComeUpdate = true;
            }
            $isUpdateData = $studentEnrolment->update($updateDataArr);

            // Update the assessment result
            if(($studentEnrolment->final_outcome == 'C' || $studentEnrolment->final_outcome == 'NYC') && $checkIsFinalOutComeUpdate){
                $this->updateStudentAssessment($studentEnrolment);

                if($studentEnrolment->final_outcome == 'C')
                {
                    StudentUnitEnrollment::where([
                        'student_subject_enrollment_id'=>$primaryId
                    ])->update([
                        'competency_date' => date('Y-m-d')
                    ]);
                }

            }


            if($isUpdateData){
                if($existData->activity_start_date != $activityStartDate){
                    $logDataStart = [
                        'student_subject_enrolment_id'  => $primaryId,
                        'start_original_date'           => $existData->activity_start_date,
                        'start_updated_date'            => $activityStartDate,
                        'is_type'                       => 1,
                        'created_by'                    => $createdBy,
                        'updated_by'                    => $updatedBy
                    ];
                    StudentSubjectEnrolmentHistory::create($logDataStart);
                }

                if($existData->activity_finish_date != $activityFinishDate){
                    $logDataStart = [
                        'student_subject_enrolment_id'  => $primaryId,
                        'end_original_date'             => $existData->activity_finish_date,
                        'end_updated_date'              => $activityFinishDate,
                        'is_type'                       => 2,
                        'created_by'                    => $createdBy,
                        'updated_by'                    => $updatedBy
                    ];
                    StudentSubjectEnrolmentHistory::create($logDataStart);
                }

                if($isHigherEd){
                    if($existData->grade != $finalOutcomeVal){
                        $logDataStart = [
                            'student_subject_enrolment_id'  => $primaryId,
                            'final_outcome_original'        => $existData->grade,
                            'final_outcome_updated'         => $finalOutcomeVal,
                            'is_type'                       => 3,
                            'created_by'                    => $createdBy,
                            'updated_by'                    => $updatedBy
                        ];
                        StudentSubjectEnrolmentHistory::create($logDataStart);
                    }
                }else{
                    if($existData->final_outcome != $finalOutcomeVal){
                        $logDataStart = [
                            'student_subject_enrolment_id'  => $primaryId,
                            'final_outcome_original'        => $existData->final_outcome,
                            'final_outcome_updated'         => $finalOutcomeVal,
                            'is_type'                       => 3,
                            'created_by'                    => $createdBy,
                            'updated_by'                    => $updatedBy
                        ];
                        StudentSubjectEnrolmentHistory::create($logDataStart);
                    }
                }
            }
            DB::commit();
            return $updateDataArr;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }

    }

    public function updateStudentAssessment($studentEnrolment){

        // Check Unit have assign to assessment
        $doesAssessmentUpdates = AssignedAssessmentTask::join('rto_assessment_tasks', 'rto_assessment_tasks.id', '=', 'rto_assigned_assessment_task.assessment_task_id')
                ->leftJoin('rto_assigned_assessment_tasks_units', 'rto_assigned_assessment_tasks_units.assign_assessment_task_id', '=', 'rto_assigned_assessment_task.id')
                ->where([
                    'rto_assigned_assessment_tasks_units.unit_id'=>$studentEnrolment->unit_id,
                    ])
                ->where('rto_assigned_assessment_task.college_id', '=', $studentEnrolment->college_id)
                ->where('rto_assigned_assessment_task.subject_id', '=', $studentEnrolment->subject_id)
                ->where('rto_assigned_assessment_task.semester_id', '=', $studentEnrolment->semester_id)
                ->where('rto_assigned_assessment_task.term', '=', $studentEnrolment->term)
                ->where('rto_assigned_assessment_task.batch', '=', $studentEnrolment->batch)
                ->where('rto_assessment_tasks.is_active', '=', '1')
                ->get([
                    'rto_assigned_assessment_task.assessment_task_id',
                    'rto_assigned_assessment_task.course_type_id',
                    'rto_assigned_assessment_task.year',
                    'rto_assessment_tasks.task_name'
                ]);

        if(count($doesAssessmentUpdates->toArray())){

            foreach($doesAssessmentUpdates as $doesAssessmentUpdate){
                $checkIsTaskLocked = StudentAssignedAssessmentTask::where(
                    [
                        // Conditions to find the record
                        'student_id' => $studentEnrolment->student_id,
                        'subject_id' => $studentEnrolment->subject_id,
                        'batch' => $studentEnrolment->batch,
                        'semester_id' => $studentEnrolment->semester_id,
                        'assessment_task_id' => $doesAssessmentUpdate->assessment_task_id,
                        'term' => $studentEnrolment->term
                    ])->get();
                if(isset($checkIsTaskLocked[0]) && $checkIsTaskLocked[0]->is_approved){
                    return;
                }
                StudentAssignedAssessmentTask::updateOrCreate(
                    [
                        // Conditions to find the record
                        'student_id' => $studentEnrolment->student_id,
                        'subject_id' => $studentEnrolment->subject_id,
                        'batch' => $studentEnrolment->batch,
                        'semester_id' => $studentEnrolment->semester_id,
                        'assessment_task_id' => $doesAssessmentUpdate->assessment_task_id,
                        'term' => $studentEnrolment->term
                    ],
                    [
                        // Fields to update or create
                        'student_id' => $studentEnrolment->student_id,
                        'subject_id' => $studentEnrolment->subject_id,
                        'batch' => $studentEnrolment->batch,
                        'semester_id' => $studentEnrolment->semester_id,
                        'term' => $studentEnrolment->term,
                        'competency' => ($studentEnrolment->final_outcome == 'C') ? 'S' : 'NYS',
                        'college_id' => $studentEnrolment->college_id,
                        'course_id' => $studentEnrolment->course_id,
                        'assessment_task_id' => $doesAssessmentUpdate->assessment_task_id,
                        'subject_attempts' => $studentEnrolment->subject_attempt,
                        'course_type_id' => $doesAssessmentUpdate->course_type_id,
                        'year' => $doesAssessmentUpdate->year,
                    ]
                );
            }

        }

    }
    public function deleteStudentResultSemesterData($request){
        try {
            DB::beginTransaction();
            $data = StudentSubjectEnrolment::find($request['id']);
            StudentAssignedAssessmentTask::where('student_id',$data['student_id'])->where('subject_id',$data['subject_id'])->delete();
            $result = StudentSubjectEnrolment::where('id',$request['id'])->delete();
            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }
    public function saveTcsiStudentUnitEnrollmentFormData(AddTcsiStudentUnitEnrollmentInformation $request){
        try {
            DB::beginTransaction();
            $result = [];
            $unitIds = $request->unitIds ?? [];
            if (!empty($unitIds)) {
                $i = 0;
                foreach ($unitIds as $uid) {
                    $result[] = $this->saveTcsiDataForUnit($uid, (array) $request);
                    $i++;
                }
            } else {
                $result[] = $this->saveTcsiDataForUnit($request->stud_subject_enroll_id, (array) $request);
            }
            DB::commit();
            $result['message'] = 'TCSI values updated successfully for ' . $i . ' units';
            $result['status'] = 'success';
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    private function saveTcsiDataForUnit($uid, $request) {
        $unitDetail = StudentSubjectEnrolment::find($uid);
        $request['stud_subject_enroll_id'] = $uid;
        $request['subject_id'] = $unitDetail['subject_id'];
        $request['course_id'] = $unitDetail['course_id'];
        $result = TcsiStudentUnitEnrollment::firstOrNew(['stud_subject_enroll_id' => $uid]);
        $result->fill($request)->save();

        return $result;
    }
    public function getTcsiStudentUnitEnrollmentData($request){
        try {
            DB::beginTransaction();
            $result = TcsiStudentUnitEnrollment::where('stud_subject_enroll_id',$request['id'])->first();
            $resultData = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->join('rto_result_grade as rrg', 'rrg.id', '=', 'rsse.mark_outcome')
                ->select(['rrg.unit_of_study_status_code'])
                ->where('rsse.id',$request['id'])
                ->first();

            if($result){
                $result['unit_of_study_status_code'] = $resultData->unit_of_study_status_code ?? '';
            }

            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }
    public function getTcsiStudentUnitEnrollmentFormDropdownListData($request){

        $dataArray['modeOfAttendance']['data'] = $this->convertConstantsFormat(Config::get('constants.modeOfAttendance'));
        $dataArray['arrRemissionReasonCode']['data'] = $this->convertConstantsFormat(Config::get('constants.arrRemissionReasonCode'));
        $dataArray['arrStudStatusCode']['data'] = $this->convertConstantsFormat(Config::get('constants.arrStudStatusCode'));
        $dataArray['arrWorkExpIndustryCode']['data'] = $this->convertConstantsFormat(Config::get('constants.arrWorkExpIndustryCode'));
        $dataArray['arrUnitOfStudyStatus']['data'] = $this->convertConstantsFormat(Config::get('constants.arrUnitOfStudyStatus'));
        return $dataArray;

    }
    public function  getStudentSubjectOutcomeAndAvetmissData($request){
        return StudentSubjectEnrolment::where('id',$request['id'])->first();
    }
    public function getStudentOutcomeFormDropdownListData($request){
        $collegeId = Auth::user()->college_id;

        //TODO::GNG-2481
        $isHigherEd = ($request['is_higher_ed'] == 'true') ? true : false;
        if($isHigherEd){
            $markOutcomeArr = $this->getResultGradeList($collegeId);
            $outcomeDataArr = $this->convertConstantsFormat($markOutcomeArr);
        }else{
            $outcomeDataArr = $this->convertConstantsFormat(Config::get('constants.arrSelectFinalOutcomeNew'));
        }
        $dataArray['subjectUnitNameDetail'] = StudentSubjectEnrolment::select(
                                                                        DB::raw("CONCAT(rsu.unit_code,':',rsu.unit_name) as unit_name"),
                                                                        DB::raw("CONCAT(rs.subject_code,':',rs.subject_name) as subject_name"))
                                                                        ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rto_student_subject_enrolment.unit_id')
                                                                        ->leftjoin('rto_subject as rs', 'rs.id', '=', 'rsu.subject_id')
                                                                        ->where('rto_student_subject_enrolment.id',$request['id'])
                                                                        ->get()->toArray();
        $arrTerm = StudentSubjectEnrolment::where('college_id', '=', $collegeId)->where('id', '=', $request['id'])->select('term as Id', 'term as Name')->get();
        $studentCourseData = StudentCourses::find($request['student_course_id']);
        $startYear =  date('Y', strtotime($studentCourseData->start_date));
        $endYear =  date('Y', strtotime($studentCourseData->finish_date));
        $arrSemester = Semester::whereBetween('year', [$startYear, $endYear])->get(['id as Id', 'semester_name as Name'])->toArray();


        //get batch based on subject and semester_id
        $arrBatch = Timetable::where('college_id', '=', $collegeId)->where('subject_id', '=', $request['subject_id'])->where('semester_id', '=', $request['semester_id'])->select('batch as Id', 'batch as Name')->groupBy('Name')->get()->toArray();

        $studentDetails = StudentCourses::join('rto_students as rs', 'rs.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_student_courses.id',$request['student_course_id'])
            ->get(['rto_courses.fee_help',DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as full_name"),'rto_campus.name as campus_name','rs.generated_stud_id','rto_student_courses.status', DB::raw("DATE_FORMAT(rto_student_courses.start_date, '%d-%m-%Y') AS start_date"),DB::raw("DATE_FORMAT(rto_student_courses.finish_date, '%d-%m-%Y') AS finish_date")])->toArray();

        $venaudata =     $this->studentResultTabRepository->getEnrollVenueLocation($request);

        $dataArray['studentDetails'] = !empty($studentDetails[0]) ?  $studentDetails[0]:"";
        $dataArray['arrSemester']['data'] = $arrSemester;
        $dataArray['arrStudyreasonData']['data'] = StudyReason::select('title as Name','avaitmiss_id as Id')->get()->toArray();
        $dataArray['arrFundingSource']['data'] = $this->convertConstantsFormat(Config::get('constants.arrFundingSource'));
        $dataArray['arrFundingSourceNat']['data'] =  $this->convertConstantsFormat(Config::get('constants.arrFundingSourceNat'));
        $dataArray['arrSelectFinalOutcome']['data'] = $outcomeDataArr;
        $dataArray['arrTerm']['data'] = $arrTerm;
        $dataArray['arrBatch']['data'] = $arrBatch;
        $dataArray['arrVenau']['data'] = $venaudata;
        $dataArray['arrRemissionReasonCode']['data'] = $this->convertConstantsFormat(Config::get('constants.arrRemissionReasonCode'));
        $dataArray['arrStudStatusCode']['data'] = $this->convertConstantsFormat(Config::get('constants.arrStudStatusCode'));

        return $dataArray;
    }
    public function saveSubjectOutcomeFormData(AddStudentSubjectOutcome $request, $isHigherEd)
    {
        try {
            DB::beginTransaction();
            $result = $this->saveSubjectOutcome($request->stud_subject_enroll_id, (array) $request, $isHigherEd);
            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }
    public function getTransferToAnotherCourseFormDropdownListData($request){
        $dataArray['subjectUnitNameDetail'] = StudentSubjectEnrolment::select(
            DB::raw("CONCAT(rsu.unit_code,':',rsu.unit_name) as unit_name"),
            DB::raw("CONCAT(rs.subject_code,':',rs.subject_name) as subject_name"))
            ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rto_student_subject_enrolment.unit_id')
            ->leftjoin('rto_subject as rs', 'rs.id', '=', 'rsu.subject_id')
            ->where('rto_student_subject_enrolment.id',$request['id'])
            ->get()->toArray();
        $dataArray['arrTransferToCourse']['data'] = $this->convertConstantsFormat( StudentCourses::getStudentAppliedCourseNameWithStudentCourseId($request['student_id']));
        return $dataArray;
    }
    public function getStudentCourseInfoData($request){
        $data = StudentCourses::where('id',$request['student_course_id'])->first();
        return $data;
    }
    public function transferResultToAnotherCourseData($value)
    {
        try {
            DB::beginTransaction();
            // dd($data);
            // foreach($data as $value){
              $studentEnrolment =  StudentSubjectEnrolment::where('id',$value->id)->first();

              // Get course Id from student course becasue one unit can have in multipal course
              $getCourseId = StudentCourses::find($value->transfer_to_student_course_id);

              $subjectEnrollment = StudentSubjectEnrolment::updateOrCreate(
                    [
                        'student_id' => $studentEnrolment->student_id,
                        'subject_id' => $studentEnrolment->subject_id,
                        'course_id' => $getCourseId->course_id,
                        'unit_id' => $studentEnrolment->unit_id,
                        'subject_attempt' => '2'
                    ],
                    [
                        // Fields to update or create
                        'college_id' => $studentEnrolment->college_id,
                        'student_id' => $studentEnrolment->student_id,
                        'subject_id' => $studentEnrolment->subject_id,
                        'course_id' => $getCourseId->course_id,
                        'student_course_id' => $value->transfer_to_student_course_id,
                        'unit_id' => $studentEnrolment->unit_id,
                        'vanue_location' => $studentEnrolment->vanue_location,
                        'subject_attempt' => '2',
                        'is_result_lock' => '0',
                        'activity_start_date' => date('Y-m-d', strtotime($value->activity_start_date)),
                        'activity_finish_date' => date('Y-m-d', strtotime($value->activity_finish_date)),
                        'last_assessment_approved_date' => date('Y-m-d', strtotime($value->last_assessment_approved_date)),
                        'final_outcome' => ($studentEnrolment->final_outcome == 'C' || $studentEnrolment->final_outcome == 'CT' || $studentEnrolment->final_outcome == 'RPL')?'CT':'Enrolled',
                        'created_by' => Auth::user()->id,
                        'updated_by' => Auth::user()->id
                    ]
                );

                StudentUnitEnrollment::updateOrCreate(
                    [
                        'student_subject_enrollment_id' => $subjectEnrollment->id
                    ],
                    [
                        // Fields to update or create
                        'college_id' => $studentEnrolment->college_id,
                        'unit_id' => $studentEnrolment->unit_id,
                        'student_subject_enrollment_id' => $subjectEnrollment->id,
                        'study_from' => date('Y-m-d', strtotime($value->activity_start_date)),
                        'study_to' => date('Y-m-d', strtotime($value->activity_finish_date)),
                        'created_by' => Auth::user()->id,
                        'updated_by' => Auth::user()->id
                    ]
                );
            // }
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }
    public function addUnitOnCourseStatusChange($data, $isHigherEd)
    {
        try {
            DB::beginTransaction();

            foreach($data as $value){
              $studentEnrolment =  StudentSubjectEnrolment::where('id',$value->id)->first();

              // Get course Id from student course becasue one unit can have in multipal course
              $getCourseId = StudentCourses::find($value->student_course_id);

              $subjectEnrollment = StudentSubjectEnrolment::updateOrCreate(
                    [
                        'student_id' => $studentEnrolment->student_id,
                        'subject_id' => $studentEnrolment->subject_id,
                        'course_id' => $getCourseId->course_id,
                        'unit_id' => $studentEnrolment->unit_id,
                        'subject_attempt' => '2'
                    ],
                    [
                        // Fields to update or create
                        'college_id' => $studentEnrolment->college_id,
                        'student_id' => $studentEnrolment->student_id,
                        'subject_id' => $studentEnrolment->subject_id,
                        'course_id' => $getCourseId->course_id,
                        'student_course_id' => $value->student_course_id,
                        'unit_id' => $studentEnrolment->unit_id,
                        'vanue_location' => $studentEnrolment->vanue_location,
                        'subject_attempt' => '2',
                        'is_result_lock' => '0',
                        'activity_start_date' => $value->start_date,
                        'activity_finish_date' => $value->finish_date,
                        'last_assessment_approved_date' => $value->finish_date,
                        'final_outcome' => ($studentEnrolment->final_outcome == 'C' || $studentEnrolment->final_outcome == 'CT' || $studentEnrolment->final_outcome == 'RPL')?'CT':'Enrolled',
                        'created_by' => Auth::user()->id,
                        'updated_by' => Auth::user()->id
                    ]
                );

                StudentUnitEnrollment::updateOrCreate(
                    [
                        'student_subject_enrollment_id' => $subjectEnrollment->id
                    ],
                    [
                        // Fields to update or create
                        'college_id' => $studentEnrolment->college_id,
                        'unit_id' => $studentEnrolment->unit_id,
                        'student_subject_enrollment_id' => $subjectEnrollment->id,
                        'study_from' => $value->start_date,
                        'study_to' => $value->finish_date,
                        'created_by' => Auth::user()->id,
                        'updated_by' => Auth::user()->id
                    ]
                );
            }
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }
    private function saveSubjectOutcome($uid, $request, $isHigherEd=false)
    {
        $unitDetail = StudentSubjectEnrolment::find($uid);
        $request['stud_subject_enroll_id'] = $uid;
        $request['subject_id'] = $unitDetail['subject_id'];
        $request['course_id'] = $unitDetail['course_id'];
        $request['vanue_location'] = $request['venue_location'];

        //TODO::GNG-2481
        $isOutcomeData = false;
        if(isset($request['final_outcome'])){
            $isOutcomeData = true;
            $createdBy = $updatedBy = Auth::user()->id;
            $finalOutcomeVal = $request['final_outcome'];
            if($isHigherEd){
                $outcomeVal = $request['final_outcome'];
                $gradeData = ResultGrade::find($outcomeVal);
                $request['final_outcome'] = null;
                $request['mark_outcome']  = $outcomeVal;
                $request['grade']         = ($gradeData) ? $gradeData->grade : null;
            }
        }



        $attempt = StudentSubjectEnrolment::where([
            'student_id' => $unitDetail['student_id'],
            'course_id' => $unitDetail['course_id'],
            'subject_id' => $unitDetail['subject_id'],
            'unit_id' => $unitDetail['unit_id'],
            'subject_attempt' => ($unitDetail['subject_attempt'] - 1),
        ])->orderBy('subject_attempt','desc')->first();
        if(!empty($attempt)){
            if($attempt['activity_finish_date'] > $request['activity_start_date']){
                return ['status'=>'error', 'message'=>'Attempt '.($unitDetail['subject_attempt']).' start date is must be after the previous attempt end date'];
             }
        }
        $result = StudentSubjectEnrolment::firstOrNew(['id' => $uid]);
        $result->fill($request)->save();

        if($result->final_outcome == 'C' || $result->final_outcome == 'NYC'){
            $this->updateStudentAssessment($result);
        }

        //TODO::GNG-2481
        //Manage log here
        if($isOutcomeData){
            if($isHigherEd){
                if($unitDetail->grade != $request['grade']){
                    StudentSubjectEnrolmentHistory::create([
                        'student_subject_enrolment_id'  => $uid,
                        'final_outcome_original'        => $unitDetail->grade,
                        'final_outcome_updated'         => $request['grade'],
                        'is_type'                       => 3,
                        'created_by'                    => $createdBy,
                        'updated_by'                    => $updatedBy
                    ]);
                }
            }else{
                if($unitDetail->final_outcome != $finalOutcomeVal){
                    StudentSubjectEnrolmentHistory::create([
                        'student_subject_enrolment_id'  => $uid,
                        'final_outcome_original'        => $unitDetail->final_outcome,
                        'final_outcome_updated'         => $finalOutcomeVal,
                        'is_type'                       => 3,
                        'created_by'                    => $createdBy,
                        'updated_by'                    => $updatedBy
                    ]);
                }
            }
        }
        // If batch is changed delete old attrendance data
        $batch = (isset($request['batch'])) ? $request['batch'] : null;
        //TODO::GNG-2189 same as GNG-1711
        if(!empty($batch)){

            if($unitDetail->batch != $batch){
                $timetableData = TimetableDetail::from('rto_timetable_detail as rtd')
                    ->join('rto_timetable as rt', 'rt.id', '=', 'rtd.timetable_id')
                    ->where('rt.batch', $unitDetail->batch)
                    ->pluck('rtd.id')
                    ->toArray();

                    StudentAttendance::where('student_id', $request['student_id'])
                    ->whereIn('timetable_detail_id', $timetableData)
                    ->delete();

            }
            event(new \App\Events\EnrollStudentAttendanceEvent($request['student_id'], [$batch]));
        }
        $result['message'] = 'Subject Outcome updated successfully';
        $result['status'] = 'success';
        return $result;
    }
    public function getStudentUnitOutcomeData($request){
        $columnArr = array(
            'rsse.final_outcome',
            'rsue.study_from',
            'rsue.study_to',
            'rsue.schedule_hours',
            'rsue.tution_fee',
            'rsue.amount_paid',
            'rsue.attended_hour',
            'rsue.competency_date',
            'rsse.id',
            'rsse.is_result_lock',
            'rsse.mark_outcome',
            'rsse.last_assessment_approved_date',
            'rsse.activity_finish_date',
            'rsse.marks',
            'rsse.grade',
            'rsse.delivery_mode',
            'rsse.comment',
            'rsse.course_id',
        );

        $result =  StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            ->leftjoin('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'rsse.id')
            ->where('rsse.id',$request['id'])
            ->select($columnArr)
            ->first()
            ->toArray();
        $courseData = StudentCourses::where('course_id', $result['course_id'])->get(['course_type_id'])->first()->toArray();
        $course_type_id = $courseData['course_type_id'];
        $ishigherEd = (new CourseType)->checkHigherEdGradingType($course_type_id);
        $result['final_outcome'] = ($ishigherEd) ? $result['mark_outcome']:$result['final_outcome'];
        $result['competency_date'] = ($result['competency_date'] != '') ? $result['competency_date']:(($result['last_assessment_approved_date'] != '')?$result['last_assessment_approved_date']:(($result['activity_finish_date'] !='')?$result['activity_finish_date']:''));
        return $result;
    }
    public function getUnitOutcomeFormDropdownListData($request)
    {
        //TODO::GNG-2481
        $isHigherEd = ($request['is_higher_ed'] == 'true') ? true : false;
        if($isHigherEd){
            $collegeId = Auth::user()->college_id;
            $outcomeArr = $this->getResultGradeList($collegeId, false);
        }else{
            $outcomeArr = Config::get('constants.arrSelectFinalOutcome');
        }

        $dataArray['arrDeliveryMode']['data'] = $this->convertConstantsFormat(Config::get('constants.arrDeliveryMode'));
        $dataArray['arrSelectFinalOutcome']['data'] = $this->convertConstantsFormat($outcomeArr);
        $dataArray['isHigherEd'] = $isHigherEd;

        return $dataArray;
    }
    public function getResultGradeList($collegeId, $pluck=false)
    {
        if($pluck){
            return ResultGrade::where('college_id', $collegeId)->pluck('grade', 'grade')->all();
        }else{
            $resArr = ResultGrade::where('college_id', $collegeId)->get();
            $listData = [];
            foreach ($resArr as $res) {
                $listData[$res->id] = $res->grade;
            }
            return $listData;
        }

    }

    public function saveUnitOutcomeFormData(AddStudentUnitOutcome  $request){
        $postArray = (array) $request;
        $courseData = StudentCourses::where('course_id', $postArray['course_id'])->get(['course_type_id'])->first()->toArray();
        $course_type_id = $courseData['course_type_id'];
        $ishigherEd = (new CourseType)->checkHigherEdGradingType($course_type_id);
        try {
            DB::beginTransaction();
            $result = [];
            $result[] = $this->saveUnitOutcome($postArray['stud_subject_enroll_id'], $postArray,$ishigherEd);
            DB::commit();
            $result['message'] = 'Unit Outcome updated successfully';
            $result['status'] = 'success';
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    private function saveUnitOutcome($uid, $request,$isHigherEd) {
        $request['final_outcome'] = $request['compentency'];

        if(isset($request['final_outcome'])){
            $isOutcomeData = true;
            $createdBy = $updatedBy = Auth::user()->id;
            $finalOutcomeVal = $request['final_outcome'];
            if($isHigherEd){

                if($request['marks']){
                    $arrSubject = (new Subject())->getSubjectlistCollegeIdWise($request['subject_id']);
                    $objResultGrade = ResultGrade::getGradFromMarks($request['college_id'],$arrSubject[0]['grading_type'],$request['marks']);
                    $request['final_outcome'] = null;
                    $request['grade'] = $objResultGrade['grade'];
                    $request['mark_outcome'] = $objResultGrade['mark_outcome'];
                }

            }
        }

        $result2 = StudentUnitEnrollment::firstOrNew(['student_subject_enrollment_id' => $uid]);
        $result2->fill($request)->save();
        unset($request['stud_subject_enroll_id']);
        $result = StudentSubjectEnrolment::firstOrNew(['id' => $uid]);
        $result->fill($request)->save();

        if($result->final_outcome == 'C' || $result->final_outcome == 'NYC'){
            $this->updateStudentAssessment($result);
        }

        return $result;
    }

    public function  getAvetmissFormDropdownListData($request){

        $dataArray['arrPreDeliveryMode']['data'] = $this->convertConstantsFormat(Config::get('constants.arrPreDeliveryMode'));
        $dataArray['arrDeliveryMode']['data'] = $this->convertConstantsFormat(Config::get('constants.arrDeliveryMode'));
        $dataArray['arrFeeExemptionType']['data'] = $this->convertConstantsFormat(Config::get('constants.arrFeeExemptionType'));
        $dataArray['arrVETInSchoolFlag']['data'] = $this->convertConstantsFormat(Config::get('constants.arrVETInSchoolFlag'));
        $dataArray['arrSpecificFunding']['data'] = $this->convertConstantsFormat(Config::get('constants.arrSpecificFunding'));
        $dataArray['arrCourseCommencing']['data'] = $this->convertConstantsFormat(Config::get('constants.arrCourseCommencing'));
        $dataArray['arrFundingSourceNat']['data'] = $this->convertConstantsFormat(Config::get('constants.arrFundingSourceNat'));
        $dataArray['arrFundingSource']['data'] = $this->convertConstantsFormat(Config::get('constants.arrFundingSource'));
        $dataArray['arrStudyreasonData']['data'] = StudyReason::select('title as Name','avaitmiss_id as Id')->get()->toArray();

        $objStudentCourse = StudentCourses::where('id',$request['student_course_id'])->first();
        $dataArray['defaultSelected'] = array(
            'delivery_mode' => $objStudentCourse->internal.$objStudentCourse->external.$objStudentCourse->workplace_based_delivery
        );

        $arrPredominantDeliveryMode = Config::get('constants.arrPredominantDeliveryMode');
        $data=[];
        if(!empty($request['delivery_mode'])){
            foreach ($arrPredominantDeliveryMode as $key => $resArr) {
                if ($key === $request['delivery_mode']){
                    $data = [];
                    if($resArr){
                        //unset($resArr['']);
                        foreach ($resArr as $key => $value) {
                            $data[] = [
                                'Id' =>  $key ,
                                'Name' => $value
                            ];
                        }
                    }
                }
            }
        }else{

            foreach ($arrPredominantDeliveryMode as $key => $resArr) {

                if ($key === $objStudentCourse->internal.$objStudentCourse->external.$objStudentCourse->workplace_based_delivery){
                    $data = [];
                    if($resArr){
                        //unset($resArr['']);
                        foreach ($resArr as $key => $value) {
                            $data[] = [
                                'Id' =>  $key ,
                                'Name' => $value
                            ];
                        }
                    }
                }
            }
        }
        $dataArray['arrPredominantDeliveryMode']['data'] = $data;

        return $dataArray;
    }

    public function saveAvitmessFormData(AddStudentUnitAvetimissValue $request, $isHigherEd){
        try {
            DB::beginTransaction();
            $result = [];
            $unitIds = $request->unitIds ?? [];
            $i = 1;
            if (!empty($unitIds)) {
                foreach ($unitIds as $uid) {
                    $result[] = $this->saveSubjectOutcome($uid, (array) $request, $isHigherEd);
                    $i++;
                }
            } else {
                $result[] = $this->saveSubjectOutcome($request->stud_subject_enroll_id, (array) $request, $isHigherEd);
            }
            DB::commit();
            $result['message'] = 'Avetmiss Outcome updated successfully for ' . $i . ' units';
            $result['status'] = 'success';
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function getSelectedUnitSubjectData($request,$countOnly=false)
    {
        $finalOutcomeArr = Config::get('constants.arrSelectFinalOutcome');
        $finalOutcomeArr[''] = 'N/A';
        $finalOutcomeArr[0] = 'N/A';
        $post = ($request->input()) ? $request->input() : [];

        //TODO::GNG-2481
        $isHigherEd = ($post['is_higher_ed'] == 'true') ? true : false;
        if($isHigherEd){
            $collegeId = Auth::user()->college_id;
            $markOutcomeArr = $this->getResultGradeList($collegeId);
        }

        $columnArr = array(
            DB::raw("CONCAT('', rto_subject.subject_code, ':',rto_subject.subject_name) as subject_name"),
            'rsse.final_outcome as final_outcome',
            'rsse.semester_id',
            'rsse.unit_id',
            'rsu.unit_code',
            'rsse.subject_id',
            'rsse.student_id',
            'rsse.course_id',
            'rsse.activity_start_date as start_date',
            'rsse.activity_finish_date as finish_date',
            'rsue.study_from as start_date_unit',
            'rsue.study_to as finish_date_unit',
            'rsse.id',
            'rsse.mark_outcome',
            'rsse.grade',
            'rto_users.name as updated_by',
            'rsu.unit_name',
        );

        $query = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rsse.subject_id')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'rsse.updated_by')
            ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rsse.unit_id')
            ->leftjoin('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'rsse.id')
            ->whereIn('rsse.id',$post['unitIds'])
            ->select($columnArr)
            ->GroupBy('rsse.id');


        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPaginationV2($query, $post, $countOnly);

        if(!$countOnly) {
            foreach ($result['data'] as $k => $row) {
                $result['data'][$k]['SN'] = ($k+1);
                //$result[$k]['final_outcome'] = $finalOutcomeArr[$row['final_outcome']];
                //$result[$k]['final_outcome1'] = $finalOutcomeArr[$row['final_outcome']];
                //TODO::GNG-2481
                if ($isHigherEd) {
                    $finalOutcomeVal = isset($markOutcomeArr[$row['mark_outcome']]) ? $markOutcomeArr[$row['mark_outcome']] : "-";
                } else {
                    $finalOutcomeVal = $row['final_outcome'];
                }
                $result['data'][$k]['start_date'] = Helpers::convertDateToReadableFormat($row['start_date_unit']);          //date('d M Y', strtotime($row['start_date_unit']));
                $result['data'][$k]['finish_date'] = Helpers::convertDateToReadableFormat($row['finish_date_unit']);        //date('d-M Y', strtotime($row['finish_date_unit']));
                $result['data'][$k]['start_date_unit'] = Helpers::convertDateToReadableFormat($row['start_date_unit']);     //date(Config::get('app.dateFormatFrontSidePHP') , strtotime($row['start_date_unit']));
                $result['data'][$k]['finish_date_unit'] = Helpers::convertDateToReadableFormat($row['finish_date_unit']);   //date(Config::get('app.dateFormatFrontSidePHP') , strtotime($row['finish_date_unit']));
                $result['data'][$k]['final_outcome'] = $finalOutcomeVal;
                $result['data'][$k]['final_outcome1'] = $finalOutcomeVal;
            }
        }
        return $result;
    }
    public function  getStudentDetailsData($request){
        $sqlDateFormat = Helpers::toMysqlDateFormat();
        return StudentCourses::join('rto_students as rs', 'rs.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_student_courses.id',$request['student_course_id'])
            ->select([
                DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as full_name"),
                'rto_campus.name as campus_name',
                'rs.generated_stud_id',
                'rto_student_courses.status',
                DB::raw("DATE_FORMAT(rto_student_courses.start_date, '$sqlDateFormat') AS start_date"),
                DB::raw("DATE_FORMAT(rto_student_courses.finish_date, '$sqlDateFormat') AS finish_date")
            ])
            ->get()
            ->toArray();

    }
    public function getUnitDetailData($request){
        $post = ($request->input()) ? $request->input() : [];
        $finalOutcomeArr = Config::get('constants.arrSelectFinalOutcome');
        $finalOutcomeArr[''] = 'N/A';
        $finalOutcomeArr[0] = 'N/A';

        $columnArr = array(
            'rsse.subject_id',
            'rsse.student_id',
            'rsse.course_id',
            'rsse.unit_id',
            'rsu.unit_code',
            'rsu.unit_name',
            'rsse.batch',
            'rto_result_grade.grade',
            DB::raw("CONCAT(rto_semester.semester_name,'(Term', rsse.term, ')') as semester_term"),
            'rsse.activity_start_date as course_activity_date',
            'rsse.activity_finish_date as actual_end_date',
            'rsse.final_outcome as final_outcome',
            'rsse.activity_start_date as start_date',
            'rsse.activity_finish_date as finish_date',
            DB::raw("CONCAT(DATE_FORMAT(rsse.activity_start_date, '%d %b %Y')) as start_date_detail"),
            DB::raw("CONCAT(DATE_FORMAT(rsse.activity_finish_date, '%d %b %Y')) as finish_date_detail"),
            'rto_users.name as user_name',
            'rsse.updated_at',
            DB::raw("CONCAT(rst.first_name,' ', rst.last_name) as teacher_name"),
            DB::raw("CONCAT(rto_students.first_name,' ', rto_students.family_name) as student_name"),
            'rto_timetable.teacher_id',
            'rsse.id'
        );

        return StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
        ->leftjoin('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'rsse.id')
        ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rsse.unit_id')
        ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rsse.semester_id')
        ->leftjoin('rto_result_grade', 'rto_result_grade.id', '=', 'rsse.mark_outcome')
        ->leftjoin('rto_students', 'rto_students.id', '=', 'rsse.student_id')
        ->leftjoin('rto_users', 'rto_users.id', '=', 'rsse.created_by')
        ->leftjoin('rto_timetable', function($join){
            $join->on('rto_timetable.subject_id', '=', 'rsse.subject_id');
            $join->on('rto_timetable.batch','=', 'rsse.batch');
        })
        ->leftjoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rto_timetable.teacher_id')
        ->where('rsse.id',$post['id'])
        ->select($columnArr)
        ->get()->toArray();
    }

    public function getAssessmentDetailsData($request, $isMoodleConnect=false)
    {
        $sqlDateFormat = Helpers::toMysqlDateFormat();
        $isHigherEd = StudentCourses::checkCourseIsHigherEd($request['student_course_id']);
        $finalOutcomeArr = Config::get('constants.arrSelectFinalOutcome');
        $finalOutcomeArr[''] = 'N/A';
        $finalOutcomeArr[0] = 'N/A';
        $post = $request ?? [];
        $post['take'] = $post['take'] ?? 20;
        // $query = StudentAssignedAssessmentTask::join('rto_assessment_tasks as rst', 'rst.id', '=', 'rto_student_assigned_assessment_tasks.assessment_task_id')
        //     ->join('rto_assigned_assessment_task as raat', 'rto_student_assigned_assessment_tasks.assessment_task_id', '=', 'raat.assessment_task_id')
        //     ->leftjoin('rto_users as ru', 'ru.id', '=', 'rst.updated_by')
        //     ->where([
        //         'rto_student_assigned_assessment_tasks.student_id'=>$request['student_id'],
        //         'rto_student_assigned_assessment_tasks.subject_id'=>$request['subject_id'],
        //         'rto_student_assigned_assessment_tasks.course_id'=>$request['course_id'],
        //         'raat.batch'=>$request['batch']
        //         ])
        //     ->select('ru.name as updated_by_name',DB::raw('DATE_FORMAT(raat.updated_at, "%d-%b-%Y") as uploaded_date'),'rto_student_assigned_assessment_tasks.*','rst.task_name','rst.final_outcome','raat.due_date')
        //     ->groupBy('rto_student_assigned_assessment_tasks.assessment_task_id');

        $query = AssignedAssessmentTask::leftJoin('rto_student_assigned_assessment_tasks as rsaat', function ($join) use ($request) {
            $join->on('rsaat.assessment_task_id', '=', 'rto_assigned_assessment_task.assessment_task_id')
                 ->where([
                     'rsaat.student_id' => $request['student_id'],
                     'rsaat.subject_id' => $request['subject_id'],
                     'rsaat.course_id' => $request['course_id'],
                     'rsaat.batch' => $request['batch'],
                 ]);
        })
        ->leftJoin('rto_assessment_tasks as rst', 'rst.id', '=', 'rto_assigned_assessment_task.assessment_task_id')
        ->leftJoin('rto_assigned_assessment_tasks_units', 'rto_assigned_assessment_tasks_units.assign_assessment_task_id', '=', 'rto_assigned_assessment_task.id')
        ->leftJoin('rto_users as ru', 'ru.id', '=', 'rst.updated_by')
        ->select(
            'ru.name as updated_by_name',
            DB::raw("DATE_FORMAT(rto_assigned_assessment_task.updated_at, '$sqlDateFormat') as uploaded_date"),
            'rto_assigned_assessment_task.college_id',
            'rto_assigned_assessment_task.assessment_task_id',
            'rto_assigned_assessment_task.course_type_id',
            'rto_assigned_assessment_task.year',
            'rto_assigned_assessment_task.semester_id',
            'rto_assigned_assessment_task.term',
            'rto_assigned_assessment_task.batch',
            'rto_assigned_assessment_task.subject_id',
            'rto_assigned_assessment_task.id',
            'rsaat.competency',
            'rsaat.marks',
            'rsaat.student_id',
            'rsaat.course_id',
            'rsaat.updated_by',
            'rsaat.created_by',
            'rsaat.updated_at',
            'rsaat.created_at',
            'rsaat.result_comment',
            'rsaat.extra_fee',
            'rsaat.assessment_type',
            'rsaat.is_approved',
            'rsaat.is_locked',
            'rsaat.extended_due_date_reason',
            //'rsaat.extended_due_date',
            DB::raw("DATE_FORMAT(rsaat.extended_due_date, '$sqlDateFormat') as extended_due_date"),
            'rsaat.comments',
            'rst.task_name',
            'rst.id as taskid',
            'rst.final_outcome',
            'rto_assigned_assessment_task.due_date',
            DB::raw("DATE_FORMAT(rto_assigned_assessment_task.due_date, '$sqlDateFormat') as due_date2"),
            'rto_assigned_assessment_tasks_units.unit_id'
        )
        ->where('rto_assigned_assessment_task.batch', $request['batch'])
        ->where('rto_assigned_assessment_task.subject_id', $request['subject_id'])
        ->where('rto_assigned_assessment_tasks_units.unit_id', $request['unit_id'])
        ->groupBy('rto_assigned_assessment_task.assessment_task_id');

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPaginationV2($query, $post);

        foreach ($result['data'] as $k => $row)
        {
            $result['data'][$k]['assessment_task_id'] = ($row['assessment_task_id'])?$row['assessment_task_id']:$row['taskid'];
            $result['data'][$k]['course_id'] = ($row['course_id'])?$row['course_id']:$request['course_id'];
            $result['data'][$k]['student_id'] = ($row['student_id'])?$row['student_id']:$request['student_id'];
            $result['data'][$k]['competency'] = ($row['competency'])?$row['competency']:'-';
            $result['data'][$k]['marks'] = $row['marks'];
            $result['data'][$k]['final_outcome1'] = isset($finalOutcomeArr[$row['final_outcome']]) ? $finalOutcomeArr[$row['final_outcome']]:"-";
            $result['data'][$k]['is_higher_ed'] = $request['is_higher_ed'];
            $result['data'][$k]['moodleData'] = $this->getMoodleDataForAssessment($row, $isMoodleConnect);
        }

        return $result;
    }

    public function getMoodleDataForAssessment($row, $isMoodleConnect)
    {
        if($isMoodleConnect){
            $prefix = 'assessment';
            $whereArr = [
                'assessment_task_id' => $row['assessment_task_id'],
                'unit_id' => $row['unit_id']
            ];
            $assessmentTask = AssessmentTaskUnit::where($whereArr)->first();
            if($assessmentTask){
                $moodleItem = $assessmentTask->moodleItem;
                $failed = (int) ($moodleItem && !empty($moodleItem->sync_failed_at));
                $syncStatus = $failed == 1 ? 'Sync Fail' : ($moodleItem && $moodleItem->synced_at ? 'Synced' : 'Not Sync');
                //$synced = (int) ($moodleItem && empty($moodleItem->sync_failed_at) && !empty($moodleItem->synced_at));

                return [
                    "{$prefix}_moodle_sync_status"    => $syncStatus,
                    "{$prefix}_syncable_id"           => $moodleItem && $moodleItem->syncable_id ? $moodleItem->syncable_id : null,
                    "{$prefix}_sync_item_id"          => $moodleItem && $moodleItem->sync_item_id ? $moodleItem->sync_item_id : null,
                    "{$prefix}_moodle_sync_id"        => data_get($moodleItem, "data.item.id") ?? null,
                    "{$prefix}_moodle_synced_at"      => $moodleItem && $moodleItem->synced_at ? \Carbon\Carbon::parse($moodleItem->synced_at)->diffForHumans() : '',
                    "{$prefix}_moodle_failed_at"      => $moodleItem && $moodleItem->sync_failed_at ? \Carbon\Carbon::parse($moodleItem->sync_failed_at)->diffForHumans() : '',
                    "{$prefix}_moodle_failed_message" => $failed == 1 ? ($moodleItem->sync_failed_message ?? "Something went wrong") : '',
                ];
            }
        }
        return false;
    }

    // public function getAssessmentDetailsData($request, $countOnly=false)
    // {
    //     $isHigherEd = StudentCourses::checkCourseIsHigherEd($request['student_course_id']);
    //     $finalOutcomeArr = Config::get('constants.arrSelectFinalOutcome');
    //     $finalOutcomeArr[''] = 'N/A';
    //     $finalOutcomeArr[0] = 'N/A';
    //     $post = ($request) ? $request:[];
    //     $query = StudentAssignedAssessmentTask::join('rto_assessment_tasks as rst', 'rst.id', '=', 'rto_student_assigned_assessment_tasks.assessment_task_id')
    //         ->join('rto_assigned_assessment_task as raat', 'rto_student_assigned_assessment_tasks.assessment_task_id', '=', 'raat.assessment_task_id')
    //         ->leftjoin('rto_users as ru', 'ru.id', '=', 'rst.updated_by')
    //         ->where([
    //             'rto_student_assigned_assessment_tasks.student_id'=>$request['student_id'],
    //             'rto_student_assigned_assessment_tasks.subject_id'=>$request['subject_id'],
    //             'rto_student_assigned_assessment_tasks.course_id'=>$request['course_id'],
    //             'raat.batch'=>$request['batch']
    //             ])
    //         ->select('ru.name as updated_by_name',DB::raw('DATE_FORMAT(rst.updated_at, "%d-%b-%Y") as uploaded_date'),'rto_student_assigned_assessment_tasks.*','rst.task_name','rst.final_outcome','raat.due_date')
    //         ->groupBy('rto_student_assigned_assessment_tasks.assessment_task_id');

    //     $this->gridDataSorting($query, $post);
    //     $result = $this->gridDataPagination($query, $post, $countOnly);
    //     if(!$countOnly) {
    //         foreach ($result as $k => $row) {

    //             $result[$k]['competency'] = $row['competency'];
    //             $result[$k]['marks'] = $row['marks'];
    //             $result[$k]['final_outcome1'] = isset($finalOutcomeArr[$row['final_outcome']]) ? $finalOutcomeArr[$row['final_outcome']]:"-";
    //             $result[$k]['is_higher_ed'] = $request['is_higher_ed'];
    //         }
    //     }
    //     return $result;
    // }

    public function updateAssessmentDetailsData($request,$process){

        $updatedData = $request['models'];
        $decodedData = json_decode($updatedData, true);

        DB::beginTransaction();
        try {
            $data = $process->run($decodedData);
            $result['status'] = 'success';
            $result['data'] =  $data;
            $result['message'] = "Assessment update successfully.";
            DB::commit();
            return $result;
        }catch(\Exception $e){
            DB::rollBack();
            //safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }
    // public function updateAssessmentDetailsDataOld($request){
    //     $updatedData = $request['models'];
    //     $decodedData = json_decode($updatedData, true);

    //     $isHigherEd = ($decodedData[0]['is_higher_ed'] == 'true') ? true : false;

    //     if($isHigherEd){
    //         $checkMaxMark = AssessmentTask::where('id',$decodedData[0]['assessment_task_id'])->first(['total_marks']);

    //         if($checkMaxMark->total_marks < $decodedData[0]['marks']){
    //             $result['status'] = 'fail';
    //             $result['message'] = 'The maximum marks for the assessment exceed the current mark.';
    //             return $result;
    //         }
    //     }

    //     try {
    //         DB::beginTransaction();

    //         $data['due_date'] = date("Y-m-d", strtotime($decodedData[0]['due_date']));
    //         AssignedAssessmentTask::where('assessment_task_id',$decodedData[0]['assessment_task_id'])->update($data);

    //         $data1['competency'] = $decodedData[0]['competency'];
    //         $data1['marks'] = $decodedData[0]['marks'];

    //         $result['status'] = 'success';
    //         $result['data'] =  StudentAssignedAssessmentTask::where('assessment_task_id',$decodedData[0]['assessment_task_id'])->where('student_id',$decodedData[0]['student_id'])->update($data1);
    //         $result['message'] = "Assessment update successfully.";
    //         DB::commit();
    //         return $result;
    //     } catch (\Exception $e) {
    //         DB::rollBack();
    //         throw new ApplicationException($e->getMessage());
    //     }
    // }
    public function getPredominantDeliveryModeData($request){
        $arrPredominantDeliveryMode = Config::get('constants.arrPredominantDeliveryMode');
        $data=[];
        if(!empty($request->input('delivery_mode'))){
            foreach ($arrPredominantDeliveryMode as $key => $resArr) {
                if ($key === $request->input('delivery_mode')){
                    $data = [];
                    if($resArr){
                        //unset($resArr['']);
                        foreach ($resArr as $key => $value) {
                            $data[] = [
                                'Id' =>  $key ,
                                'Name' => $value
                            ];
                        }
                    }
                }
            }
        }
        return $data;
    }
    public function getBatchDateInfo($request)
    {

        $arrBatch = Timetable::where('college_id', $request['college_id'])
                            ->where('batch', $request['batch'])
                            ->selectRaw('MIN(start_week) as start_week, MAX(end_week) as end_week')
                            ->first();

        return $arrBatch;
    }
    public function saveHigherEdUnitOutcome($request)
    {
        $unitEnrolment = StudentSubjectEnrolment::find($request['id']);
        if ($unitEnrolment) {
            $unitEnrolment->mark_outcome = $request['finalOutcome'];
            return $unitEnrolment->save();
        }
        return false;
    }
}
