<template lang="">
    <div class="-gap-1 flex flex-col items-center justify-center">
        <span class="indicator-top">
            <svg
                width="12"
                height="12"
                viewBox="0 0 12 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    d="M2.14645 7.35355C2.34171 7.54882 2.65829 7.54882 2.85355 7.35355L6 4.20711L9.14645 7.35355C9.34171 7.54882 9.65829 7.54882 9.85355 7.35355C10.0488 7.15829 10.0488 6.84171 9.85355 6.64645L6.35355 3.14645C6.15829 2.95118 5.84171 2.95118 5.64645 3.14645L2.14645 6.64645C1.95118 6.84171 1.95118 7.15829 2.14645 7.35355Z"
                    :fill="(dir == 'up' || dir == 'asc') ? '#1890ff' : '#9CA3AF'"
                />
            </svg>
        </span>
        <span class="indicator-bottom -mt-1.5">
            <svg
                width="12"
                height="12"
                viewBox="0 0 12 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    d="M2.14645 4.64645C2.34171 4.45118 2.65829 4.45118 2.85355 4.64645L6 7.79289L9.14645 4.64645C9.34171 4.45118 9.65829 4.45118 9.85355 4.64645C10.0488 4.84171 10.0488 5.15829 9.85355 5.35355L6.35355 8.85355C6.15829 9.04882 5.84171 9.04882 5.64645 8.85355L2.14645 5.35355C1.95118 5.15829 1.95118 4.84171 2.14645 4.64645Z"
                    :fill="(dir == 'down' || dir == 'desc') ? '#1890ff' : '#9CA3AF'"
                />
            </svg>
        </span>
    </div>
</template>
<script>
import { ref, computed, watch } from "vue";
import { attendanceResource } from "@spa/services/attendanceResource.js";
export default {
    props: {
        dir: String,
    },
};
</script>
<style lang=""></style>
