@props([
'overlay' => false
])
<div {{$attributes->twMerge('tw-ajax-loader h-no-tabs tw-ajax-loader--result px-8 py-6 bg-gray-100 relative
    hidden')}}>
    <div class="flex justify-between mb-4">
        <x-v2.skeleton.buttongroup :autoHide="false" count="1" class:button="w-[425px]" />
        <x-v2.skeleton.buttongroup :autoHide="false" count="4" />
    </div>
    <div class="space-y-6">
        <div class="grid grid-cols-12 gap-4 w-full py-1">
            <div
                class="flex col-span-6 md:col-span-2 p-4 bg-white/80 rounded-md justify-start items-center space-x-2">

                <div class="w-12 h-12 p-3 bg-sky-100 rounded-md justify-center items-center inline-flex">
                    <img src="{{ asset('v2/img/result_assessment.svg') }}" class="w-5 h-5" alt="Result Assessment">
                </div>
                <div class="inline-flex flex-col items-start justify-start flex-1 space-y-1">
                    <p class="text-sm leading-5 text-gray-400">Completion</p>
                    <div class="w-12 h-5 bg-gray-200 rounded animate-pulse mb-2"></div>
                </div>
            </div>
            <div
                class="flex col-span-6 md:col-span-2 p-4 bg-white/80 rounded-md justify-start items-center space-x-2">
                <div class="w-12 h-12 p-3 bg-sky-100 rounded-md justify-center items-center inline-flex">
                    <img src="{{ asset('v2/img/result_assessment.svg') }}" class="w-5 h-5" alt="Result Assessment">
                </div>
                <div class="inline-flex flex-col items-start justify-start flex-1 space-y-1">
                    <p class="text-sm leading-5 text-gray-400">Assessments</p>
                    <div class="w-12 h-5 bg-gray-200 rounded animate-pulse mb-2"></div>
                </div>
            </div>
            <div
                class="flex flex-col col-span-12 md:col-span-8 p-4 bg-white/80 rounded-md justify-start items-center space-x-2">
                <div class="w-full h-5 bg-gray-200 rounded-full animate-pulse mb-2"></div>
            </div>
        </div>
        <div class="flex">
            <x-v2.skeleton.buttongroup :autoHide="false" count="5" />
        </div>
        <x-v2.card>
            <x-slot name="header">
                <div class="flex justify-between gap-2">
                    <x-v2.skeleton.buttongroup :autoHide="false" count="2" />
                    <x-v2.skeleton.buttongroup :autoHide="false" count="2" />
                </div>
            </x-slot>
            <x-slot name="content">
                <x-v2.skeleton.card-grid rows="5" columns="4" id="documentSkeleton" class="py-4" />
            </x-slot>
        </x-v2.card>
    </div>
    @if($overlay)
    <x-v2.loader :showText="false" class="absolute bg-white/45 w-full h-full inset-0 p-0 h-no-tabs"
        class:spinner="w-12 h-12" />
    @endif
</div>