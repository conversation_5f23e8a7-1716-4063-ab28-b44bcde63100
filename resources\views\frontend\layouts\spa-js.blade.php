<!-- jQuery 2.2.3 -->
<script src="{{ asset('plugins/jQuery/jquery-2.2.3.min.js') }}"></script>
<script src="{{ asset('v2/js/kendo-new.all.min.js') }}"></script>
<script src="{{ asset('v2/js/kendo-ui-license.js') }}"></script>
@livewireScripts
<script>
    $(document).on(
    "click",
    '.tw-dropdown-trigger[data-toggle="dropdown"]',
    function (e) {
    e.stopPropagation();
    
    const $button = $(this);
    const $menu = $button.siblings(".tw-dropdown-popup");
    
    // Hide all other dropdowns
    $(".tw-dropdown-popup")
    .not($menu)
    .addClass("hidden")
    .attr("aria-hidden", "true");
    
    $menu.toggleClass("hidden");
    $menu.attr("aria-hidden", $menu.hasClass("hidden") ? "true" : "false");
    },
    );
    
    $(document).on("click", function () {
    $(".tw-dropdown-popup").addClass("hidden").attr("aria-hidden", "true");
    });
    window.cookieStorage = {
    getItem(key) {
    let cookies = document.cookie.split(";");
    for (let i = 0; i < cookies.length; i++) { let cookie=cookies[i].split("=");
                    if (key == cookie[0].trim()) {
                        return decodeURIComponent(cookie[1]);
                    }
                }
                return null;
            },
            setItem(key, value) {
                // Check if the cookie already exists and has the same value
                let currentValue = this.getItem(key);
                if (currentValue !== value) {
                    // Update the cookie if the value is different
                    document.cookie =
                        key + "=" + encodeURIComponent(value) + " ; path=/"; } }, };
    $(document).ready(function () {
        $(document).on('click', '.tw-menu-link', function (e) {
            const href = $(this).attr('href');
            console.log('clickedItem', href);

            if (!href || href === '#') {
                e.preventDefault();
                return;
            }

            // Remove active class from all links
            $('.tw-menu-link').removeClass('active');

            // Add active class to the clicked one
            $(this).addClass('active');
        });
    });
</script>

<script type="text/javascript"
    src="https://gapp01.atlassian.net/s/d41d8cd98f00b204e9800998ecf8427e-T/-3o5b4z/b/4/c95134bc67d3a521bb3f4331beb9b804/_/download/batch/com.atlassian.jira.collector.plugin.jira-issue-collector-plugin:issuecollector/com.atlassian.jira.collector.plugin.jira-issue-collector-plugin:issuecollector.js?locale=en-US&collectorId=115c2917">
</script>

<script type="text/javascript">
    window.ATL_JQ_PAGE_PROPS =  {
"triggerFunction": function(showCollectorDialog) {
    //Requires that jQuery is available! 
    jQuery("#feedback-button").click(function(e) {
        e.preventDefault();
        showCollectorDialog();
    });
}};
</script>