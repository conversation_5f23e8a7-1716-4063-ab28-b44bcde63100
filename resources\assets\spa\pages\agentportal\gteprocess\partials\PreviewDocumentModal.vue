<template>
    <div>
        <div class="kendo-modal-wrapper">
            <k-dialog
                v-if="visible"
                :title="getDrawerTitle"
                :width="width"
                @close="cancelProcess"
            >
                <div class="modal-content">
                    <div class="space-y-2">
                        <div
                            class="tw-filemanager tw-media-manager"
                            ref="previewPdf"
                        ></div>
                    </div>
                </div>
            </k-dialog>
        </div>
    </div>
</template>
<script>
import axios from "axios";
import { Link } from "@inertiajs/vue3";
import Button from "@spa/components/Buttons/Button.vue";
import { IconEdit24Regular } from "@iconify-prerendered/vue-fluent";
import { Dialog, DialogActionsBar } from "@progress/kendo-vue-dialogs";
import { Form } from "@progress/kendo-vue-form";
import FeedbackForm from "@studentportal/communication/partials/FeedbackForm.vue";
import TrainerForm from "@studentportal/communication/partials/TrainerForm.vue";
import { IconSearch24Regular } from "@iconify-prerendered/vue-fluent";

export default {
    props: {
        user: { type: Object, default: [] },
        userId: "",
        file: { type: [Blob, String, Object], default: null },
        filetype: { type: String, default: null },
        visible: { type: Boolean, default: false },
        isUrl: { type: Boolean, default: false },
        width: { type: String, default: "50%" },
    },

    components: {
        "tw-button": Button,
        "icon-pen": IconEdit24Regular,
        "k-dialog": Dialog,
        "k-dialog-actions-bar": DialogActionsBar,
        "k-form": Form,
        "feedback-form": FeedbackForm,
        "trainer-form": TrainerForm,
        "icon-search": IconSearch24Regular,
        Link,
    },
    data: function () {
        return {
            isSaving: false,
            isLoading: false,
            showVisible: this.visible,
        };
    },
    computed: {
        getDrawerTitle() {
            return "Preview Document";
        },
    },
    methods: {
        cancelProcess(e) {
            this.$emit("cancel", e);
        },
        allowSubmit(e) {
            this.$refs.feedbackForm.submit();
        },
        redirectToEditProfile() {
            const postData = {
                id: this.userId,
            };
            axios
                .post(
                    "update-last-password-change-date",
                    postData,
                    this.ajaxheaders,
                )
                .then((response) => {
                    window.location.href = this.route("student-edit-profile");
                });
        },
        previewDocument() {
            console.log("Called Document");
            if (!this.$refs.previewPdf) {
                console.error("studentDocument ref is undefined.");
                return;
            }
            const componentRef = this.$refs.previewPdf;

            if (!this.file) {
                return false;
            }
            if (this.isUrl) {
                var fileUrl = encodeURI(this.file);
            } else {
                var fileUrl = encodeURI(window["APP_URL"] + this.file);
            }

            $.when(
                $.getScript(
                    "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.js",
                ),
                $.getScript(
                    "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js",
                ),
            )
                .done(function () {
                    window.pdfjsLib.GlobalWorkerOptions.workerSrc =
                        "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js";
                })
                .then(function () {
                    const pdfView = $(componentRef)
                        .kendoPDFViewer({
                            pdfjsProcessing: {
                                file: fileUrl,
                            },
                            width: "100%",
                            height: "100%",
                        })
                        .data("kendoPDFViewer");
                });
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.previewDocument();
        });
    },
    watch: {
        showVisible(newValue) {
            console.log("Newval", newValue);
            if (newValue) {
                this.$nextTick(() => {
                    this.previewDocument();
                });
            }
        },
    },
};
</script>
<style lang=""></style>
