<?php

namespace App\Http\Controllers\v2\api;

use App\Mail\RequestGteDocument;
use App\Model\v2\Agent;
use App\Model\v2\GteCurrentVisaStatus;
use App\Model\v2\GteDocumentMaster;
use App\Model\v2\GteEnrollmentDocuments;
use App\Model\v2\GtePaymentsDocuments;
use App\Model\v2\GteProcessComment;
use App\Model\v2\GteProcessDocuments;
use App\Model\v2\GteProcessTrack;
use App\Model\v2\GteStudentDocuments;
use App\Model\v2\GteVisaDocuments;
use App\Model\v2\Student;
use App\Model\v2\StudentCourses;
use App\Repositories\GteProcessRepository;
use App\Traits\CommonTrait;
use App\Traits\ResponseTrait;
use App\Traits\SendNotificationTrait;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;
use Notifications\Types\DTOs\StudentVisaGrantNotificationDTO;
use Notifications\Types\NotificationType;

class StudentGteProcessApiController extends BaseController
{
    use CommonTrait;
    use ResponseTrait;
    use SendNotificationTrait;

    public $loginUser;

    protected $studentModel;

    protected $gteProcessDocument;

    protected $gteDocumentMaster;

    protected $gteStudentDocuments;

    protected $studentCourses;

    protected $gteProcessTrack;

    protected $gteEnrollmentDocuments;

    protected $gteVisaDocuments;

    protected $gteCurrentVisaStatus;

    protected $gteProcessComments;

    protected $gteProcessModel;

    protected $studentCourseModel;

    protected $gtePaymentsDocuments;

    protected $agentModel;

    public function __construct(
        Student $studentModel,
        GteProcessDocuments $gteProcessDocument,
        GteDocumentMaster $gteDocumentMaster,
        GteStudentDocuments $gteStudentDocuments,
        StudentCourses $studentCourses,
        GteProcessTrack $gteProcessTrack,
        GtePaymentsDocuments $gtePaymentsDocuments,
        GteEnrollmentDocuments $gteEnrollmentDocuments,
        GteVisaDocuments $gteVisaDocuments,
        GteCurrentVisaStatus $gteCurrentVisaStatus,
        GteProcessComment $gteProcessComments,
        Agent $agentModel
    ) {
        $this->studentModel = new GteProcessRepository($studentModel);
        $this->gteProcessModel = new GteProcessRepository($gteProcessDocument);
        $this->gteDocumentMaster = new GteProcessRepository($gteDocumentMaster);
        $this->gteStudentDocuments = new GteProcessRepository($gteStudentDocuments);
        $this->studentCourseModel = new GteProcessRepository($studentCourses);
        $this->gteProcessTrack = new GteProcessRepository($gteProcessTrack);
        $this->gtePaymentsDocuments = new GteProcessRepository($gtePaymentsDocuments);
        $this->gteEnrollmentDocuments = new GteProcessRepository($gteEnrollmentDocuments);
        $this->gteVisaDocuments = new GteProcessRepository($gteVisaDocuments);
        $this->gteCurrentVisaStatus = new GteProcessRepository($gteCurrentVisaStatus);
        $this->gteProcessComments = new GteProcessRepository($gteProcessComments);
        $this->agentModel = new GteProcessRepository($agentModel);

        $this->middleware(function ($request, $next) {
            if (! empty(Auth()->guard('agent')->user())) {
                $this->loginUser = Auth::guard('agent')->user();
            }
            if (! empty(Auth::user())) {
                $this->loginUser = Auth::user();
            }

            return $next($request);
        });
    }

    public function offerManageData(Request $request)
    {
        $dataArr = $this->studentModel->getStudentData($request);
        foreach ($dataArr as $key => $student) {
            $dataArr[$key]['profile_pic'] = $this->getStudentProfilePicPath($student['id'], $student['profile_picture'], 'small');
        }
        $data['data'] = $dataArr;
        $data['total'] = $this->studentModel->getStudentData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function filterOfferManage(Request $request)
    {

        if ($request->input('id')) {
            $data = $this->studentModel->getManageofferResult($request);

            return $this->successResponse('Data found successfully', 'data', $data);
        }
        $data = $this->getManageOfferFilterCategory();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    private function getManageOfferFilterCategory()
    {
        return [
            ['id' => 1, 'hasChild' => true, 'text' => 'Course', 'expanded' => false],
            ['id' => 2, 'hasChild' => true, 'text' => 'Agent', 'expanded' => false],
            ['id' => 3, 'hasChild' => true, 'text' => 'Status', 'expanded' => false],
        ];
    }

    public function uploadSignedOfferLetter(Request $request)
    {

        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $req = $request->input('metadata');
        $uploadData = json_decode($req, true);
        $filePath = Config::get('constants.uploadFilePath.SignedOfferLetter');
        $this->uploaderOfDocumentsWithChunk($request, $uploadData, $filePath, $collegeId, $studentId, 'is_offerletter', 'signedOfferLetter');
    }

    public function uploaderOfDocumentsWithChunk($request, $uploadData, $filePath, $collegeId, $studentId, $documentType, $documentName)
    {
        $originalFileName = date('YmdHis').$uploadData['fileName'];
        $fileUid = $uploadData['uploadUid'];
        $chunkIndex = $uploadData['chunkIndex'];
        $totalChunks = $uploadData['totalChunks'];
        $FileOrignalName = $uploadData['fileName'];
        $key = '{college_id}';
        $value = $collegeId;
        $default = str_replace("$key", $value, $filePath['default']);
        $view = str_replace("$key", $value, $filePath['view']);
        $resultArr = ['default' => $default.$studentId.'/', 'view' => $view.$studentId.'/'];
        $destinationPath = $resultArr;
        $signedOfferLetter = $file = $request->file('files');
        if (! is_dir($destinationPath['default'])) {
            @mkdir($destinationPath['default'], 0777);
        }
        // Temporary Chunk Path
        $chunkPath = $destinationPath['default'].DIRECTORY_SEPARATOR.$FileOrignalName.'.part';
        // Save the Current Chunk
        $chunkData = file_get_contents($file->getRealPath());
        // dd($chunkPath);

        file_put_contents($chunkPath, $chunkData, FILE_APPEND);
        // dd($chunkData);

        $request->request->add([
            'document_name' => $originalFileName,
            'file_original_name' => $FileOrignalName,
            'created_by' => $this->loginUser->id,
        ]);

        if ($chunkIndex + 1 == $totalChunks) {
            $finalName = hashFileName($FileOrignalName);
            $finalPath = $destinationPath['default'].DIRECTORY_SEPARATOR.$finalName;

            rename($chunkPath, $finalPath);
            if (isset($signedOfferLetter)) {
                $res = $file->move($destinationPath['default'], $originalFileName);

                if ($documentName == 'signedOfferLetter') {
                    $saveData = $this->gteProcessModel->create($request->input());
                    $checkStudentGte = $this->gteProcessTrack->getWhere(['student_id' => $studentId], ['id']);
                    if (isset($checkStudentGte) && $checkStudentGte != '') {
                        $data = $this->gteProcessTrack->modify([$documentType => 'started'], ['student_id' => $studentId]);
                    } else {
                        $data = $this->gteProcessTrack->create($request->input());
                    }
                    if ($res && $saveData) {
                        echo json_encode(['uploaded' => true, 'fileUid' => $fileUid, 'chunkIndex' => $chunkIndex, 'status' => 'success', 'message' => 'Uploaded Successfully Signed Letter of Offer']);
                        exit;
                    }
                } elseif ($documentName == 'Gtedocument') {
                    $saveData = $this->gteStudentDocuments->create($request->input());
                    $data = $this->gteProcessTrack->modify(['is_documentation' => 'started'], ['student_id' => $studentId]);
                    if ($res && $saveData) {
                        echo json_encode(['uploaded' => true, 'fileUid' => $fileUid, 'chunkIndex' => $chunkIndex, 'status' => 'success', 'message' => 'Uploaded Successfully Signed Letter of Offer']);
                        exit;
                    }
                } elseif ($documentName == 'PaymentDocument') {
                    $saveData = $this->gtePaymentsDocuments->create($request->input());
                    $data = $this->gteProcessTrack->modify(['is_payment' => 'started'], ['student_id' => $studentId]);
                    if ($res && $saveData) {
                        echo json_encode(['uploaded' => true, 'fileUid' => $fileUid, 'chunkIndex' => $chunkIndex, 'status' => 'success', 'message' => 'Uploaded Successfully Payment Document']);
                        exit;
                    }
                } elseif ($documentName == 'ConfirmationEnrollment') {
                    $saveData = $this->gteEnrollmentDocuments->create($request->input());
                    $data = $this->gteProcessTrack->modify(['is_enrollment' => 'started'], ['student_id' => $studentId]);
                    if ($res && $saveData) {
                        echo json_encode(['uploaded' => true, 'fileUid' => $fileUid, 'chunkIndex' => $chunkIndex, 'status' => 'success', 'message' => 'Uploaded Successfully Confirmation Of Enrollment']);
                        exit;
                    }
                } elseif ($documentName == 'VisaDocument') {
                    $saveData = $this->gteVisaDocuments->create($request->input());
                    $data = $this->gteProcessTrack->modify(['is_visa' => 'started'], ['student_id' => $studentId]);
                    if ($res && $saveData) {
                        echo json_encode(['uploaded' => true, 'fileUid' => $fileUid, 'chunkIndex' => $chunkIndex, 'status' => 'success', 'message' => 'Uploaded Successfully Visa Document']);
                        exit;
                    }
                }

            }
        }
        echo json_encode(['status' => 'error', 'uploaded' => false, 'fileUid' => $fileUid, 'chunkIndex' => $chunkIndex, 'message' => 'Something will be wrong. Please try again.']);
        exit;
    }

    public function uploadGtedocument(Request $request)
    {

        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $req = $request->input('metadata');
        $uploadData = json_decode($req, true);
        $filePath = Config::get('constants.uploadFilePath.GteDocument');
        $this->uploaderOfDocumentsWithChunk($request, $uploadData, $filePath, $collegeId, $studentId, 'is_documentation', 'Gtedocument');

    }

    public function uploadPaymentDocument(Request $request)
    {

        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $req = $request->input('metadata');
        $uploadData = json_decode($req, true);
        $filePath = Config::get('constants.uploadFilePath.GtePayment');
        $this->uploaderOfDocumentsWithChunk($request, $uploadData, $filePath, $collegeId, $studentId, 'is_payment', 'PaymentDocument');
    }

    public function uploadConfirmationEnrollment(Request $request)
    {

        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $req = $request->input('metadata');
        $uploadData = json_decode($req, true);
        $filePath = Config::get('constants.uploadFilePath.ConfirmationOfEnrollment');
        $this->uploaderOfDocumentsWithChunk($request, $uploadData, $filePath, $collegeId, $studentId, 'is_enrollment', 'ConfirmationEnrollment');

    }

    public function getGtedocumentList(Request $request)
    {
        $studentId = $request->input('student_id');
        $data = $this->gteDocumentMaster->getGTEDocumentData($studentId);

        return $this->successResponse('Email sent successfully to agent', 'data', $data);
    }

    public function uploadVisaDocument(Request $request)
    {

        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $req = $request->input('metadata');
        $uploadData = json_decode($req, true);
        $filePath = Config::get('constants.uploadFilePath.GteVisa');
        $this->uploaderOfDocumentsWithChunk($request, $uploadData, $filePath, $collegeId, $studentId, 'is_visa', 'VisaDocument');

    }
    /*
        public function uploadGtedocument(Request $request){
            $collegeId = $request->college_id;
            $studentId = $request->student_id;
            $req = $request->input('metadata');
            $uploadData = json_decode($req,true);
            $originalFileName = hashFileName($uploadData['fileName']);
            $FileOrignalName = $uploadData['fileName'];
            $request->request->add([
                'document_file_name' => $originalFileName,
                'file_original_name' => $FileOrignalName,
                'created_by' => $this->loginUser->id
            ]);

            $signedOfferLetter = $request->file();
            if (isset($signedOfferLetter['files'])) {
                $file = $signedOfferLetter['files'];
                $filePath = Config::get('constants.uploadFilePath.GteDocument');
                $key = '{college_id}';
                $value = $collegeId;
                $default = str_replace("$key", $value, $filePath['default']);
                $view = str_replace("$key", $value, $filePath['view']);
                $resultArr = ['default' => $default.$studentId.'/', 'view' => $view.$studentId.'/'];
                $destinationPath = $resultArr;
                $res = $file->move($destinationPath['default'], $originalFileName);
                $saveData = $this->gteStudentDocuments->create($request->input());
                $data = $this->gteProcessTrack->modify(['is_documentation' => 'started'], ['student_id' => $studentId]);
                if($res && $saveData){
                    echo json_encode(['uploaded' => true, 'status'=>'success', 'message'=>'Uploaded Successfully Signed Letter of Offer']);exit;
                }
            }
            echo json_encode(['status'=>'error', 'message'=>'Something will be wrong. Please try again.']);exit;
        }
        public function uploadPaymentDocument(Request $request){

            $collegeId = $request->college_id;
            $studentId = $request->student_id;
            $req = $request->input('metadata');
            $uploadData = json_decode($req,true);
            $originalFileName = date('YmdHis').$uploadData['fileName'];
            $FileOrignalName = $uploadData['fileName'];
            $request->request->add([
                'document_name' => $originalFileName,
                'file_original_name' => $FileOrignalName,
                'created_by' => $this->loginUser->id
            ]);

            $signedOfferLetter = $request->file();
            if (isset($signedOfferLetter['files'])) {
                $file = $signedOfferLetter['files'];
                $filePath = Config::get('constants.uploadFilePath.GtePayment');
                $key = '{college_id}';
                $value = $collegeId;
                $default = str_replace("$key", $value, $filePath['default']);
                $view = str_replace("$key", $value, $filePath['view']);
                $resultArr = ['default' => $default.$studentId.'/', 'view' => $view.$studentId.'/'];
                $destinationPath = $resultArr;
                $res = $file->move($destinationPath['default'], $originalFileName);
                $saveData = $this->gtePaymentsDocuments->create($request->input());
                $data = $this->gteProcessTrack->modify(['is_payment' => 'started'], ['student_id' => $studentId]);
                if($res && $saveData){
                    echo json_encode(['uploaded' => true, 'status'=>'success', 'message'=>'Uploaded Successfully Payment Document']);exit;
                }
            }
            echo json_encode(['status'=>'error', 'message'=>'Something will be wrong. Please try again.']);exit;
        }

        public function uploadConfirmationEnrollment(Request $request){

            $collegeId = $request->college_id;
            $studentId = $request->student_id;
            $req = $request->input('metadata');
            $uploadData = json_decode($req,true);
            $originalFileName = date('YmdHis').$uploadData['fileName'];
            $FileOrignalName = $uploadData['fileName'];
            $request->request->add([
                'document_name' => $originalFileName,
                'file_original_name' => $FileOrignalName,
                'created_by' => $this->loginUser->id
            ]);

            $signedOfferLetter = $request->file();
            if (isset($signedOfferLetter['files'])) {
                $file = $signedOfferLetter['files'];
                $filePath = Config::get('constants.uploadFilePath.ConfirmationOfEnrollment');
                $key = '{college_id}';
                $value = $collegeId;
                $default = str_replace("$key", $value, $filePath['default']);
                $view = str_replace("$key", $value, $filePath['view']);
                $resultArr = ['default' => $default.$studentId.'/', 'view' => $view.$studentId.'/'];
                $destinationPath = $resultArr;
                $res = $file->move($destinationPath['default'], $originalFileName);
                $saveData = $this->gteEnrollmentDocuments->create($request->input());
                $data = $this->gteProcessTrack->modify(['is_enrollment' => 'started'], ['student_id' => $studentId]);
                if($res && $saveData){
                    echo json_encode(['uploaded' => true, 'status'=>'success', 'message'=>'Uploaded Successfully Confirmation Of Enrollment']);exit;
                }
            }
            echo json_encode(['status'=>'error', 'message'=>'Something will be wrong. Please try again.']);exit;
        }



        public function uploadVisaDocument(Request $request){

            $collegeId = $request->college_id;
            $studentId = $request->student_id;
            $req = $request->input('metadata');
            $uploadData = json_decode($req,true);
            $originalFileName = date('YmdHis').$uploadData['fileName'];
            $FileOrignalName = $uploadData['fileName'];
            $request->request->add([
                'document_name' => $originalFileName,
                'file_original_name' => $FileOrignalName,
                'created_by' => $this->loginUser->id
            ]);

            $signedOfferLetter = $request->file();
            if (isset($signedOfferLetter['files'])) {
                $file = $signedOfferLetter['files'];
                $filePath = Config::get('constants.uploadFilePath.GteVisa');
                $key = '{college_id}';
                $value = $collegeId;
                $default = str_replace("$key", $value, $filePath['default']);
                $view = str_replace("$key", $value, $filePath['view']);
                $resultArr = ['default' => $default.$studentId.'/', 'view' => $view.$studentId.'/'];
                $destinationPath = $resultArr;
                $res = $file->move($destinationPath['default'], $originalFileName);
                $saveData = $this->gteVisaDocuments->create($request->input());
                $data = $this->gteProcessTrack->modify(['is_visa' => 'started'], ['student_id' => $studentId]);
                if($res && $saveData){
                    echo json_encode(['uploaded' => true, 'status'=>'success', 'message'=>'Uploaded Successfully Visa Document']);exit;
                }
            }
            echo json_encode(['status'=>'error', 'message'=>'Something will be wrong. Please try again.']);exit;
        } */

    public function uploadedOfferLetterData(Request $request)
    {
        $data['data'] = $this->gteProcessModel->uploadedOfferLetterData($request);
        $data['total'] = $this->gteProcessModel->uploadedOfferLetterData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function uploadedGteDocumentsData(Request $request)
    {
        $data['data'] = $this->gteStudentDocuments->uploadedGteDocumentData($request);
        $data['total'] = $this->gteStudentDocuments->uploadedGteDocumentData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function uploadedPaymentData(Request $request)
    {
        $data['data'] = $this->gtePaymentsDocuments->uploadedPaymentData($request);
        $data['total'] = $this->gtePaymentsDocuments->uploadedPaymentData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function uploadedConfirmationEnrollData(Request $request)
    {
        $data['data'] = $this->gteEnrollmentDocuments->uploadedConfirmationEnrollData($request);
        $data['total'] = $this->gteEnrollmentDocuments->uploadedConfirmationEnrollData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function uploadedVisaData(Request $request)
    {
        $data['data'] = $this->gteVisaDocuments->uploadedVisaData($request);
        $data['total'] = $this->gteVisaDocuments->uploadedVisaData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function previousOfferLetterData(Request $request)
    {
        $data['data'] = $this->gteProcessModel->previousOfferLetterData($request);
        $data['total'] = $this->gteProcessModel->previousOfferLetterData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function previousGteDocumentsData(Request $request)
    {
        $data['data'] = $this->gteStudentDocuments->previousGteDocumentsData($request);
        $data['total'] = $this->gteStudentDocuments->previousGteDocumentsData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function previousPaymentData(Request $request)
    {
        $data['data'] = $this->gtePaymentsDocuments->previousPaymentData($request);
        $data['total'] = $this->gtePaymentsDocuments->previousPaymentData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function previousConfirmationEnrollData(Request $request)
    {
        $data['data'] = $this->gteEnrollmentDocuments->previousConfirmationEnrollData($request);
        $data['total'] = $this->gteEnrollmentDocuments->previousConfirmationEnrollData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function previousVisaData(Request $request)
    {
        $data['data'] = $this->gteVisaDocuments->previousVisaData($request);
        $data['total'] = $this->gteVisaDocuments->previousVisaData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    private function approveRejectWithMail($request, $status)
    {
        $formData = $request->input('formData');
        $arrData = [];
        parse_str($formData, $arrData);
        $arrData['is_email'] = (isset($arrData['is_email']) && $arrData['is_email'] == 'on') ? '1' : '0';
        $arrData['is_submission'] = (isset($arrData['is_submission']) && $arrData['is_submission'] == 'on') ? '1' : '0';
        $arrData['status'] = $status;
        $arrData['updated_by'] = $this->loginUser->id;

        if (isset($arrData['is_email']) && $arrData['is_email'] == 1) {
            $getAgentMail = $this->studentCourseModel->getAgentDetails($arrData['student_course_id'])->toArray();
            $sendMailtoAgent = Mail::to($getAgentMail[0]['agent_email'])->send(new RequestGteDocument($arrData['comment']));
        }

        return $arrData;
    }

    public function approveOfferLetter(Request $request)
    {
        $arrData = $this->approveRejectWithMail($request, 'approved');
        $updateGte = $this->gteProcessModel->update($arrData, $request->id);

        return $this->successResponse('Approved Successfully Signed Letter of Offer', 'data', $updateGte);
    }

    public function approveGteDocument(Request $request)
    {
        $arrData = $this->approveRejectWithMail($request, 'approved');
        $updateGte = $this->gteStudentDocuments->update($arrData, $request->id);

        return $this->successResponse('Approved Successfully GTE Document', 'data', $updateGte);
    }

    public function approveGtePayment(Request $request)
    {
        $arrData = $this->approveRejectWithMail($request, 'approved');
        $updateGte = $this->gtePaymentsDocuments->update($arrData, $request->id);

        return $this->successResponse('Approved Successfully GTE Payment', 'data', $updateGte);
    }

    public function approveGteEnrollment(Request $request)
    {
        $arrData = $this->approveRejectWithMail($request, 'approved');
        $updateGte = $this->gteEnrollmentDocuments->update($arrData, $request->id);

        return $this->successResponse('Approved Successfully Confirmation Of Enrollment', 'data', $updateGte);
    }

    public function approveGteVisa(Request $request)
    {

        $arrData = $this->approveRejectWithMail($request, 'approved');

        $updateGte = $this->gteVisaDocuments->update($arrData, $request->id);

        $courseDetail = StudentCourses::find($arrData['student_course_id']);
        // Student Visa Grant Notification
        $notificationData = StudentVisaGrantNotificationDTO::LazyFromArray([
            'courseId' => $courseDetail->course_id,
            'courseName' => $courseDetail->course->course_name,
            'enrollmentUrl' => '',
            'preDepartureUrl' => '',
        ]);
        $student = Student::with('associatedUserAccount')->find($courseDetail->student_id);
        if ($student && $student->associatedUserAccount) {
            $studentUser = $student->associatedUserAccount;
            $this->sendNotification(
                $studentUser,
                NotificationType::STUDENT_VISA_GRANT,
                $notificationData,
                true
            );
        }

        return $this->successResponse('Approved Successfully GTE Visa', 'data', $updateGte);
    }

    public function rejectOfferLetter(Request $request)
    {
        $arrData = $this->approveRejectWithMail($request, 'rejected');
        $updateGte = $this->gteProcessModel->update($arrData, $request->id);

        return $this->successResponse('Rejected Successfully Signed Letter of Offer', 'data', $updateGte);
    }

    public function rejectGteDocument(Request $request)
    {
        $arrData = $this->approveRejectWithMail($request, 'rejected');
        $updateGte = $this->gteStudentDocuments->update($arrData, $request->id);

        return $this->successResponse('Rejected Successfully GTE Document', 'data', $updateGte);
    }

    public function rejectGtePayment(Request $request)
    {
        $arrData = $this->approveRejectWithMail($request, 'rejected');
        $updateGte = $this->gtePaymentsDocuments->update($arrData, $request->id);

        return $this->successResponse('Rejected Successfully GTE Payment', 'data', $updateGte);
    }

    public function rejectGteEnrollment(Request $request)
    {
        $arrData = $this->approveRejectWithMail($request, 'rejected');
        $updateGte = $this->gteEnrollmentDocuments->update($arrData, $request->id);

        return $this->successResponse('Rejected Successfully Confirmation Of Enrollment', 'data', $updateGte);
    }

    public function rejectGteVisa(Request $request)
    {
        $arrData = $this->approveRejectWithMail($request, 'rejected');
        $updateGte = $this->gteVisaDocuments->update($arrData, $request->id);

        return $this->successResponse('Rejected Successfully GTE Visa', 'data', $updateGte);
    }

    public function requestGteDocumentMail(Request $request)
    {
        $email = $request->email;
        $contentData = $request->content;
        $sendEmail = Mail::to($email)->send(new RequestGteDocument($contentData));
        if ($sendEmail) {
            return $this->successResponse('Email sent successfully to agent', 'data', []);
        } else {
            return $this->errorResponse('Something went wronge.', 'data', []);
        }
    }

    public function markStageAsComplete(Request $request)
    {

        $request->request->add([
            'is_offerletter' => 'completed',
            'college_id' => $request->college_id,
        ]);

        $whereArr = [
            'college_id' => $request->college_id,
            'student_id' => $request->student_id,
        ];
        $uploadDocMessage = $this->errorResponse('Please Upload Document First', 'data', [], 200);
        $approveDocMessage = $this->errorResponse('Please Approve Document First', 'data', [], 200);

        if ($request->stepno == 1) {
            $checkAprroval = $this->gteProcessModel->checkStatus($whereArr);
            if (isset($checkAprroval)) {
                if ($checkAprroval->status == 'approved') {
                    $data = $this->gteProcessTrack->modify(['is_offerletter' => 'completed'], ['student_id' => $request->student_id]);
                    $data1 = 'Approved';
                } else {
                    return $approveDocMessage;
                }
            } else {
                return $uploadDocMessage;
            }

        } elseif ($request->stepno == 2) {
            $checkAprroval = $this->gteStudentDocuments->checkStatus($whereArr);
            if (isset($checkAprroval)) {
                if ($checkAprroval->status == 'approved') {
                    $data = $this->gteProcessTrack->modify(['is_documentation' => 'completed'], ['student_id' => $request->student_id]);
                    $data1 = 'Approved';
                } else {
                    return $approveDocMessage;
                }
            } else {
                return $uploadDocMessage;
            }
        } elseif ($request->stepno == 3) {
            $checkAprroval = $this->gtePaymentsDocuments->checkStatus($whereArr);
            if (isset($checkAprroval)) {
                if ($checkAprroval->status == 'approved') {
                    $data = $this->gteProcessTrack->modify(['is_payment' => 'completed'], ['student_id' => $request->student_id]);
                    $data1 = 'Approved';
                } else {
                    return $approveDocMessage;
                }
            } else {
                return $uploadDocMessage;
            }
        } elseif ($request->stepno == 4) {
            $checkAprroval = $this->gteEnrollmentDocuments->checkStatus($whereArr);
            if (isset($checkAprroval)) {
                if ($checkAprroval->status == 'approved') {
                    $data = $this->gteProcessTrack->modify(['is_enrollment' => 'completed'], ['student_id' => $request->student_id]);
                    $data1 = 'Approved';
                } else {
                    return $approveDocMessage;
                }
            } else {
                return $uploadDocMessage;
            }
        } elseif ($request->stepno == 5) {
            $checkAprroval = $this->gteVisaDocuments->checkStatus($whereArr);
            if (isset($checkAprroval)) {
                if ($checkAprroval->status == 'approved') {
                    $data = $this->gteProcessTrack->modify(['is_visa' => 'completed'], ['student_id' => $request->student_id]);
                    $data1 = 'Approved';
                } else {
                    return $approveDocMessage;
                }
            } else {
                return $uploadDocMessage;
            }
        }

        return $this->successResponse('Step Completed Successfully', 'data', $data1);
    }

    public function updateCurrentVisaStatus(Request $request)
    {
        $data = $request->input('data');
        $statusDate = date('Y-m-d H:i:s', strtotime($data['date']));
        if ($data['current_status'] == 'Application in Progress') {
            $data['progress_date'] = $statusDate;
        } elseif ($data['current_status'] == 'Application Submitted') {
            $data['submitted_date'] = $statusDate;
        } else {
            $data['result_date'] = $statusDate;
        }

        $checkIsCurrentVisa = $this->gteCurrentVisaStatus->getWhere(['student_id' => $data['student_id']], ['id']);
        if (isset($checkIsCurrentVisa) && $checkIsCurrentVisa != '') {
            $updateStatus = $this->gteCurrentVisaStatus->update($data, $checkIsCurrentVisa->id);
        } else {
            $createStatus = $this->gteCurrentVisaStatus->create($data);
        }

        return $this->successResponse('Current Visa Status Updated Successfully', 'data', $data);
    }

    public function saveGteComments(Request $request)
    {

        $data = $request->input();

        if ($request->stepNo == 1) {
            $getData = $this->gteProcessComments->getData(['student_id' => $data['student_id']], ['offer_comment']);
            if (! empty($getData)) {
                if ($getData[0]['offer_comment'] != '') {
                    $getTabCommentArr = json_decode($getData[0]['offer_comment'], true);
                } else {
                    $getTabCommentArr = [];
                }
                array_push($getTabCommentArr, $data);
            } else {
                $getTabCommentArr = [$data];

            }
            $data['offer_comment'] = json_encode($getTabCommentArr);

        } elseif ($request->stepNo == 2) {
            $getData = $this->gteProcessComments->getData(['student_id' => $data['student_id']], ['document_comment']);
            if (! empty($getData)) {
                if ($getData[0]['document_comment'] != '') {
                    $getTabCommentArr = json_decode($getData[0]['document_comment'], true);
                } else {
                    $getTabCommentArr = [];
                }
                array_push($getTabCommentArr, $data);
            } else {
                $getTabCommentArr = [$data];
            }
            $data['document_comment'] = json_encode($getTabCommentArr);

        } elseif ($request->stepNo == 3) {
            $getData = $this->gteProcessComments->getData(['student_id' => $data['student_id']], ['payment_comment']);
            if (! empty($getData)) {
                if ($getData[0]['payment_comment'] != '') {
                    $getTabCommentArr = json_decode($getData[0]['payment_comment'], true);
                } else {
                    $getTabCommentArr = [];
                }
                array_push($getTabCommentArr, $data);
            } else {
                $getTabCommentArr = [$data];
            }
            $data['payment_comment'] = json_encode($getTabCommentArr);
        } elseif ($request->stepNo == 4) {
            $data = $request->input();
            $getData = $this->gteProcessComments->getData(['student_id' => $data['student_id']], ['confirmation_comment']);
            if (! empty($getData)) {
                if ($getData[0]['confirmation_comment'] != '') {
                    $getTabCommentArr = json_decode($getData[0]['confirmation_comment'], true);
                } else {
                    $getTabCommentArr = [];
                }
                array_push($getTabCommentArr, $data);
            } else {
                $getTabCommentArr = [$data];
            }
            $data['confirmation_comment'] = json_encode($getTabCommentArr);
        } elseif ($request->stepNo == 5) {
            $data = $request->input();
            $getData = $this->gteProcessComments->getData(['student_id' => $data['student_id']], ['visa_status_comment']);
            if (! empty($getData)) {
                if ($getData[0]['visa_status_comment'] != '') {
                    $getTabCommentArr = json_decode($getData[0]['visa_status_comment'], true);
                } else {
                    $getTabCommentArr = [];
                }
                array_push($getTabCommentArr, $data);
            } else {
                $getTabCommentArr = [$data];
            }
            $data['visa_status_comment'] = json_encode($getTabCommentArr);
        }
        $savecomment = $this->gteProcessComments->getWhere(['student_id' => $data['student_id']], ['id']);
        if (isset($savecomment) && $savecomment != '') {
            $updateComment = $this->gteProcessComments->update($data, $savecomment->id);
        } else {
            $createComment = $this->gteProcessComments->create($data);
        }

        return $this->successResponse('Commented Successfully On Document', 'data', $savecomment, 200);
    }

    public function getSectionWiseComments(Request $request)
    {

        $data = $request->input();
        $getData = $this->gteProcessComments->getData(['student_id' => $data['student_id']], ['offer_comment', 'document_comment', 'payment_comment', 'confirmation_comment', 'visa_status_comment']);
        if (! empty($getData)) {
            if ($getData[0]['offer_comment'] != '') {
                $offer = json_decode($getData[0]['offer_comment'], true);
            } else {
                $offer = [];
            }
            if ($getData[0]['document_comment'] != '') {
                $documentation = json_decode($getData[0]['document_comment'], true);
            } else {
                $documentation = [];
            }
            if ($getData[0]['payment_comment'] != '') {
                $payment = json_decode($getData[0]['payment_comment'], true);
            } else {
                $payment = [];
            }
            if ($getData[0]['confirmation_comment'] != '') {
                $enrollment = json_decode($getData[0]['confirmation_comment'], true);
            } else {
                $enrollment = [];
            }
            if ($getData[0]['visa_status_comment'] != '') {
                $visa = json_decode($getData[0]['visa_status_comment'], true);
            } else {
                $visa = [];
            }
            $getTabCommentArr['offer'] = $offer;
            $getTabCommentArr['documentation'] = $documentation;
            $getTabCommentArr['payment'] = $payment;
            $getTabCommentArr['enrollment'] = $enrollment;
            $getTabCommentArr['visa'] = $visa;
        } else {
            $getTabCommentArr['offer'] = [];
            $getTabCommentArr['documentation'] = [];
            $getTabCommentArr['payment'] = [];
            $getTabCommentArr['enrollment'] = [];
            $getTabCommentArr['visa'] = [];
        }

        return $this->successResponse('Data Found Successfully', 'data', $getTabCommentArr, 200);
    }

    public function getVisaStepperData(Request $request)
    {

        $data = $request->input('data');
        $checkIsCurrentVisa = $this->gteCurrentVisaStatus->getWhere(['student_id' => $data['student_id']], ['id', 'current_status', 'progress_date', 'submitted_date', 'result_date']);
        if (isset($checkIsCurrentVisa) && $checkIsCurrentVisa != '') {
            $progress_date = ($checkIsCurrentVisa->progress_date != '0000-00-00') ? date('M d, Y', strtotime($checkIsCurrentVisa->progress_date)) : '- -';
            $submitted_date = ($checkIsCurrentVisa->submitted_date != '0000-00-00') ? date('M d, Y', strtotime($checkIsCurrentVisa->submitted_date)) : '- -';
            $result_date = ($checkIsCurrentVisa->result_date != '0000-00-00') ? date('M d, Y', strtotime($checkIsCurrentVisa->result_date)) : '- -';
            $cprogress_date = ($checkIsCurrentVisa->progress_date != '0000-00-00') ? date('m/d/Y', strtotime($checkIsCurrentVisa->progress_date)) : '- -';
            $csubmitted_date = ($checkIsCurrentVisa->submitted_date != '0000-00-00') ? date('m/d/Y', strtotime($checkIsCurrentVisa->submitted_date)) : '- -';
            $cresult_date = ($checkIsCurrentVisa->result_date != '0000-00-00') ? date('m/d/Y', strtotime($checkIsCurrentVisa->result_date)) : '- -';
            $finalArr = [
                [
                    'status' => 'Application in Progress',
                    'date' => $progress_date,
                    'cdate' => $cprogress_date,
                ],
                [
                    'status' => 'Application Submitted',
                    'date' => $submitted_date,
                    'cdate' => $csubmitted_date,
                ],
                [
                    'status' => ($result_date != '- -') ? $checkIsCurrentVisa->current_status : 'VISA Result',
                    'date' => $result_date,
                    'cdate' => $cresult_date,
                ],
            ];
        } else {
            $finalArr = [[
                'status' => 'Application in Progress',
                'date' => '- -',
            ],
                [
                    'status' => 'Application Submitted',
                    'date' => '- -',
                ],
                [
                    'status' => 'VISA Result',
                    'date' => '- -',
                ]];
        }

        return $this->successResponse('Data found successfully', 'data', $finalArr);
    }

    public function removeGteDocument(Request $request)
    {

        $documentPath = $request->path;
        $stepId = $request->stepId;
        if ($request->path) {
            $dirPath = public_path().$documentPath;
            // if (is_dir($dirPath)){
            unlink($dirPath);
            // }
        }
        if ($stepId == 1) {
            $res = $this->gteProcessModel->delete($request->id);
        } elseif ($stepId == 2) {
            $res = $this->gteStudentDocuments->delete($request->id);
        } elseif ($stepId == 3) {
            $res = $this->gtePaymentsDocuments->delete($request->id);
        } elseif ($stepId == 4) {
            $res = $this->gteEnrollmentDocuments->delete($request->id);
        } elseif ($stepId == 5) {
            $res = $this->gteVisaDocuments->delete($request->id);
        }

        return $this->successResponse('Document Deleted Successfully', 'data', $res, 200);
    }

    public function resetGteDocument(Request $request)
    {
        $stepId = $request->stepno;
        $whereArr = [
            'college_id' => $request->college_id,
            'student_id' => $request->student_id,
        ];
        $key = '{college_id}';
        $value = $request->college_id;
        $errorMessage = $this->errorResponse('Document Does Not Exist', 'data', [], 200);
        if ($stepId == 1) {
            $filePath = Config::get('constants.uploadFilePath.SignedOfferLetter');
            $default = str_replace("$key", $value, $filePath['default']);
            $resultArr = ['default' => $default.$request->student_id, 'view' => $request->student_id.'/'];
            if (is_dir($resultArr['default'])) {
                $this->delete_directory($resultArr['default']);
            } else {
                return $errorMessage;
            }
            $getRecords = $this->gteProcessModel->getData($whereArr, ['id']);
            $data = array_column($getRecords, 'id');
            $allRecordDelete = $this->gteProcessModel->deleteAll($data);
            $updateStatus = $this->gteProcessTrack->modify(['is_offerletter' => 'notstarted'], ['student_id' => $request->student_id]);
        } elseif ($stepId == 2) {
            $filePath = Config::get('constants.uploadFilePath.GteDocument');
            $default = str_replace("$key", $value, $filePath['default']);
            $resultArr = ['default' => $default.$request->student_id, 'view' => $request->student_id.'/'];
            if (is_dir($resultArr['default'])) {
                $this->delete_directory($resultArr['default']);
            } else {
                return $errorMessage;
            }
            $getRecords = $this->gteStudentDocuments->getData($whereArr, ['id']);
            $data = array_column($getRecords, 'id');
            $allRecordDelete = $this->gteStudentDocuments->deleteAll($data);
            $updateStatus = $this->gteProcessTrack->modify(['is_documentation' => 'notstarted'], ['student_id' => $request->student_id]);
        } elseif ($stepId == 3) {
            $filePath = Config::get('constants.uploadFilePath.GtePayment');
            $default = str_replace("$key", $value, $filePath['default']);
            $resultArr = ['default' => $default.$request->student_id, 'view' => $request->student_id.'/'];
            if (is_dir($resultArr['default'])) {
                $this->delete_directory($resultArr['default']);
            } else {
                return $errorMessage;
            }
            $getRecords = $this->gtePaymentsDocuments->getData($whereArr, ['id']);
            $data = array_column($getRecords, 'id');
            $allRecordDelete = $this->gtePaymentsDocuments->deleteAll($data);
            $updateStatus = $this->gteProcessTrack->modify(['is_payment' => 'notstarted'], ['student_id' => $request->student_id]);
        } elseif ($stepId == 4) {
            $filePath = Config::get('constants.uploadFilePath.ConfirmationOfEnrollment');
            $default = str_replace("$key", $value, $filePath['default']);
            $resultArr = ['default' => $default.$request->student_id, 'view' => $request->student_id.'/'];
            if (is_dir($resultArr['default'])) {
                $this->delete_directory($resultArr['default']);
            } else {
                return $errorMessage;
            }
            $getRecords = $this->gteEnrollmentDocuments->getData($whereArr, ['id']);
            $data = array_column($getRecords, 'id');
            $allRecordDelete = $this->gteEnrollmentDocuments->deleteAll($data);
            $updateStatus = $this->gteProcessTrack->modify(['is_enrollment' => 'notstarted'], ['student_id' => $request->student_id]);
        } elseif ($stepId == 5) {
            $filePath = Config::get('constants.uploadFilePath.GteVisa');
            $default = str_replace("$key", $value, $filePath['default']);
            $resultArr = ['default' => $default.$request->student_id, 'view' => $request->student_id.'/'];
            if (is_dir($resultArr['default'])) {
                $this->delete_directory($resultArr['default']);
            } else {
                return $errorMessage;
            }
            $getRecords = $this->gteVisaDocuments->getData($whereArr, ['id']);
            $data = array_column($getRecords, 'id');
            $allRecordDelete = $this->gteVisaDocuments->deleteAll($data);
            $updateStatus = $this->gteProcessTrack->modify(['is_visa' => 'notstarted'], ['student_id' => $request->student_id]);
        }

        return $this->successResponse('Document Reset Successfully', 'data', $allRecordDelete, 200);
    }

    public function delete_directory($dirname)
    {
        if (is_dir($dirname)) {
            $dir_handle = opendir($dirname);
        }
        if (! $dir_handle) {
            return false;
        }
        while ($file = readdir($dir_handle)) {
            if ($file != '.' && $file != '..') {
                if (! is_dir($dirname.'/'.$file)) {
                    unlink($dirname.'/'.$file);
                } else {
                    $this->delete_directory($dirname.'/'.$file);
                }
            }
        }
        closedir($dir_handle);
        rmdir($dirname);

        return true;
    }

    public function getAgentAgencyName(Request $request)
    {
        $whereArr = [
            'college_id' => $request->user()->college_id,
        ];
        $data1 = Agent::Where($whereArr)->select('agency_name as agent_name')->get()->toarray();
        $data = [];
        foreach ($data1 as $v) {
            $data[] = $v['agent_name'];
        }

        return $this->successResponse('data found successfully', 'data', $data);
    }
}
