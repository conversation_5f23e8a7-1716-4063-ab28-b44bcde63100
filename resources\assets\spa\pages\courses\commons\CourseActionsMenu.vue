<template lang="">
    <twbutton
        size="xs"
        variant="action"
        title="Manage Columns"
        ref="actionMenu"
        className="option-menu w-7 h-7"
        @click.stop="handleToggle"
        :autoHide="false"
    >
        <icon
            :name="'action-menu'"
            width="16"
            height="16"
            viewbox="0 0 24 24"
            :fill="'#9CA3AF'"
        />
    </twbutton>
    <popup
        :anchor="'actionMenu'"
        ref="popupRef"
        :show="show"
        v-bind="popupOptions"
    >
        <div>
            <template v-for="item in getOptions">
                <div
                    class="flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100"
                    @click="handleItemClick($event, item.index)"
                >
                    <div class="flex items-center">
                        <icon :name="item.icon" />
                    </div>
                    <div class="text-sm">{{ item.text }}</div>
                </div>
            </template>
        </div>
    </popup>
</template>
<script>
import { Popup } from "@progress/kendo-vue-popup";
import Button from "@spa/components/Buttons/Button";
export default {
    props: {
        isMoodleConnect: Boolean,
        courseItem: Object,
    },
    components: {
        popup: Popup,
        twbutton: Button,
    },
    emits: {
        structure: null,
        changestatus: null,
        delete: null,
        sync: null,
    },
    data: function () {

        const menuOptions = [
            { index: 0, text: "Course Structure", icon: "structure" },
            { index: 1, text: "Deactivate", icon: "toggle-off" },
            { index: 2, text: "Delete", icon: "delete" }
        ];

        if (this.isMoodleConnect && this.courseItem.moodle_data?.sync_status !== "Synced") {
            menuOptions.push({
                index: 3,
                text: this.courseItem.moodle_data?.sync_status === "Sync Failed" ? "Resync to Moodle" : "Sync to Moodle",
                icon: "sync"
            });
        }

        return {
            options: menuOptions,
            /*options: [
                {
                    index: 0,
                    text: "Course Structure",
                    icon: "structure",
                },
                {
                    index: 1,
                    text: "Deactivate",
                    icon: "toggle-off",
                },
                {
                    index: 2,
                    text: "Delete",
                    icon: "delete",
                },
            ],*/
            popupOptions: {
                popupClass: "tw-popup tw-popup--tr !p-1",
                popupAlign: {
                    horizontal: "right",
                    vertical: "top",
                },
                anchorAlign: {
                    horizontal: "right",
                    vertical: "bottom",
                },
                animate: false,
                offset: {
                    top: 5,
                },
            },
            show: false,
            loading: false,
        };
    },
    computed: {
        getOptions() {
            const menuItems = this.options;
            if (!this.courseItem.activated_now) {
                menuItems[1].text = "Activate";
                menuItems[1].icon = "toggle-off";
            }
            return menuItems;
        },
    },
    emits: {
        structure: null,
        changestatus: null,
        delete: null,
        sync: null,
    },
    mounted: function () {
        document.addEventListener("click", this.clickOutsideHandler);
    },
    beforeDestroy() {
        document.removeEventListener("click", this.clickOutsideHandler);
    },
    methods: {
        closePopup() {
            this.show = false;
        },
        handleToggle() {
            this.show = !this.show;
        },
        clickOutsideHandler(event) {
            if (this.$refs.popupRef) {
                const popupElement = this.$refs.popupRef.$el;
                if (this.show && !popupElement.contains(event.target)) {
                    this.closePopup();
                }
            }
        },
        handleItemClick(e, index) {
            if (index == 0) {
                this.$emit("structure");
            } else if (index == 1) {
                this.$emit("changestatus");
            } else if (index == 2) {
                this.$emit("delete");
            } else if (index == 3) {
                this.$emit("sync");
            }
        },
    },
};
</script>
<style lang=""></style>
