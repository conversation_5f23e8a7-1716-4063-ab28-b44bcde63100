@php
$currRoute = Route::current()->getName();
$menuTitle = ((isset($mainmenu)) && (!empty($mainmenu))) ? $mainmenu : '';
$sessionArr = session('arrStudentPermissionList');
$currUrl = '/'.Request::path();
@endphp

<aside id="sidebar"  class="sidebar {{ (@($_COOKIE['themeColor']) && ($_COOKIE['themeColor'] == 'light')) ? 'lighttheme' : 'darktheme'}}">
  <div class="sidebar-wrapper">
    <div class="logo-wrapper">
        <a href="{{ route('student_dashboard') }}" class="ml-4 mt-2">
            <img src="{{ asset('v2/img/logo-light.svg') }}"
                     alt="Logo"
                     class="lightSvg">
                <img src="{{ asset('v2/img/logo-dark.svg') }}"
                     alt="Logo"
                     class="darkSvg">
        </a>
        <button type="button" id="menuBtn" title="Menu">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="24" height="24" rx="12" fill="#D1D5DB"/>
                <path d="M10.6667 14.6667L8 12M8 12L10.6667 9.33333M8 12L13.3333 12M16 17.3333V6.66667" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>
    </div>
    <div class="md:flex issidebar md:flex-shrink-0">
      <div class="flex flex-col toogleWidth w-full">
          <div class="aside_wrap flex flex-col h-0 flex-1">
              <div class="relative serachBox text-gray-400 my-4 focus-within:text-gray-400">
                  <div class="absolute inset-y-0 pl-5 flex items-center pointer-events-none">
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                        aria-hidden="true">
                        <path fill-rule="evenodd"
                            d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                            clip-rule="evenodd" />
                    </svg>
                  </div>
                <input id="findStudents" name="search"
                class="menuText w-full block findStudent border border-transparent rounded-md leading-5 bg-gray-400 bg-opacity-25 text-gray-400 focus:outline-none focus:placeholder-gray-400 focus:text-gray-400 sm:text-sm ui-autocomplete-input pb-2 pt-2"
                placeholder="Search" type="text" />
              </div>
              <ul id="verticalMenu"
                        class="menu-list sidebar-menu h-screen overflow-y-auto">
                        @foreach (menuItemsForCurrentUser() as $menuItem)
                            @if (in_array($menuTitle,$menuItem['mainmenu']))
                                <li
                                    class="{{ in_array($currRoute, $menuItem['activeurls']) ? 'activeMainMenu rounded-md open visible' : 'cursor-pointer rounded-md active-li' }} {{ @$menuItem['gap_after'] ? 'gap-after' : '' }} {{ isset($menuItem['sub_menu']) ? 'with-submenu' : ''}}">
                                    <a href="{{ $menuItem['url'] }}">
                                        <div class="flex items-center">
                                            <span class="icon-wrapper tooltip2">
                                                {!! $menuItem['svgicon'] !!}
                                                <span class="tooltiptext text-white">{{ $menuItem['label'] }}</span>
                                            </span>
                                            <span
                                                  class="{{ $currRoute == $menuItem['url'] ? 'activeText' : 'menuText' }} font-normal leading-5">{{ $menuItem['label'] }}</span>
                                        </div>
                                        @if (isset($menuItem['sub_menu']))
                                            <div class="arrow-wrap">
                                                <span><i class="fa fa-angle-right"></i></span>
                                            </div>
                                        @endif
                                    </a>
                                    @if (isset($menuItem['sub_menu']))
                                        <ul class="treeview-menu block rounded-md bg-gray-700 pl-9">
                                            @foreach ($menuItem['sub_menu'] as $subMenuItem)
                                                <li
                                                    class="{{ ((isset($subMenuItem['subactiveurls']) && in_array($currRoute, $subMenuItem['subactiveurls'])) || $currUrl == $subMenuItem['url']) ? 'bg-primary-blue-500 activeSubMenu' : '' }} group flex items-center rounded-md px-1 py-1 text-sm font-medium">
                                                    <a class="submenu-link group flex items-center px-2 py-2 text-sm font-normal leading-5 transition-all duration-100"
                                                       href="{{ $subMenuItem['url'] }}">
                                                        <span
                                                              class="subMenuText font-normal leading-5 group-hover:text-primary-blue-500">{{ $subMenuItem['label'] }}</span>
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                    @endif
                                </li>
                            @endif
                        @endforeach
                    </ul>

              <div class="flex bottom-0 absolute  items-center justify-between px-4 py-2 profileBox rounded-lg">
                    <div class="flex space-x-2 items-center justify-start">
                        <div class="w-8 h-8 rounded-full">
                            <img src="{{ asset('v2/img/user.png') }}" class="" alt="searchIcon" />
                        </div>
                        <div class="menuText inline-flex flex-col items-start justify-start w-36">
                            <span class="menuText font-medium leading-5 text-white">@if(Auth::guard('student')->user()) {{ Auth::guard('student')->user()->name }} @endif  </span>
                            @if(isset($userRole))
                                @foreach($userRole as $key => $value)
                                <span class="menuText leading-none text-gray-300">{{  ($key == $currentRole) ? $value : ''    }}</span>
                                @endforeach
                            @endif
                        </div>
                    </div>
                    <button id="profileMenu"><i class="menuText k-icon k-i-more-horizontal"></i></button>
                </div>
                <div class="inline-flex flex-col items-start justify-start w-60 bg-white shadow border rounded-md profileMenuClass" style="display: none;">
                    <div class="flex flex-col items-start justify-start w-56 px-4 py-3">
                        <span class="text-sm leading-5 text-gray-900">Signed in as</span>
                        <span class="text-sm font-medium leading-5 text-gray-900">{{ !empty(Auth::guard('student')->user()) ?  Auth::guard('student')->user()->email :  '' }}</span>
                    </div>
                    <div class="w-56 h-0.5 bg-gray-100"></div>
                    <div class="inline-flex flex-col space-y-2 items-start justify-center w-60 px-4 py-3">
                        <span class="text-sm leading-5 text-gray-700">Sidebar Theme</span>
                        <div class="inline-flex space-x-2 items-start justify-start">
                            <input type="radio" {{ (Cookie::get('themeColor') == 'dark') ? 'checked' : '' }} id="radio1" name="selector" class="sidebarTheme_radio" value="darkTheme" checked>
                            <label for="radio1" class="cursor-pointer relative flex space-x-2 items-center justify-center py-1 px-4 bg-gray-100 rounded-full sidebarTheme_label">
                                <div class="bg-gray-500 flex h-full items-center justify-center p-1.5 rounded-full">
                                </div>
                                <span class="text-sm leading-5 text-center text-gray-800">Dark</span>
                            </label>
                            <input type="radio" {{ (Cookie::get('themeColor') == 'light') ? 'checked' : '' }}  id="radio2" name="selector" class="sidebarTheme_radio" value="lightTheme">
                            <label for="radio2" class="cursor-pointer flex space-x-2 items-center justify-center py-1 px-4 bg-gray-100 rounded-full sidebarTheme_label">
                                <div class="bg-white flex h-full items-center justify-center p-1.5 rounded-full">
                                </div>
                                <span class="text-sm leading-5 text-center text-gray-800">Light</span>
                            </label>
                        </div>
                    </div>
                    <div class="w-56 h-0.5 bg-gray-100"></div>
                    <div class="flex flex-col items-start justify-start w-full py-1">
                        <div class="inline-flex space-x-3 items-center justify-start w-full px-4 py-2">
                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.60386 2.59776C7.95919 1.13408 10.0408 1.13408 10.3961 2.59776C10.6257 3.54327 11.709 3.99198 12.5398 3.48571C13.8261 2.70199 15.298 4.17392 14.5143 5.46015C14.008 6.29105 14.4567 7.37431 15.4022 7.60386C16.8659 7.95919 16.8659 10.0408 15.4022 10.3961C14.4567 10.6257 14.008 11.709 14.5143 12.5398C15.298 13.8261 13.8261 15.298 12.5398 14.5143C11.709 14.008 10.6257 14.4567 10.3961 15.4022C10.0408 16.8659 7.95919 16.8659 7.60386 15.4022C7.37431 14.4567 6.29105 14.008 5.46016 14.5143C4.17392 15.298 2.70199 13.8261 3.48571 12.5398C3.99198 11.709 3.54327 10.6257 2.59776 10.3961C1.13408 10.0408 1.13408 7.95919 2.59776 7.60386C3.54327 7.37431 3.99198 6.29105 3.48571 5.46015C2.70199 4.17392 4.17392 2.70199 5.46015 3.48571C6.29105 3.99198 7.37431 3.54327 7.60386 2.59776Z" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M11.5 9C11.5 10.3807 10.3807 11.5 9 11.5C7.61929 11.5 6.5 10.3807 6.5 9C6.5 7.61929 7.61929 6.5 9 6.5C10.3807 6.5 11.5 7.61929 11.5 9Z" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <a href="{{ route('user_profile') }}" class="text-sm leading-5 text-gray-700">My Profile</a>
                        </div>
                        <div class="inline-flex space-x-3 items-center justify-start w-full px-4 py-2">
                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.60386 2.59776C7.95919 1.13408 10.0408 1.13408 10.3961 2.59776C10.6257 3.54327 11.709 3.99198 12.5398 3.48571C13.8261 2.70199 15.298 4.17392 14.5143 5.46015C14.008 6.29105 14.4567 7.37431 15.4022 7.60386C16.8659 7.95919 16.8659 10.0408 15.4022 10.3961C14.4567 10.6257 14.008 11.709 14.5143 12.5398C15.298 13.8261 13.8261 15.298 12.5398 14.5143C11.709 14.008 10.6257 14.4567 10.3961 15.4022C10.0408 16.8659 7.95919 16.8659 7.60386 15.4022C7.37431 14.4567 6.29105 14.008 5.46016 14.5143C4.17392 15.298 2.70199 13.8261 3.48571 12.5398C3.99198 11.709 3.54327 10.6257 2.59776 10.3961C1.13408 10.0408 1.13408 7.95919 2.59776 7.60386C3.54327 7.37431 3.99198 6.29105 3.48571 5.46015C2.70199 4.17392 4.17392 2.70199 5.46015 3.48571C6.29105 3.99198 7.37431 3.54327 7.60386 2.59776Z" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M11.5 9C11.5 10.3807 10.3807 11.5 9 11.5C7.61929 11.5 6.5 10.3807 6.5 9C6.5 7.61929 7.61929 6.5 9 6.5C10.3807 6.5 11.5 7.61929 11.5 9Z" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <a href="{{ route('general-info') }}" class="text-sm leading-5 text-gray-700">Settings</a>
                        </div>
                    </div>
                    <div class="w-56 h-0.5 bg-gray-100"></div>
                    <div class="flex flex-col items-start justify-start w-full py-1">
                        <a href="{{ route('user_logout') }}" class="inline-flex items-center justify-start w-full px-4 py-2">
                            <span class="text-sm leading-5 text-red-500">Sign out</span>
                        </a>
                    </div>
                </div>
          </div>
      </div>
    </div>
  </div>
</aside>