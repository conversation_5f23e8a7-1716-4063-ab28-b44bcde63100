<?php

namespace App\Jobs;

use App\Model\v2\ResultImport;
use App\Services\ResultImport\ResultCreator;
use App\Services\ResultImport\ResultImportValidator;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessResultImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $importRecord;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(ResultImport $importRecord)
    {
        $this->importRecord = $importRecord;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $validator = new ResultImportValidator;
        [$errors, $related] = $validator->validate($this->importRecord);

        if (! empty($errors) && $errors != '[]') {
            $this->importRecord->update([
                'status' => 'failed',
                'error_message' => $errors, // Already JSON encoded in the validator
            ]);

            return;
        }

        try {
            $creator = new ResultCreator;
            $creator->create($this->importRecord, $related);

            $this->importRecord->update([
                'status' => 'completed',
                'error_message' => null,
            ]);
        } catch (\Throwable $e) {
            $errorData = [
                [
                    'error_type' => 'system',
                    'error_title' => 'Result Creation Failed',
                    'error_description' => 'Failed to create result: '.$e->getMessage().'.',
                ],
            ];

            $this->importRecord->update([
                'status' => 'failed',
                'error_message' => json_encode($errorData),
            ]);
        }
    }
}
