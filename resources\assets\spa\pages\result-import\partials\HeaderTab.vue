<template>
    <div class="space-y-4">
        <Card
            :pt="{
                root: 'p-4 lg:p-6 rounded-lg',
            }"
        >
            <template #content>
                <div class="flex flex-col space-y-4">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-semibold text-gray-800">Import Results</h2>
                        <div class="flex space-x-2">
                            <Button
                                variant="secondary"
                                class="h-9"
                                @click="downloadSample('highered')"
                            >
                                <file-icon name="csv" class="h-6 w-6 text-gray-500" />
                                <span>Download HigherEd Sample</span>
                            </Button>
                            <Button variant="secondary" class="h-9" @click="downloadSample('vet')">
                                <file-icon name="csv" class="h-6 w-6 text-gray-500" />
                                <span>Download VET Sample</span>
                            </Button>
                            <Button variant="primary" class="h-9" @click="openFileUpload">
                                <icon
                                    :name="'upload'"
                                    :fill="'currentColor'"
                                    :width="'16'"
                                    :height="'16'"
                                />
                                <span>Upload CSV</span>
                            </Button>
                        </div>
                    </div>
                    <div class="text-sm text-gray-600">
                        <p>Upload a CSV file with the following headers:</p>
                        <ul class="mt-2 list-disc pl-5">
                            <li><strong>StudentId</strong> - Required</li>
                            <li><strong>CourseType</strong> - Required (e.g., HigherEd, VET)</li>
                            <li><strong>UnitId</strong> - Required</li>
                            <li><strong>TotalMark</strong> - Optional</li>
                            <li>
                                <strong>FinalOutcome</strong> - Optional (e.g., C, NYC, CT, RPL)
                            </li>
                            <li>
                                <strong>Assessment_*</strong> - Optional (e.g., Assessment_1,
                                Assessment_2)
                            </li>
                        </ul>
                    </div>
                </div>
            </template>
        </Card>

        <div class="flex flex-wrap items-center justify-between gap-2">
            <div>
                <search-input
                    v-model.lazy="filterValues.search"
                    :pt="{ root: 'w-80 h-9' }"
                    placeholder="Search by Student ID or Subject ID"
                    :debounce="300"
                    :autocomplete="'off'"
                />
            </div>
            <div class="flex flex-wrap items-center gap-3">
                <!-- <Button variant="secondary" class="h-9" @click="toggleSortDirection">
                    <icon :name="'sort'" :fill="'currentColor'" :width="'16'" :height="'16'" />
                    <span>{{ sortDirection === 'asc' ? 'Oldest First' : 'Newest First' }}</span>
                </Button>
                <Button variant="secondary" class="h-9" @click="refreshData" :loading="refreshing">
                    <icon :name="'refresh'" :fill="'currentColor'" :width="'16'" :height="'16'" />
                    <span>Refresh</span>
                </Button> -->
                <Button
                    variant="secondary"
                    class="h-9"
                    @click="toggleAutoRefresh"
                    :class="{ 'border-green-500 bg-green-100': autoRefreshEnabled }"
                >
                    <icon
                        :name="autoRefreshEnabled ? 'pause' : 'play'"
                        :fill="'currentColor'"
                        :width="'16'"
                        :height="'16'"
                    />
                    <span>{{
                        autoRefreshEnabled ? 'Stop Auto Refresh' : 'Start Auto Refresh'
                    }}</span>
                </Button>
            </div>
        </div>
    </div>
</template>

<script>
import { debounce } from 'lodash';
import IconInput from '@spa/components/IconInput.vue';
import Button from '@spa/components/Buttons/Button.vue';
import Card from '@spa/components/Card/Card.vue';

export default {
    components: {
        'search-input': IconInput,
        Button,
        Card,
    },
    props: {
        sortDirection: { type: String, default: 'desc' },
        refreshing: { type: Boolean, default: false },
        autoRefreshEnabled: { type: Boolean, default: false },
    },
    data() {
        return {
            filterValues: {
                search: '',
            },
            debouncedEmitFilter: debounce(function (newval) {
                if (newval.search === '') {
                    newval.search = null;
                }
                this.$emit('filter', newval);
            }, 300), // 300ms debounce delay
        };
    },
    watch: {
        'filterValues.search': function () {
            this.debouncedEmitFilter(this.filterValues);
        },
    },
    methods: {
        downloadSample(type) {
            this.$emit('download-sample', type);
        },
        openFileUpload() {
            this.$emit('open-file-upload');
        },
        toggleSortDirection() {
            this.$emit('toggle-sort');
        },
        refreshData() {
            this.$emit('refresh');
        },
        toggleAutoRefresh() {
            this.$emit('toggle-auto-refresh');
        },
    },
};
</script>
