<?php

namespace Domains\Customers\Onboarding\Controllers;

use App\Exceptions\ApplicationException;
use App\Model\v2\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Domains\Customers\Models\CentralUser;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Domains\Customers\Onboarding\DTO\RegisterUserPayload;
use Domains\Customers\Onboarding\DTO\OnboardingUserPayload;
use Domains\Customers\Onboarding\Process\OnboardingProcess;
use Support\DTO\MailAddress;
use Support\Mails\EmailService;

class LandingSiteController
{
    use ValidatesRequests;

    public function home()
    {
        return view('landing.home');
    }

    public function about()
    {
        return view('landing.about');
    }

    public function pricing()
    {
        return view('landing.pricing');
    }

    public function faq()
    {
        return view('landing.faq');
    }
    
    public function features()
    {
        return view('landing.features');
    }

    public function contact()
    {

        return view('landing.contact');
    }

    public function postContact(Request $request)
    {
        $this->validate($request, [
            'name' => 'required',
            'email' => 'required|email',
            'subject' => 'required',
            'message' => 'required',
        ]);


        (new EmailService())
            ->to(new MailAddress($request->email))
            ->subject($request->subject)
            ->send('mail.onboarding.contact', [
                'input' => $request->all()
            ]);


        return redirect()->back()->with(['success' => 'Message Sent']);
    }
}
