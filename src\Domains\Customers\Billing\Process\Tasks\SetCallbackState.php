<?php

namespace Domains\Customers\Billing\Process\Tasks;

use App\Exceptions\ApplicationException;
use App\Model\v2\Tenant;
use Closure;
use Domains\Customers\Billing\DTO\ShortCourseCheckoutPayload;
use Domains\Customers\Billing\Models\StudentGalaxyInvoice;
use Illuminate\Support\Facades\Cache;

class SetCallbackState
{
    public function __invoke(ShortCourseCheckoutPayload $payload, Closure $next)
    {
        $user = $payload->request->user();
        
        $domain = $this->getDefaultShortCourseDomain($payload->request);

        /* TODO: get course here */
        $course = @$payload->model->course;
        if (!$course) {
            throw new ApplicationException("Invalid request payload. Course missing.");
        }

        /* get any pending invoice */
        $invoice = StudentGalaxyInvoice::GetAllPendingInvoices($payload->model);
        /* If there are no pending invoice, then generate one */
        if(!$invoice){
            $invoice = StudentGalaxyInvoice::CreateFromStudentCourses($payload->model, $user->id);
        }else{
            $invoice->amount = (@$payload->model->getTotalPayableFee() ?? 0) * 100;
            $invoice->save();
        }

        $payload->state = $invoice->uuid;
        $payload->invoice = $invoice; 
        /* TODO: a helper service to generate cache key from user and course */

        // $payload->state = $user->getUUID() . '_scp_' . $course->course_code;

        // Cache::driver('file')
        //     ->remember($payload->state, 60, function () use ($payload, $domain) {
        //         return $domain;
        //     });

        return $next($payload);
    }


    public function getDefaultShortCourseDomain($request)
    {
        $host = @$request->input("domain") ?? "";
        
        $tenant = ($host) ? Tenant::GetTenantInfoByAllowedDomain($host) : null;
        $url = ($tenant) ? $tenant->GetApiBaseurl() : null;

        $parsed = parse_url($url);
        
        return $parsed['host'] ?? null;
    }
}
