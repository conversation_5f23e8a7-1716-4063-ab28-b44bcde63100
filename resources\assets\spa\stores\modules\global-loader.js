import { defineStore } from "pinia";

export const useLoaderStore = defineStore("loader", {
    state: () => ({
        isLoading: false, // Global loader state
        contextLoaders: {}, // Context-specific loaders
    }),
    actions: {
        startLoading() {
            this.isLoading = true;
        },
        stopLoading() {
            this.isLoading = false;
        },
        startContextLoading(context) {
            this.contextLoaders[context] = true;
        },
        stopContextLoading(context) {
            this.contextLoaders[context] = false;
        },
        isContextLoading(context) {
            this.isLoading = false;
            return !!this.contextLoaders[context];
        },
    },
});
