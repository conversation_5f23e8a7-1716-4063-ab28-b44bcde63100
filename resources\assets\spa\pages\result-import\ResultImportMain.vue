<template>
    <Layout :noSpacing="true" :loading="loading">
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Result Import" :back="false" />
        </template>
        <div class="space-y-6 px-8 py-6">
            <!-- Header Tab with Import Options and Filters -->
            <HeaderTab
                :sortDirection="sortDirection"
                :refreshing="refreshing"
                :autoRefreshEnabled="autoRefreshEnabled"
                @download-sample="downloadSample"
                @open-file-upload="openFileUpload"
                @toggle-sort="toggleSortDirection"
                @refresh="refreshData"
                @filter="handleFilter"
                @toggle-auto-refresh="toggleAutoRefresh"
            />

            <!-- Grid Component -->
            <ResultImportGrid
                :data="resource.state.imports.data || []"
                :pagination="{
                    skip: (resource.state.pageable.currentPage - 1) * perPage,
                    take: perPage,
                    total: resource.state.pageable.totalItems || 0,
                    pageSizes: resource.state.pageable.pageSizes,
                }"
                :sort="[{ field: 'created_at', dir: sortDirection }]"
                @sort="handleSort"
                @changepage="handlePageChange"
                @changepagesize="handlePageSizeChange"
                @view-details="viewDetails"
                @resync="resyncImport"
                @delete="confirmDelete"
                @edit="editRecord"
                @view-error="handleViewError"
            />

            <!-- Hidden File Input -->
            <input
                type="file"
                ref="fileInput"
                @change="handleFileUpload"
                accept=".csv"
                class="hidden"
            />
        </div>

        <!-- Details Modal -->
        <DetailsModal
            v-model:visible="detailsModalVisible"
            :importData="selectedImport"
            @resync="resyncImport"
        />

        <!-- Delete Confirmation Modal -->
        <DeleteModal
            v-model:visible="deleteModalVisible"
            :importData="importToDelete"
            :loading="deleting"
            @delete="deleteImport"
        />

        <!-- Edit Modal -->
        <EditModal
            v-model:visible="editModalVisible"
            :importData="importToEdit"
            :saving="saving"
            @save="saveEditedRecord"
        />

        <!-- Error Modal -->
        <ErrorModal v-model:visible="errorModalVisible" :errors="currentErrorMessages" />
    </Layout>
</template>

<script>
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';

// Import custom components
import HeaderTab from './partials/HeaderTab.vue';
import ResultImportGrid from './partials/ResultImportGrid.vue';
import DetailsModal from './partials/DetailsModal.vue';
import DeleteModal from './partials/DeleteModal.vue';
import EditModal from './partials/EditModal.vue';
import ErrorModal from './partials/ErrorModal.vue';

// Import resource
import useResultImportResource from '@spa/services/result-import/resultImportResource.js';

export default {
    components: {
        Layout,
        PageTitleContent,
        HeaderTab,
        ResultImportGrid,
        DetailsModal,
        DeleteModal,
        EditModal,
        ErrorModal,
    },
    // No props needed as we're using the resource state directly
    setup() {
        const loaderStore = useLoaderStore();
        const resource = useResultImportResource('spa/result-import', {
            filters: {
                search: '',
                take: 10,
                page: 1,
            },
        });

        return {
            loaderStore,
            resource,
        };
    },
    data() {
        return {
            fileInput: null,
            autoRefreshInterval: null,
            autoRefreshEnabled: false,
            errorModalVisible: false,
            currentErrorMessages: null,
        };
    },
    mounted() {
        this.fileInput = this.$refs.fileInput;
        this.resource.fetch();
    },
    beforeUnmount() {
        // Clear the auto-refresh interval when component is unmounted
        this.stopAutoRefresh();
    },
    computed: {
        sortDirection() {
            return this.resource.state.dir || 'desc';
        },
        perPage() {
            return this.resource.state.pageable.pageSizeValue;
        },
        detailsModalVisible: {
            get() {
                return this.resource.state.detailsModalVisible;
            },
            set(value) {
                this.resource.state.detailsModalVisible = value;
            },
        },
        deleteModalVisible: {
            get() {
                return this.resource.state.deleteModalVisible;
            },
            set(value) {
                this.resource.state.deleteModalVisible = value;
            },
        },
        selectedImport() {
            return this.resource.state.selectedImport;
        },
        importToDelete() {
            return this.resource.state.importToDelete;
        },
        refreshing() {
            return this.resource.state.refreshing;
        },
        deleting() {
            return this.resource.state.deleting;
        },
        editModalVisible: {
            get() {
                return this.resource.state.editModalVisible;
            },
            set(value) {
                this.resource.state.editModalVisible = value;
            },
        },
        importToEdit() {
            return this.resource.state.importToEdit;
        },
        saving() {
            return this.resource.state.saving;
        },
    },
    methods: {
        // Refresh data
        refreshData() {
            this.resource.refreshData();
        },

        // Toggle sort direction
        toggleSortDirection() {
            this.resource.toggleSortDirection();
        },

        // Handle sort change
        handleSort(sort) {
            this.resource.handleSort(sort);
        },

        // Handle page change
        handlePageChange(event) {
            this.resource.handlePageChange(event);
        },

        // Handle filter change
        handleFilter(filters) {
            this.resource.handleFilter(filters);
        },

        // Open file upload dialog
        openFileUpload() {
            this.fileInput.click();
        },

        // Handle file upload
        async handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            try {
                await this.resource.uploadFile(file);
                // Start auto-refresh after successful upload
                this.startAutoRefresh();
            } finally {
                // Reset file input
                event.target.value = '';
            }
        },

        // Download sample CSV
        downloadSample(type) {
            this.resource.downloadSample(type);
        },

        // View import details
        viewDetails(importData) {
            this.resource.viewDetails(importData);
        },

        // Confirm delete
        confirmDelete(importData) {
            this.resource.confirmDelete(importData);
        },

        // Delete import
        deleteImport() {
            this.resource.deleteImport();
        },

        // Resync import
        resyncImport(importData) {
            this.resource.resyncImport(importData);
        },

        // Edit record
        editRecord(importData) {
            this.resource.editRecord(importData);
        },

        // Save edited record
        saveEditedRecord(data) {
            this.resource.saveEditedRecord(data);
        },

        // Handle view error
        handleViewError(errors) {
            console.log('errors', errors);
            this.currentErrorMessages = errors;
            this.errorModalVisible = true;
        },

        // Start auto refresh
        startAutoRefresh() {
            if (!this.autoRefreshEnabled) {
                this.autoRefreshEnabled = true;
                this.autoRefreshInterval = setInterval(() => {
                    this.resource.fetch({}, false);
                }, 10000); // Refresh every 10 seconds
            }
        },

        // Stop auto refresh
        stopAutoRefresh() {
            if (this.autoRefreshInterval) {
                clearInterval(this.autoRefreshInterval);
                this.autoRefreshInterval = null;
            }
            this.autoRefreshEnabled = false;
        },

        // Toggle auto refresh
        toggleAutoRefresh() {
            if (this.autoRefreshEnabled) {
                this.stopAutoRefresh();
            } else {
                this.startAutoRefresh();
            }
        },
    },
};
</script>
