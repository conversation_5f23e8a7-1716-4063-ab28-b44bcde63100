<template>
    <FormElement class="flex h-full flex-col justify-between space-y-6">
        <div class="space-y-6">
            <Card :pt="{
                root: 'max-w-full mx-auto p-0 md:p-0 shadow-md rounded-lg border border-gray-300 mx-8 lg:mx-20',
                header: 'flex items-center justify-between px-6 py-4 border-b border-gray-200',
                content: 'pb-4 pt-3 px-6',
            }">
                <template #header>
                    <h2 class="text-lg font-semibold text-gray-900">
                        {{ title }}
                    </h2>
                    <div class="flex items-center gap-2">
                        <p class="text-xxs">
                            Accepted files:
                            <span class="font-bold">PDF and DOCX</span>
                        </p>
                        <div class="h-2 w-px bg-gray-300"></div>
                        <p class="text-xxs">
                            Max Size:
                            <span class="font-bold">1 MB</span>
                        </p>
                    </div>
                </template>
                <template #content>
                    <div class="space-y-4">
                        <div class="custom-uploader rounded-lg border border-gray-200">

                            <PreviewPane :show="showPreview" @close-preview="handlePreviewClose"
                                @show-preview="handleViewDetail" :documentName="documentName" :files="gridData"
                                v-model:documentName="documentName" @next="handleNextClick" @prev="handlePrevClick">
                                <template #content>
                                    <table class="h-fit w-full table-auto">
                                        <thead>
                                            <tr class="bg-gray-50">
                                                <th
                                                    class="h-10 w-80 border-b border-gray-200 px-4 py-2 text-left"
                                                >
                                                    <span
                                                        class="text-left text-xs font-normal uppercase leading-[0.875rem] text-gray-600">Document
                                                        Name</span>
                                                </th>
                                                <th class="h-10 border-b border-gray-200 px-4 py-2 text-left">
                                                    <span
                                                        class="text-left text-xs font-normal uppercase leading-[0.875rem] text-gray-600">File
                                                        Name</span>
                                                </th>
                                                <th
                                                    class="h-10 w-32 border-b border-gray-200 px-4 py-2 text-left"
                                                >
                                                    <span
                                                        class="text-left text-xs font-normal uppercase leading-[0.875rem] text-gray-600">Action</span>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <template v-for="(field, index) in this
                                                .data.agentChecklistData" :key="index">
                                                <tr>
                                                    <td class="border-b border-gray-200 px-4 py-3">
                                                        <span class="text-sm font-medium leading-5 text-gray-800">
                                                            {{
                                                                field.document_name
                                                            }}
                                                            <sup class="text-red-500">*</sup>
                                                        </span>
                                                    </td>
                                                    <td class="border-b border-gray-200 px-4 py-3">
                                                        <Field :id="field.id" :name="field.id" :label="'Upload Photos'" :hintMessage="'Hint: Select your additional photos'" :component="'myTemplate'" :validator="field.is_compulsory ? validateRequiredFile : null " :multiple="false" :autoUpload="false" @add="
                                                                handleOnAdd($event,field,index)" @remove="
                                                                handleOnRemove($event,field,)">
                                                            <template v-slot:myTemplate="{
                                                                props,
                                                            }">
                                                                <FileUploader v-bind="props" @change="props.onChange" @blur="props.onBlur" @focus="props.onFocus" :value="field.uploads" :restrictions="{allowedExtensions:
                                                                            [
                                                                                '.pdf',
                                                                                '.docx',
                                                                            ],
                                                                        maxFileSize: 1000000,
                                                                    }"
                                                                    :defaultFiles="
                                                                        field.uploads
                                                                    "
                                                                    :buttonLabel="'Select File'"
                                                                    :isCustomUploader="
                                                                        false
                                                                    "
                                                                    :isPadding="
                                                                        true
                                                                    "
                                                                    :isAgencyUploadForm="
                                                                        true
                                                                    "
                                                                    :iconName="'download_arrow_up'"
                                                                />
                                                            </template>
                                                        </Field>
                                                    </td>
                                                    <td class="border-b border-gray-200 px-4 py-3">
                                                        <span class="flex items-center gap-2">
                                                            <Tooltip :anchor-element="'target'" :position="'top'"
                                                                :parentTitle="true
                                                                    " :tooltipClassName="'flex !p-1.5'" :class="'w-full'"
                                                                v-if="
                                                                    actions[0]
                                                                        .value
                                                                ">
                                                                <Button v-if="field.uploads && field.uploads.length > 0" :variant="'text'" class="h-fit text-gray-400" :title="actions[0].text" :ref="!actions[0].value? 'actionMenu': ''" @click="handleViewDetail(field,)">
                                                                    <file-icon :name="actions[0].icon" />
                                                                </Button>
                                                            </Tooltip>
                                                        </span>
                                                    </td>
                                                </tr>
                                            </template>
                                            <template v-for="(field, index) in formFields" :key="index">
                                                <tr>
                                                    <td class="border-b border-gray-200 px-4 py-3">
                                                        <Field :id="'document_name_' +index" :name="'document_name_' +index" :component="'agencyNameTemplate'" :orientation="'horizontal'" :validator="requiredtrue" :pt="getFieldClass"   :placeholder="'Document Name'">
                                                            <template #agencyNameTemplate="{props}">
                                                                <FormInput v-bind="props"

                                                                     @change="props.onChange"
                                                                     @blur="props.onBlur"
                                                                    @focus="props.onFocus" />
                                                            </template>
                                                        </Field>
                                                    </td>
                                                    <td class="border-b border-gray-200 px-4 py-3">
                                                        <Field :id="field.id" :name="field.id" :label="'Upload Photos'"
                                                            :hintMessage="'Hint: Select your additional photos'"
                                                            :component="'myTemplate'" :validator="field.is_compulsory
                                                                    ? validateRequiredFile
                                                                    : null
                                                                " :multiple="false" :autoUpload="false" @add="
                                                                handleOnAdd(
                                                                    $event,
                                                                    field,index
                                                                )
                                                                " @remove="
                                                                handleOnRemove(
                                                                    $event,
                                                                    field,
                                                                )
                                                                ">
                                                            <template v-slot:myTemplate="{
                                                                props,
                                                            }">
                                                                <FileUploader :showNotList="true"  v-bind="props
                                                                    " @change="
                                                                        props.onChange
                                                                    " @blur="
                                                                        props.onBlur
                                                                    " @focus="
                                                                        props.onFocus
                                                                    " :value="field.uploads
                                                                        " :restrictions="{
                                                                        allowedExtensions:
                                                                            [
                                                                                '.pdf',
                                                                                '.docx',
                                                                            ],
                                                                        maxFileSize: 1000000,
                                                                    }" :defaultFiles="field.uploads
                                                                        " :buttonLabel="'Select File'" :isCustomUploader="false
                                                                        " :isPadding="true
                                                                        " />
                                                            </template>
                                                        </Field>
                                                    </td>
                                                    <td class="border-b border-gray-200 px-4 py-3">
                                                        <span class="flex items-center gap-2">
                                                            <template v-for="(button, key) in actions" :key="key">
                                                                <Tooltip :anchor-element="'target'" :position="'top'"
                                                                    :parentTitle="true
                                                                        " :tooltipClassName="'flex !p-1.5'"
                                                                    :class="'w-full'" v-if="
                                                                        button.value
                                                                    ">
                                                                    <Button
                                                                            :variant="'text'"
                                                                            class="h-fit text-gray-400"
                                                                            :title="button.text"
                                                                            :ref="!button.value ? 'actionMenu' : '' "
                                                                            @click="handleAction(index)">
                                                                        <file-icon :name="button.icon" :class="button.text =='Delete'? 'text-red-500': ''" />
                                                                    </Button>
                                                                </Tooltip>
                                                            </template>
                                                        </span>
                                                    </td>
                                                </tr>
                                            </template>
                                        </tbody>
                                    </table>
                                </template>
                                <template #preview>
                                    <div class="custom-pdf p-8 px-0 pb-0 pt-0">
                                        <div class="tw-filemanager tw-media-manager !h-full !border-0" ref="previewPdf">
                                        </div>
                                    </div>
                                    <div class="block md:hidden">
                                        <PreviewDocumentModal :file="file" :isUrl="true" :visible="showPreview"
                                            :width="'90%'" @cancel="handlePreviewClose" />
                                    </div>
                                </template>
                            </PreviewPane>
                        </div>

                    </div>
                </template>
            </Card>

            <Card
            :pt="{
                root: 'max-w-full mx-auto p-0 md:p-0 shadow rounded-lg border border-gray-300 mx-8 lg:mx-20',
                header: 'flex items-center justify-between px-6 py-4 border-b border-gray-200',
                content: 'py-4 px-6',
            }"
        >
            <template #header>
                <h2 class="text-lg font-semibold text-gray-900">
                    Upload Extra documents
                    <sub class="ml-2 text-xs font-normal text-gray-500"
                        ><em>* Not Mandatory</em></sub
                    >
                </h2>
                <div class="flex items-center gap-2">
                    <p class="text-xxs">
                        Accepted files:
                        <span class="font-bold">PDF and DOCX</span>
                    </p>
                    <div class="h-2 w-px bg-gray-300"></div>
                    <p class="text-xxs">
                        Max Size:
                        <span class="font-bold">1 MB</span>
                    </p>
                </div>
            </template>
            <template #content>
                <div>
                    <HighlightBox :variant="'info'" :class="'mb-4'">
                        <template #icon>
                            <icon :name="'info-help'" />
                        </template>
                        <template #content>
                            <p class="text-primary-blue-600">
                                Please ensure the file name is clear and
                                descriptive of its content (e.g.,
                                "Contract_2025.pdf"), avoiding random names like
                                "xyz.pdf" or "file123.doc".
                            </p>
                        </template>
                    </HighlightBox>
                    <Field
                        :id="'other'"
                        :name="other"
                        :label="'Upload Photos'"
                        :hintMessage="'Hint: Select your additional photos'"
                        :component="'myTemplate'"
                        :validator="null"
                        :multiple="false"
                        :autoUpload="false"
                        @add="handleExtraDocumentOnAdd($event, field)"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <ExtraFileUploader
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                                :restrictions="{
                                    allowedExtensions: ['.pdf', '.docx'],
                                    maxFileSize: 1000000,
                                }"
                                :buttonLabel="'Upload File'"
                                :isCustomUploader="false"
                                :isPadding="false"
                                :isAgencyUploadForm="false"
                                :iconName="'download_arrow_up'"
                                :variant="'dropzone'"
                                :class="'custom-dropzone-uploader'"
                                :linkedMsg="'Upload files'"
                                :noteMsg="'PDF, WORD, JPG, PNG up to 20MB'"
                                :hintMsg="'or drag and drop'"
                            />
                        </template>
                    </Field>
                    <div class="mt-4 grid grid-cols-4 gap-1" v-if="data?.agentExtraDocumentData?.length">
                    <div
                        v-for="doc in data?.agentExtraDocumentData"
                        :key="doc.id"
                        :class="chipClass"
                        class="inline-flex items-center gap-2 border bg-white p-0.5 ps-2 pe-1 text-sm leading-5 text-gray-700 shrink-0 chip-custom-style rounded-lg h-[31.35px] border-gray-300"
                    >
                        <file-icon
                            :name="getIconName(doc.extension)"
                            width="28"
                            height="28"
                        />
                        <!-- FIX: Truncate text properly -->
                        <div class="flex-1 overflow-hidden">
                            <span class="block truncate max-w-full">{{ doc.original_name }}</span>
                        </div>
                        <icon-dismiss class="h-4 w-4 cursor-pointer stroke-2 text-gray-400 hover:text-red-600"  @click="handleExtraDocumentOnRemove(doc.id)" />
                    </div>
                </div>
                </div>
            </template>
        </Card>
            <div class="mx-8 w-fit rounded-lg border border-gray-200 bg-white px-6 py-5 shadow-md lg:mx-20">
                <Field  :id="'isAgreeTerms'" :name="'isAgreeTerms'" :component="'abnTemplate'"
                    @change="handleIsAgreeTerms"
                    :orientation="'horizontal'"  :validator="requiredtrue" class="!flex items-center gap-2"
                    :placeholder="'Website'" :isCheckFirst="true" :pt="{
                        label: '!text-blue-500',
                    }">
                   <template #abnTemplate="{ props }">
                    <div class="flex items-center gap-2">
                        <FormCheckbox v-bind="props" @change="props.onChange" @blur="props.onBlur" @focus="props.onFocus" />
                            <span class="text-sm">
                                I agree to the
                            <span class="text-blue-500 underline cursor-pointer" @click="fetchTerms">
                                Terms and Conditions
                            </span>
                                and
                            <span class="text-blue-500 underline cursor-pointer" @click="fetchPrivacy">
                                Privacy Policy
                            </span>
                            </span>
                    </div>
                    </template>

                </Field>
            </div>
        </div>
        <NavigationButtonGroup :isTermsChecked="isTermsChecked" @checkValidation="checkValidation" @prev="goBack" @next="skipNext" :isNotSubmit="true" />
    </FormElement>
    <KWindow v-if="visibleWindow" :title="'Status'" @close="toggleWindow">
      Additional info - you can drag and resize the me!
    </KWindow>
<!-- Terms Modal -->
<div v-if="showTermsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-white p-6 rounded shadow-lg max-w-xl w-full">
    <h2 class="text-lg font-bold mb-4">Terms and Conditions</h2>
    <div class="text-sm text-gray-700 max-h-[60vh] overflow-y-auto" v-html="termsContent"></div>
    <button class="mt-4 text-blue-500" @click="showTermsModal = false">Close</button>
  </div>
</div>

<!-- Privacy Modal -->
<div v-if="showPrivacyModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-white p-6 rounded shadow-lg max-w-xl w-full">
    <h2 class="text-lg font-bold mb-4">Privacy Policy</h2>
    <div class="text-sm text-gray-700 max-h-[60vh] overflow-y-auto" v-html="privacyContent"></div>
    <button class="mt-4 text-blue-500" @click="showPrivacyModal = false">Close</button>
  </div>
</div>
</template>

<script>
import FileUploader from "@spa/components/Uploader/FileUploader.vue";
import ExtraFileUploader from "@spa/components/Uploader/ExtraFileUploader.vue";
import FormCheckbox from "@spa/components/KendoInputs/FormCheckbox.vue";
import { Field, FormElement } from "@progress/kendo-vue-form";
import Button from "@spa/components/Buttons/Button.vue";
import Card from "@spa/components/Card/Card.vue";
import apiClient from "@spa/services/api.client";
import { usePage } from "@inertiajs/vue3";
import NavigationButtonGroup from "@spa/modules/agency/add-agency/NavigationButtonGroupV2.vue";
import FormInput from "@spa/components/KendoInputs/FormInput.vue";
import { Tooltip } from "@progress/kendo-vue-tooltip";
import PreviewPane from "@spa/components/KendoGrid/PreviewPane.vue";
import { router } from '@inertiajs/vue3';
import HighlightBox from "@spa/components/HighlightBox/HighlightBox.vue";
import { Dialog, DialogActionsBar, Window } from "@progress/kendo-vue-dialogs";
import {
    IconDismiss20Regular
} from "@iconify-prerendered/vue-fluent";
import axios from 'axios';

export default {
    props: {
        nextStep: Function, // Accepts nextStep as a prop
        prevStep: Function, // Accepts prevStep as a prop
        skipNextStep: Function, // Accepts prevStep as a prop
        title: String,
        data: { type: Object, default: [] },
    },
    components: {
        FileUploader,
        ExtraFileUploader,
        FormCheckbox,
        Field,
        FormElement,
        Button,
        Card,
        NavigationButtonGroup,
        FormInput,
        Tooltip,
        PreviewPane,
        HighlightBox,
        KWindow: Window,
        "icon-dismiss": IconDismiss20Regular,

    },
    inject: {
        kendoForm: { default: {} },
    },
    data() {
        return {
            isTermsChecked: false,
            formFields: [],
            showTermsModal: false,
            showPrivacyModal: false,
            termsContent: '',
            privacyContent: '',
            actions: [
                {
                    text: "Preview Document",
                    value: "preview",
                    icon: "eye",
                },
                {
                    text: "Delete",
                    value: "delete",
                    icon: "trash",
                    isMore: true,
                },
            ],
            // replace with actual data
            gridData: [
                {
                    selected: true,
                    document_name: "FIle 1",
                    id: 1,
                },
                {
                    selected: false,
                    document_name: "FIle 2",
                    id: 2,
                },
            ],
            showPreview: false,
            documentName: "",
            isUrl: true,
        };
    },
    mounted() {
        this.initializeFormValues();
    },
    methods: {
        async fetchTerms() {
            try {
                const res = await axios.get('/terms-and-condition/agent', {
                    headers: {
                    Accept: 'application/json',
                    },
                });
                console.log(res); // check what's in here
                this.termsContent = res.data.content;
                this.showTermsModal = true;
                } catch (error) {
                console.error('Fetch Terms Failed:', error.response || error.message);
                }
        },
        async fetchPrivacy() {
            try {
                    const res = await axios.get('/privacy-policy/agent', {
                        headers: {
                        Accept: 'application/json',
                        },
                    });
                    console.log(res); // check what's in here
                    this.privacyContent = res.data.content;
                    this.showPrivacyModal = true;
                } catch (error) {
                    console.error('Fetch Terms Failed:', error.response || error.message);
                }
        },
        getIconName(extension) {
            console.log("Files");
            let extensionMapping = {
                ".pdf": "pdf",
                ".xlsx": "xlsx",
                ".png": "image",
                ".jpg": "image",
                ".jpeg": "image",
                ".docx": "docx",
            };
            return extensionMapping[extension] || "pdf";
        },
        initializeFormValues() {
            this.data.agentChecklistData.forEach((field) => {
                if (field.uploads && field.uploads.length > 0) {
                    this.kendoForm.onChange(field.id, { value: field.uploads });
                }
            });
        },
        checkValidation() {
            if (this.kendoForm.valid) {
                this.goNext();
            }
        },
        handleIsAgreeTerms(event) {
            this.isTermsChecked = event.value;
        },
        validateRequiredFile(value) {
            return value && value.length > 0 ? "" : "A file is required!";
        },
        handleInputChange(e, value) {
                    // this.kendoForm.onChange(e.target.name, {
                    //     value: e.value,
                    // });
        },
        async  handleExtraDocumentOnAdd(event, field) {
            const file = event.newState[0]?.getRawFile(); // Get raw file

            if (!file) {
                console.error("No file found.");
                return;
            }

            if (this.kendoForm && typeof this.kendoForm.onChange === "function") {
                setTimeout(() => {
                    this.kendoForm.onChange('extra_documents', {
                        value: file.name,
                    });
                }, 2000);
                console.log("Updated form value:", );
            } else {
                console.error("kendoForm or onChange function is missing.");
            }


            const formData = new FormData();
            formData.append("file", file);
            formData.append("document_name", this.kendoForm.values.extra_documents);
            formData.append("agentId", this.data?.agentData?.id);

            try {
                const response = await apiClient.post(
                    "api/save-agent-extra-documents",
                    formData,
                    {
                        headers: {
                            "Content-Type": "multipart/form-data",
                        },
                    },
                );

                console.log("Upload success:", response.data);
                router.reload({ only: ['data'] });

            } catch (error) {
                console.error("Upload error:", error);
            }
        },
        async handleExtraDocumentOnRemove(id){
            const formData = new FormData();
            formData.append("id", id);
            try {
                const response = await apiClient.post(
                    "api/remove-agent-application-extra-documents",
                    formData,
                    {
                        headers: {
                            "Content-Type": "multipart/form-data",
                        },
                    },
                );
                router.reload({ only: ['data'] });
                console.log("Upload success:", response.data);
            } catch (error) {
                console.error("Upload error:", error);
            }
        },
        handleOnAdd(event, field,index) {
            const file = event.newState[0]?.getRawFile(); // Get raw file

            if (!file) {
                console.error("No file found.");
                return;
            }
            field.document_name = field.document_name ?? this.kendoForm.valueGetter("document_name_" + index);
            console.log(`File added for ${field.id}:`, file);
            // Assign file to the form field
            if (this.kendoForm && typeof this.kendoForm.onChange === "function") {
                setTimeout(() => {
                    this.kendoForm.onChange(field.id, {
                        value: file.name,
                    });
                }, 2000);
                console.log("Updated form value:", this.kendoForm.values);
            } else {
                console.error("kendoForm or onChange function is missing.");
            }

            // Call function to upload the document
            this.handleUploadDocumentsForm(file, field);
        },
        handleOnRemove(event, field) {
            const file = event.affectedFiles[0];

            if (!file) {
                console.error("No file found.");
                return;
            }

            console.log("hewe", file);
            this.handleRemoveDocumentsForm(file, field);
            // Optionally, handle any additional logic for file removal
        },
        handleAction(index) {
            const preservedValues = { ...this.kendoForm.values };
            this.formFields.splice(index, 1);
            const newValues = {};
            this.formFields.forEach((field, i) => {
                const oldKey = Object.keys(field)[0]; // Get original key
                const newKey = `document_name_${i}`;

                newValues[newKey] = preservedValues[oldKey] ?? "";
            });

            this.kendoForm.values = newValues;
        },
        async handleUploadDocumentsForm(file, field) {
            const formData = new FormData();
            formData.append("file", file);
            formData.append("checklist_id", field.id);
            formData.append("document_name", field.document_name);
            formData.append("is_compulsory", field.is_compulsory);
            formData.append("agentId", this.data?.agentData?.id);

            try {
                const response = await apiClient.post(
                    "api/save-agent-application-documents",
                    formData,
                    {
                        headers: {
                            "Content-Type": "multipart/form-data",
                        },
                    },
                );

                console.log("Upload success:", response.data);
                // Refresh the state after successful upload
                // this.refreshState();
                router.reload({ only: ['data'] });

            } catch (error) {
                console.error("Upload error:", error);
            }
        },
        async handleRemoveDocumentsForm(file, field) {
            console.log(field);
            const formData = new FormData();
            formData.append("checklist_id", field.id);
            formData.append("agent_id", this.data?.agentData?.id);

            try {
                const response = await apiClient.post(
                    "api/remove-agent-application-documents",
                    formData,
                    {
                        headers: {
                            "Content-Type": "multipart/form-data",
                        },
                    },
                );

                console.log("Upload success:", response.data);
            } catch (error) {
                console.error("Upload error:", error);
            }
        },
        goNext() {
            this.nextStep();
        },
        skipNext() {
            this.nextStep();
        },
        goBack() {
            this.prevStep();
        },
        handleAddMore() {
            const nextIndex = this.formFields.length;
            const newNameField = { [`document_name_${nextIndex}`]: "" };
            const newEmailField = { [`upload_${nextIndex}`]: "" };

            this.formFields.push({
                [`document_name_${nextIndex}`]: "",
                [`upload_${nextIndex}`]: "",
                is_compulsory: false,
            });
            this.kendoForm.values = {
                ...this.kendoForm.values,
                ...newNameField,
                ...newEmailField,
            };
        },
        getActions(dataItem) {
            let actionsArr = this.actions.map((action, index) => {
                // if (["rename", "move", "delete"].includes(action.value)) {
                return {
                    ...action,
                    show: !dataItem.is_compulsory, // Apply conditional logic
                };
                // }
                return { ...action, show: true };
            });

            return actionsArr;
        },
        getName(data) {
            return (this.documentName = data.document_name + "-" + data.id);
        },
        handleViewDetail(item) {
            console.log(item.uploads);
            this.getName(item);
            this.dataItem = item;
            this.showPreview = true;
            // const urlPreview = route("agent_preview_offer_letter_pdf_new", [
            //     (item.course_id = 1),
            //     (item.student_id = 2),
            //     (item.student_course_id = 3),
            // ]);
            // replace with actual url
            // const urlPreview ="http://local.galaxy360.test/uploads/3/college_marterials/1740649998-8IG7UUdpxfrVBRmx-X6o9dUbO4Kex9isC.pdf";
            const urlPreview = item.uploads[0].file_path;
            this.previewDocument(urlPreview);
        },
        handlePreviewClose() {
            this.showPreview = false;
        },
        previewDocument(item) {
            console.log("FileUrl", item);
            this.$nextTick(() => {
                const componentRef = this.$refs.previewPdf;

                if (!componentRef) {
                    console.error("studentDocument ref is undefined.");
                    return;
                }

                const fileUrl = this.isUrl
                    ? encodeURI(item)
                    : encodeURI(window[item]);

                this.file = fileUrl;
                console.log("FIle>>", this.file);

                const existingPdfViewer =
                    $(componentRef).data("kendoPDFViewer");
                console.log("Exi", existingPdfViewer);
                if (existingPdfViewer) {
                    existingPdfViewer.destroy();
                    $(componentRef).empty(); // Clear DOM to prevent duplicate instances
                }

                $.when(
                    $.getScript(
                        "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.js",
                    ),
                    $.getScript(
                        "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js",
                    ),
                )
                    .done(() => {
                        window.pdfjsLib.GlobalWorkerOptions.workerSrc =
                            "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js";
                    })
                    .then(() => {
                        $(componentRef)
                            .kendoPDFViewer({
                                pdfjsProcessing: {
                                    file: fileUrl,
                                },
                                width: "100%",
                                height: "100%",
                            })
                            .data("kendoPDFViewer");
                    });
            });
        },
        handlePreviewChange(item) {
            console.log('handlePreviewChange', item.uploads);
            setTimeout(() => {
                const urlPreview =
                    "http://local.galaxy360.test/uploads/3/college_marterials/1740649998-8IG7UUdpxfrVBRmx-X6o9dUbO4Kex9isC.pdf";
                this.previewDocument(
                    // route("agent_preview_offer_letter_pdf_new", [
                    //     item.course_id,
                    //     item.student_id,
                    //     item.student_course_id,
                    // ]),
                    urlPreview,
                );
            });
        },
        handleNextClick(item) {
            this.handlePreviewChange(item);
        },
        handlePrevClick(item) {
            this.handlePreviewChange(item);
        },
    },
};
</script>
<style lang=""></style>
