<?php

namespace App\Http\Livewire\Onboarding;

use Exception;
use Livewire\Component;
use App\Model\v2\Tenant;
use Illuminate\Support\Str;
use App\Rules\UniqueSubDomain;
use App\Rules\HcaptchaValidator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use App\Exceptions\ApplicationException;
use Illuminate\Validation\ValidationException;
use Domains\Customers\Onboarding\DTO\RegisterUserPayload;
use Domains\Customers\Onboarding\DTO\OnboardingUserPayload;
use Domains\Customers\Onboarding\Process\OnboardingProcess;

class RegisterUser extends Component
{

    public $plan = null;
    private OnboardingProcess $process;

    public $form = [
        'name' => '',
        'college_name' => '',
        'username' => '',
        'email' => '',
        // 'password' => '',
        // 'password_confirmation' => '',
        'domain' => '',
        'captcha' => '',
    ];


    protected $validationAttributes = [
        'form.name' => 'Full Name',
        'form.email' => 'Email Address',
        'form.username' => 'Username',
        'form.college_name' => 'College Name',
        'form.password' => 'Password',
        'form.domain' => 'Domain',
        'form.captcha' => 'Captcha'
    ];

    public function boot(
        OnboardingProcess $process
    ) {
        $this->process = $process;
    }

    public function rules()
    {
        return [
            'form.name' => 'required',
            'form.college_name' => 'required',
            'form.username' => 'required',
            'form.email' => 'required|email|unique:users,email',
            // 'form.password' => ['required', 'confirmed', 'min:8', 'regex:/^.*(?=.{4,})(?=.*[a-zA-Z])(?=.*[0-9])(?=.*[\d\x])(?=.*[!$#%]).*$/',],
            'form.domain' => ['required', 'max:20', 'alpha_num', new UniqueSubDomain()],
            'form.captcha' => ['required',  new HcaptchaValidator()],
        ];
    }

    public function updatedForm(){
        $this->resetErrorBag();
    }

   
    public function validate($rules = null, $messages = [], $attributes = []){
        try{
            parent::validate();
            if (Tenant::where('data->email', $this->form['email'])->count() > 0) {
                throw ValidationException::withMessages([
                    'form.email' => "Another tenant is already created using this email."
                ]);
            }

        } catch(ValidationException $e){
            // dd($e);
            // $this->setErrorBag($e->validator->errors());
            $this->dispatch('lwresponse', []);

            throw new ValidationException($e->validator);
        }
    }



    public function save()
    {
        
        $this->validate();

        DB::beginTransaction();
        try {

            if ($this->plan) {
                $this->process->switchToSubscriptionTasks();
            }

            $response = $this->process->run(
                OnboardingUserPayload::FromRegisterUser(
                    RegisterUserPayload::LazyFromArray(array_merge($this->form, [
                        'password' => Str::random(8),
                        'plan' => $this->plan
                    ]))
                )
            );

            DB::commit();

            $this->resetValidation();

            $this->dispatch('lwresponse', []);
            $this->dispatch('redirect', $response);

            return $response;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function render()
    {
        return view('onboarding.livewire.register-user');
    }
}
