@props(['rows' => 4, 'columns' => 6, 'cellClass' => '', 'wrapperClass' => '' ])


<div {{ $attributes }}>
    <div class="{{ $wrapperClass }} grid grid-cols-{{ $columns }} gap-4">
        @for ($row = 0; $row < $rows; $row++) @for ($col=0; $col < $columns; $col++) <div
            class="{{ $cellClass }} w-full p-4 bg-gray-100 rounded-lg space-y-6 opacity-60">
            <div class="bg-gray-100 animate-pulse h-5 w-full"></div>
            <div class="bg-gray-100 animate-pulse h-6 w-1/2"></div>
            <div class="bg-gray-100 animate-pulse h-3 w-28"></div>
    </div>
    @endfor
    @endfor
    </div>
</div>
