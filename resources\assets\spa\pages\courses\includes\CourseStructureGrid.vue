<template>
    <div class="max-w-max">
        <!--Header-->
        <div class="grid w-full grid-cols-12 border-b bg-gray-50 py-3">
            <div
                class="col-span-2 px-2 text-xs font-medium uppercase tracking-wide text-gray-500"
            >
                Subject Id
            </div>
            <div
                class="col-span-3 px-2 text-xs font-medium uppercase tracking-wide text-gray-500"
            >
                Subject Name
            </div>
            <div
                class="col-span-4 px-2 text-xs font-medium uppercase tracking-wide text-gray-500"
            >
                Unit List
            </div>
            <div
                class="col-span-3 px-2 text-xs font-medium uppercase tracking-wide text-gray-500"
            >
                Assessment
            </div>
        </div>
        <!--Data-->
        <template v-if="props.structure.length > 0">
            <div
                class="tw-table-alternate__row grid w-full grid-cols-12 border-b"
                v-for="(subjects, index) in displayItems"
                :key="index"
            >
                <div
                    class="col-span-2 overflow-hidden text-ellipsis p-2 font-normal text-gray-500"
                >
                    {{ subjects.subject_code }}
                </div>
                <div class="col-span-3 p-2 font-normal text-gray-500">
                    {{ subjects.name }}
                </div>
                <div class="col-span-7">
                    <template v-if="subjects.units?.length > 0">
                        <div
                            class="-mb-[1px] grid w-full grid-cols-7 border-b"
                            v-for="(unit, index) in subjects.units"
                            :key="index"
                        >
                            <div class="col-span-4 p-2 text-gray-500">
                                {{ `${unit.unit_code} - ${unit.unit_name}` }}
                            </div>
                            <div class="col-span-3">
                                <template v-if="unit.assessments?.length > 0">
                                    <div
                                        class="w-full p-2 text-gray-500"
                                        v-for="(
                                            assessment, index
                                        ) in unit.assessments"
                                        :key="index"
                                    >
                                        {{ assessment.task_name }}
                                    </div>
                                </template>
                                <div
                                    class="p-2 text-center text-gray-500"
                                    v-else
                                >
                                    No Assessments Available
                                </div>
                            </div>
                        </div>
                    </template>
                    <div class="p-4 text-center text-gray-500" v-else>
                        No Units Available
                    </div>
                </div>
            </div>
        </template>
        <div class="p-6 text-center text-gray-500" v-else>
            No Subjects Available
        </div>
        <button
            class="mt-4 tracking-wide text-primary-blue-500 hover:text-primary-blue-600"
            v-if="props.structure.length > 3"
            @click="toggleExpandCollapse"
        >
            {{ isExpanded ? "Collapse" : "Expand all" }}
        </button>
    </div>
</template>
<script setup>
import { ref, computed, watch, onMounted } from "vue";

const emit = defineEmits(["closed"]);

const props = defineProps({
    course: { default: [] },
    structure: { default: [] },
});
const displayItems = ref([]);
const isExpanded = ref(false);

const getStrucutreData = computed(() => {
    return props.structure;
});

watch(
    () => props.structure,
    () => {
        displayItems.value = isExpanded.value
            ? props.structure
            : props.structure.slice(0, 3);
    },
);

onMounted(() => {
    displayItems.value = props.structure.slice(0, 3);
});

const cancelProcess = () => {
    emit("closed");
};

const toggleExpandCollapse = () => {
    isExpanded.value = !isExpanded.value;

    if (isExpanded.value) {
        displayItems.value = props.structure;
    } else {
        displayItems.value = props.structure.slice(0, 3);
    }
};
</script>
