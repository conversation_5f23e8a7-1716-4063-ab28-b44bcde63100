<template>
    <div class="space-y-6 px-8">
        <Card
            :pt="{
                root: 'max-w-full mx-auto',
                content: 'px-6',
                header: 'px-6',
            }"
            variant="outlined"
        >
            <template #header>
                <h2 class="text-lg font-semibold text-gray-900">
                    Education Qualifications
                </h2>
            </template>

            <template #content>
                <div class="space-y-4">
                    <div
                        class="custom-uploader rounded-lg border border-gray-200"
                    >
                        <PreviewPane
                            :show="showPreview"
                            @close-preview="handlePreviewClose"
                            @show-preview="handleViewDetail"
                            :documentName="documentName"
                            :files="gridData"
                            v-model:documentName="documentName"
                            @next="handleNextClick"
                            @prev="handlePrevClick"
                        >
                            <template #content>
                                <div
                                    class="tw-custom-table-scrollbar overflow-x-auto"
                                >
                                    <table class="h-fit w-full table-auto">
                                        <thead>
                                            <tr class="bg-gray-50">
                                                <template
                                                    v-for="(
                                                        item, index
                                                    ) in educationQualificationColumns"
                                                    :key="index"
                                                >
                                                    <th
                                                        class="h-10 border-b border-gray-200 px-4 py-2 text-left"
                                                    >
                                                        <span
                                                            class="text-left text-xs font-normal uppercase leading-[0.875rem] text-gray-600"
                                                        >
                                                            {{ item.title }}
                                                            <span
                                                                class="text-sm text-red-500"
                                                                v-if="
                                                                    item.isRequired
                                                                "
                                                            >
                                                                *
                                                            </span>
                                                        </span>
                                                    </th>
                                                </template>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <template
                                                v-for="(
                                                    field, index
                                                ) in formFields"
                                                :key="index"
                                            >
                                                <tr>
                                                    <td
                                                        class="border-b border-gray-200 px-4 py-3"
                                                    >
                                                        <Field
                                                            :id="
                                                                'job_title_' +
                                                                index
                                                            "
                                                            :name="
                                                                'job_title_' +
                                                                index
                                                            "
                                                            :component="'agencyNameTemplate'"
                                                            :orientation="'horizontal'"
                                                            :validator="
                                                                requiredtrue
                                                            "
                                                            :pt="getFieldClass"
                                                            :placeholder="
                                                                'Job Title' +
                                                                ' ' +
                                                                (index + 1)
                                                            "
                                                        >
                                                            <template
                                                                #agencyNameTemplate="{
                                                                    props,
                                                                }"
                                                            >
                                                                <FormInput
                                                                    v-bind="
                                                                        props
                                                                    "
                                                                    @change="
                                                                        props.onChange
                                                                    "
                                                                    @blur="
                                                                        props.onBlur
                                                                    "
                                                                    @focus="
                                                                        props.onFocus
                                                                    "
                                                                />
                                                            </template>
                                                        </Field>
                                                    </td>
                                                    <td
                                                        class="border-b border-gray-200 px-4 py-3"
                                                    >
                                                        <Field
                                                            :id="
                                                                'code_' + index
                                                            "
                                                            :name="
                                                                'code_' + index
                                                            "
                                                            :component="'agencyNameTemplate'"
                                                            :orientation="'horizontal'"
                                                            :validator="
                                                                requiredtrue
                                                            "
                                                            :pt="getFieldClass"
                                                            :placeholder="
                                                                'Code' +
                                                                ' ' +
                                                                (index + 1)
                                                            "
                                                        >
                                                            <template
                                                                #agencyNameTemplate="{
                                                                    props,
                                                                }"
                                                            >
                                                                <FormInput
                                                                    v-bind="
                                                                        props
                                                                    "
                                                                    @change="
                                                                        props.onChange
                                                                    "
                                                                    @blur="
                                                                        props.onBlur
                                                                    "
                                                                    @focus="
                                                                        props.onFocus
                                                                    "
                                                                />
                                                            </template>
                                                        </Field>
                                                    </td>
                                                    <td
                                                        class="border-b border-gray-200 px-4 py-3"
                                                    >
                                                        <Field
                                                            :id="
                                                                'education_provider_' +
                                                                index
                                                            "
                                                            :name="
                                                                'education_provider_' +
                                                                index
                                                            "
                                                            :component="'agencyNameTemplate'"
                                                            :orientation="'horizontal'"
                                                            :validator="
                                                                requiredtrue
                                                            "
                                                            :pt="getFieldClass"
                                                            :placeholder="
                                                                'Education Provider' +
                                                                ' ' +
                                                                (index + 1)
                                                            "
                                                        >
                                                            <template
                                                                #agencyNameTemplate="{
                                                                    props,
                                                                }"
                                                            >
                                                                <FormInput
                                                                    v-bind="
                                                                        props
                                                                    "
                                                                    @change="
                                                                        props.onChange
                                                                    "
                                                                    @blur="
                                                                        props.onBlur
                                                                    "
                                                                    @focus="
                                                                        props.onFocus
                                                                    "
                                                                />
                                                            </template>
                                                        </Field>
                                                    </td>
                                                    <td
                                                        class="border-b border-gray-200 px-4 py-3"
                                                    >
                                                        <Field
                                                            :id="
                                                                'start_date_' +
                                                                index
                                                            "
                                                            :name="
                                                                'start_date_' +
                                                                index
                                                            "
                                                            :component="'agencyNameTemplate'"
                                                            :orientation="'horizontal'"
                                                            :validator="
                                                                requiredtrue
                                                            "
                                                            :pt="getFieldClass"
                                                            :placeholder="
                                                                'Start Date' +
                                                                ' ' +
                                                                (index + 1)
                                                            "
                                                        >
                                                            <template
                                                                #agencyNameTemplate="{
                                                                    props,
                                                                }"
                                                            >
                                                                <FormDatePicker
                                                                    v-bind="
                                                                        props
                                                                    "
                                                                    @change="
                                                                        props.onChange
                                                                    "
                                                                    @blur="
                                                                        props.onBlur
                                                                    "
                                                                    @focus="
                                                                        props.onFocus
                                                                    "
                                                                />
                                                            </template>
                                                        </Field>
                                                    </td>
                                                    <td
                                                        class="border-b border-gray-200 px-4 py-3"
                                                    >
                                                        <Field
                                                            :id="
                                                                'end_date_' +
                                                                index
                                                            "
                                                            :name="
                                                                'end_date_' +
                                                                index
                                                            "
                                                            :component="'agencyNameTemplate'"
                                                            :orientation="'horizontal'"
                                                            :validator="
                                                                requiredtrue
                                                            "
                                                            :pt="getFieldClass"
                                                            :placeholder="
                                                                'End Date' +
                                                                ' ' +
                                                                (index + 1)
                                                            "
                                                        >
                                                            <template
                                                                #agencyNameTemplate="{
                                                                    props,
                                                                }"
                                                            >
                                                                <div
                                                                    class="flex items-center gap-2"
                                                                >
                                                                    <FormDatePicker
                                                                        v-bind="
                                                                            props
                                                                        "
                                                                        @change="
                                                                            props.onChange
                                                                        "
                                                                        @blur="
                                                                            props.onBlur
                                                                        "
                                                                        @focus="
                                                                            props.onFocus
                                                                        "
                                                                        :class="'min-w-[150px]'"
                                                                    />
                                                                    <FormCheckbox
                                                                        v-bind="
                                                                            props
                                                                        "
                                                                        @change="
                                                                            props.onChange
                                                                        "
                                                                        @blur="
                                                                            props.onBlur
                                                                        "
                                                                        @focus="
                                                                            props.onFocus
                                                                        "
                                                                        :label="'Attending '"
                                                                        :class="'!flex w-full min-w-fit items-center gap-1'"
                                                                    />
                                                                </div>
                                                            </template>
                                                        </Field>
                                                    </td>
                                                    <td
                                                        class="w-full border-b border-gray-200 px-4 py-3"
                                                    >
                                                        <Field
                                                            :id="
                                                                'upload_document_' +
                                                                index
                                                            "
                                                            :name="
                                                                'upload_document_' +
                                                                index
                                                            "
                                                            :label="'Upload Photos'"
                                                            :hintMessage="'Hint: Select your additional photos'"
                                                            :component="'myTemplate'"
                                                            :validator="null"
                                                            :multiple="false"
                                                            :autoUpload="false"
                                                            @add="
                                                                handleOnAdd(
                                                                    $event,
                                                                    field,
                                                                )
                                                            "
                                                            @remove="
                                                                handleOnRemove(
                                                                    $event,
                                                                    field,
                                                                )
                                                            "
                                                        >
                                                            <template
                                                                v-slot:myTemplate="{
                                                                    props,
                                                                }"
                                                            >
                                                                <FileUploader
                                                                    v-bind="
                                                                        props
                                                                    "
                                                                    @change="
                                                                        props.onChange
                                                                    "
                                                                    @blur="
                                                                        props.onBlur
                                                                    "
                                                                    @focus="
                                                                        props.onFocus
                                                                    "
                                                                    :value="
                                                                        field.uploads
                                                                    "
                                                                    :restrictions="{
                                                                        allowedExtensions:
                                                                            [
                                                                                '.pdf',
                                                                                '.docx',
                                                                            ],
                                                                        maxFileSize: 1000000,
                                                                    }"
                                                                    :defaultFiles="
                                                                        field.uploads
                                                                    "
                                                                    :buttonLabel="'Upload File'"
                                                                    :isCustomUploader="
                                                                        false
                                                                    "
                                                                    :isPadding="
                                                                        true
                                                                    "
                                                                    :iconName="'download_arrow_up'"
                                                                    :isAgencyUploadForm="
                                                                        true
                                                                    "
                                                                />
                                                            </template>
                                                        </Field>
                                                    </td>
                                                    <td
                                                        class="border-b border-gray-200 px-4 py-3"
                                                    >
                                                        <span
                                                            class="flex items-center gap-2"
                                                        >
                                                            <Tooltip
                                                                :anchor-element="'target'"
                                                                :position="'top'"
                                                                :parentTitle="
                                                                    true
                                                                "
                                                                :tooltipClassName="'flex !p-1.5'"
                                                                :class="'w-full'"
                                                            >
                                                                <div>
                                                                    <Button
                                                                        :variant="'text'"
                                                                        class="h-fit text-gray-400"
                                                                        :title="'View'"
                                                                        @click="
                                                                            handleViewDetail(
                                                                                card,
                                                                            )
                                                                        "
                                                                    >
                                                                        <file-icon
                                                                            :name="'eye'"
                                                                        />
                                                                    </Button></div
                                                            ></Tooltip>
                                                            <template
                                                                v-for="(
                                                                    button, key
                                                                ) in actions"
                                                                :key="key"
                                                            >
                                                                <Tooltip
                                                                    :anchor-element="'target'"
                                                                    :position="'top'"
                                                                    :parentTitle="
                                                                        true
                                                                    "
                                                                    :tooltipClassName="'flex !p-1.5'"
                                                                    :class="'w-full'"
                                                                    v-if="
                                                                        button.value
                                                                    "
                                                                >
                                                                    <Button
                                                                        :variant="'text'"
                                                                        class="h-fit text-gray-400"
                                                                        :title="
                                                                            button.text
                                                                        "
                                                                        :ref="
                                                                            !button.value
                                                                                ? 'actionMenu'
                                                                                : ''
                                                                        "
                                                                        @click="
                                                                            handleAction(
                                                                                index,
                                                                            )
                                                                        "
                                                                    >
                                                                        <file-icon
                                                                            :name="
                                                                                button.icon
                                                                            "
                                                                            :class="
                                                                                button.text ==
                                                                                'Delete'
                                                                                    ? 'text-red-500'
                                                                                    : ''
                                                                            "
                                                                        />
                                                                    </Button>
                                                                </Tooltip>
                                                            </template>
                                                        </span>
                                                    </td>
                                                </tr>
                                            </template>
                                        </tbody>
                                    </table>
                                </div>
                            </template>
                            <template #preview>
                                <div class="custom-pdf p-8 px-0 pb-0 pt-0">
                                    <div
                                        class="tw-filemanager tw-media-manager !h-full !border-0"
                                        ref="previewPdf"
                                    ></div>
                                </div>
                                <div class="block md:hidden">
                                    <PreviewDocumentModal
                                        :file="file"
                                        :isUrl="true"
                                        :visible="showPreview"
                                        :width="'90%'"
                                        @cancel="handlePreviewClose"
                                    />
                                </div>
                            </template>
                        </PreviewPane>
                    </div>
                    <div class="text-center">
                        <Button
                            variant="tertiary"
                            class="min-w-28 px-4"
                            @click="handleAddMore"
                        >
                            <icon
                                name="add"
                                fill="#1890FF"
                                width="16"
                                height="16"
                            />
                            Add More
                        </Button>
                    </div>
                </div>
            </template>
        </Card>
        <Card
            :pt="{
                root: 'max-w-full mx-auto',
                content: 'px-6',
                header: 'px-6',
            }"
            variant="outlined"
        >
            <template #header>
                <h2 class="text-lg font-semibold text-gray-900">
                    Training Qualifications
                </h2>
            </template>

            <template #content>
                <div class="space-y-4">
                    <div
                        class="custom-uploader rounded-lg border border-gray-200"
                    >
                        <PreviewPane
                            :show="showPreview"
                            @close-preview="handlePreviewClose"
                            @show-preview="handleViewDetail"
                            :documentName="documentName"
                            :files="gridData"
                            v-model:documentName="documentName"
                            @next="handleNextClick"
                            @prev="handlePrevClick"
                        >
                            <template #content>
                                <div
                                    class="tw-custom-table-scrollbar overflow-x-auto"
                                >
                                    <table class="h-fit w-full table-auto">
                                        <thead>
                                            <tr class="bg-gray-50">
                                                <template
                                                    v-for="(
                                                        item, index
                                                    ) in trainingQualificationColumns"
                                                    :key="index"
                                                >
                                                    <th
                                                        class="h-10 border-b border-gray-200 px-4 py-2 text-left"
                                                    >
                                                        <span
                                                            class="text-left text-xs font-normal uppercase leading-[0.875rem] text-gray-600"
                                                        >
                                                            {{ item.title }}
                                                            <span
                                                                class="text-sm text-red-500"
                                                                v-if="
                                                                    item.isRequired
                                                                "
                                                            >
                                                                *
                                                            </span>
                                                        </span>
                                                    </th>
                                                </template>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <template
                                                v-for="(
                                                    field, index
                                                ) in formFields"
                                                :key="index"
                                            >
                                                <tr>
                                                    <td
                                                        class="border-b border-gray-200 px-4 py-3"
                                                    >
                                                        <Field
                                                            :id="
                                                                'job_title_' +
                                                                index
                                                            "
                                                            :name="
                                                                'job_title_' +
                                                                index
                                                            "
                                                            :component="'agencyNameTemplate'"
                                                            :orientation="'horizontal'"
                                                            :validator="
                                                                requiredtrue
                                                            "
                                                            :pt="getFieldClass"
                                                            :placeholder="
                                                                'Job Title' +
                                                                ' ' +
                                                                (index + 1)
                                                            "
                                                        >
                                                            <template
                                                                #agencyNameTemplate="{
                                                                    props,
                                                                }"
                                                            >
                                                                <FormInput
                                                                    v-bind="
                                                                        props
                                                                    "
                                                                    @change="
                                                                        props.onChange
                                                                    "
                                                                    @blur="
                                                                        props.onBlur
                                                                    "
                                                                    @focus="
                                                                        props.onFocus
                                                                    "
                                                                />
                                                            </template>
                                                        </Field>
                                                    </td>
                                                    <td
                                                        class="border-b border-gray-200 px-4 py-3"
                                                    >
                                                        <Field
                                                            :id="
                                                                'code_' + index
                                                            "
                                                            :name="
                                                                'code_' + index
                                                            "
                                                            :component="'agencyNameTemplate'"
                                                            :orientation="'horizontal'"
                                                            :validator="
                                                                requiredtrue
                                                            "
                                                            :pt="getFieldClass"
                                                            :placeholder="
                                                                'Code' +
                                                                ' ' +
                                                                (index + 1)
                                                            "
                                                        >
                                                            <template
                                                                #agencyNameTemplate="{
                                                                    props,
                                                                }"
                                                            >
                                                                <FormInput
                                                                    v-bind="
                                                                        props
                                                                    "
                                                                    @change="
                                                                        props.onChange
                                                                    "
                                                                    @blur="
                                                                        props.onBlur
                                                                    "
                                                                    @focus="
                                                                        props.onFocus
                                                                    "
                                                                />
                                                            </template>
                                                        </Field>
                                                    </td>
                                                    <td
                                                        class="border-b border-gray-200 px-4 py-3"
                                                    >
                                                        <Field
                                                            :id="
                                                                'education_provider_' +
                                                                index
                                                            "
                                                            :name="
                                                                'education_provider_' +
                                                                index
                                                            "
                                                            :component="'agencyNameTemplate'"
                                                            :orientation="'horizontal'"
                                                            :validator="
                                                                requiredtrue
                                                            "
                                                            :pt="getFieldClass"
                                                            :placeholder="
                                                                'Education Provider' +
                                                                ' ' +
                                                                (index + 1)
                                                            "
                                                        >
                                                            <template
                                                                #agencyNameTemplate="{
                                                                    props,
                                                                }"
                                                            >
                                                                <FormInput
                                                                    v-bind="
                                                                        props
                                                                    "
                                                                    @change="
                                                                        props.onChange
                                                                    "
                                                                    @blur="
                                                                        props.onBlur
                                                                    "
                                                                    @focus="
                                                                        props.onFocus
                                                                    "
                                                                />
                                                            </template>
                                                        </Field>
                                                    </td>
                                                    <td
                                                        class="border-b border-gray-200 px-4 py-3"
                                                    >
                                                        <Field
                                                            :id="
                                                                'start_date_' +
                                                                index
                                                            "
                                                            :name="
                                                                'start_date_' +
                                                                index
                                                            "
                                                            :component="'agencyNameTemplate'"
                                                            :orientation="'horizontal'"
                                                            :validator="
                                                                requiredtrue
                                                            "
                                                            :pt="getFieldClass"
                                                            :placeholder="
                                                                'Start Date' +
                                                                ' ' +
                                                                (index + 1)
                                                            "
                                                        >
                                                            <template
                                                                #agencyNameTemplate="{
                                                                    props,
                                                                }"
                                                            >
                                                                <FormDatePicker
                                                                    v-bind="
                                                                        props
                                                                    "
                                                                    @change="
                                                                        props.onChange
                                                                    "
                                                                    @blur="
                                                                        props.onBlur
                                                                    "
                                                                    @focus="
                                                                        props.onFocus
                                                                    "
                                                                />
                                                            </template>
                                                        </Field>
                                                    </td>
                                                    <td
                                                        class="w-full border-b border-gray-200 px-4 py-3"
                                                    >
                                                        <Field
                                                            :id="
                                                                'upload_document_' +
                                                                index
                                                            "
                                                            :name="
                                                                'upload_document_' +
                                                                index
                                                            "
                                                            :label="'Upload Photos'"
                                                            :hintMessage="'Hint: Select your additional photos'"
                                                            :component="'myTemplate'"
                                                            :validator="null"
                                                            :multiple="false"
                                                            :autoUpload="false"
                                                            @add="
                                                                handleOnAdd(
                                                                    $event,
                                                                    field,
                                                                )
                                                            "
                                                            @remove="
                                                                handleOnRemove(
                                                                    $event,
                                                                    field,
                                                                )
                                                            "
                                                        >
                                                            <template
                                                                v-slot:myTemplate="{
                                                                    props,
                                                                }"
                                                            >
                                                                <FileUploader
                                                                    v-bind="
                                                                        props
                                                                    "
                                                                    @change="
                                                                        props.onChange
                                                                    "
                                                                    @blur="
                                                                        props.onBlur
                                                                    "
                                                                    @focus="
                                                                        props.onFocus
                                                                    "
                                                                    :value="
                                                                        field.uploads
                                                                    "
                                                                    :restrictions="{
                                                                        allowedExtensions:
                                                                            [
                                                                                '.pdf',
                                                                                '.docx',
                                                                            ],
                                                                        maxFileSize: 1000000,
                                                                    }"
                                                                    :defaultFiles="
                                                                        field.uploads
                                                                    "
                                                                    :buttonLabel="'Upload File'"
                                                                    :isCustomUploader="
                                                                        false
                                                                    "
                                                                    :isPadding="
                                                                        true
                                                                    "
                                                                    :iconName="'download_arrow_up'"
                                                                    :isAgencyUploadForm="
                                                                        true
                                                                    "
                                                                />
                                                            </template>
                                                        </Field>
                                                    </td>
                                                    <td
                                                        class="border-b border-gray-200 px-4 py-3"
                                                    >
                                                        <span
                                                            class="flex items-center gap-2"
                                                        >
                                                            <Tooltip
                                                                :anchor-element="'target'"
                                                                :position="'top'"
                                                                :parentTitle="
                                                                    true
                                                                "
                                                                :tooltipClassName="'flex !p-1.5'"
                                                                :class="'w-full'"
                                                            >
                                                                <div>
                                                                    <Button
                                                                        :variant="'text'"
                                                                        class="h-fit text-gray-400"
                                                                        :title="'View'"
                                                                        @click="
                                                                            handleViewDetail(
                                                                                card,
                                                                            )
                                                                        "
                                                                    >
                                                                        <file-icon
                                                                            :name="'eye'"
                                                                        />
                                                                    </Button></div
                                                            ></Tooltip>
                                                            <template
                                                                v-for="(
                                                                    button, key
                                                                ) in actions"
                                                                :key="key"
                                                            >
                                                                <Tooltip
                                                                    :anchor-element="'target'"
                                                                    :position="'top'"
                                                                    :parentTitle="
                                                                        true
                                                                    "
                                                                    :tooltipClassName="'flex !p-1.5'"
                                                                    :class="'w-full'"
                                                                    v-if="
                                                                        button.value
                                                                    "
                                                                >
                                                                    <Button
                                                                        :variant="'text'"
                                                                        class="h-fit text-gray-400"
                                                                        :title="
                                                                            button.text
                                                                        "
                                                                        :ref="
                                                                            !button.value
                                                                                ? 'actionMenu'
                                                                                : ''
                                                                        "
                                                                        @click="
                                                                            handleAction(
                                                                                index,
                                                                            )
                                                                        "
                                                                    >
                                                                        <file-icon
                                                                            :name="
                                                                                button.icon
                                                                            "
                                                                            :class="
                                                                                button.text ==
                                                                                'Delete'
                                                                                    ? 'text-red-500'
                                                                                    : ''
                                                                            "
                                                                        />
                                                                    </Button>
                                                                </Tooltip>
                                                            </template>
                                                        </span>
                                                    </td>
                                                </tr>
                                            </template>
                                        </tbody>
                                    </table>
                                </div>
                            </template>
                            <template #preview>
                                <div class="custom-pdf p-8 px-0 pb-0 pt-0">
                                    <div
                                        class="tw-filemanager tw-media-manager !h-full !border-0"
                                        ref="previewPdf"
                                    ></div>
                                </div>
                                <div class="block md:hidden">
                                    <PreviewDocumentModal
                                        :file="file"
                                        :isUrl="true"
                                        :visible="showPreview"
                                        :width="'90%'"
                                        @cancel="handlePreviewClose"
                                    />
                                </div>
                            </template>
                        </PreviewPane>
                    </div>
                    <div class="text-center">
                        <Button
                            variant="tertiary"
                            class="min-w-28 px-4"
                            @click="handleAddMore"
                        >
                            <icon
                                name="add"
                                fill="#1890FF"
                                width="16"
                                height="16"
                            />
                            Add More
                        </Button>
                    </div>
                </div>
            </template>
        </Card>
    </div>
</template>
<script>
import Card from "@spa/components/Card/Card.vue";
import { Field, FormElement } from "@progress/kendo-vue-form";
import FormDropDown from "@spa/components/KendoInputs/FormDropDown.vue";
import PhoneMaskedTextBox from "@spa/components/KendoInputs/PhoneMaskedTextBox.vue";
import FormInput from "@spa/components/KendoInputs/FormInput.vue";
import FormDatePicker from "@spa/components/KendoInputs/FormDatePicker.vue";
import FormAutoComplete from "@spa/components/KendoInputs/FormAutoComplete.vue";
import PreviewPane from "@spa/components/KendoGrid/PreviewPane.vue";
import FileUploader from "@spa/components/Uploader/FileUploader.vue";
import Button from "@spa/components/Buttons/Button.vue";
import { Tooltip } from "@progress/kendo-vue-tooltip";
import FormCheckbox from "@spa/components/KendoInputs/FormCheckbox.vue";

import {
    requiredtrue,
    emailtrue,
    requiredDate,
} from "@spa/services/validators/kendoCommonValidator.js";

export default {
    props: {},
    components: {
        Card,
        Field,
        FormElement,
        FormInput,
        FormDropDown,
        PhoneMaskedTextBox,
        FormDatePicker,
        FormAutoComplete,
        PreviewPane,
        FileUploader,
        Button,
        Tooltip,
        FormCheckbox,
    },
    inject: ["kendoForm"],
    data() {
        return {
            formFields: [],
            showPreview: false,
            actions: [
                {
                    text: "Delete",
                    value: "delete",
                    icon: "trash",
                    isMore: true,
                },
            ],
            educationQualificationColumns: [
                {
                    title: "Qualification Name",
                    isRequired: true,
                },
                {
                    title: "Code",
                    isRequired: false,
                },
                {
                    title: "Education Provider",
                    isRequired: true,
                },
                {
                    title: "Start Date",
                    isRequired: true,
                },
                {
                    title: "End Date",
                    isRequired: true,
                },
                {
                    title: "Upload Document",
                    isRequired: false,
                },
                {
                    title: "Action",
                    isRequired: false,
                },
            ],
            trainingQualificationColumns: [
                {
                    title: "credentials NAME",
                    isRequired: true,
                },
                {
                    title: "Code",
                    isRequired: false,
                },
                {
                    title: "Education Provider",
                    isRequired: true,
                },
                {
                    title: "Awarded Date",
                    isRequired: true,
                },
                {
                    title: "Upload Document",
                    isRequired: false,
                },
                {
                    title: "Action",
                    isRequired: false,
                },
            ],
        };
    },
    computed: {
        getFieldClass() {
            return {
                label: "text-sm leading-7 text-gray-700 w-44 shrink-0 grow-1 !font-normal",
                root: "w-full gap-4",
                wrap: "w-full  custom-date-picker",
                field: "!gap-6",
            };
        },
    },
    methods: {
        requiredtrue,
        handlePhoneChange(e, value) {
            this.kendoForm.onChange(e.target.name, {
                value: value,
            });
        },
        handleAutoCompleteChange(e) {
            this.kendoForm.onChange(e.target.name, {
                value: value,
            });
        },
        async handleUploadDocumentsForm(file, field) {
            const formData = new FormData();
            formData.append("file", file);
            formData.append("checklist_id", field.id);
            formData.append("document_name", field.document_name);
            formData.append("is_compulsory", field.is_compulsory);
            formData.append("agentId", this.data?.agentData?.id);

            try {
                const response = await apiClient.post(
                    "api/save-agent-application-documents",
                    formData,
                    {
                        headers: {
                            "Content-Type": "multipart/form-data",
                        },
                    },
                );

                console.log("Upload success:", response.data);
                // Refresh the state after successful upload
                // this.refreshState();
            } catch (error) {
                console.error("Upload error:", error);
            }
        },
        handleOnAdd(event, field) {
            console.log("Added", field);
            const file = event.newState[0]?.getRawFile(); // Get raw file

            if (!file) {
                console.error("No file found.");
                return;
            }

            console.log(`File added for ${field.id}:`, file);
            // Assign file to the form field
            if (
                this.kendoForm &&
                typeof this.kendoForm.onChange === "function"
            ) {
                setTimeout(() => {
                    this.kendoForm.onChange(field.id, {
                        value: file.name,
                    });
                }, 2000);

                console.log("Updated form value:", this.kendoForm.values);
            } else {
                console.error("kendoForm or onChange function is missing.");
            }

            // Call function to upload the document
            this.handleUploadDocumentsForm(file, field);
        },
        handleOnRemove(event, field) {
            const file = event.affectedFiles[0];

            if (!file) {
                console.error("No file found.");
                return;
            }

            console.log("hewe", file);
            this.handleRemoveDocumentsForm(file, field);
            // Optionally, handle any additional logic for file removal
        },
        handleAction(index) {
            const preservedValues = { ...this.kendoForm.values };
            this.formFields.splice(index, 1);
            const newValues = {};
            this.formFields.forEach((field, i) => {
                const oldKey = Object.keys(field)[0]; // Get original key
                const newKey = `document_name_${i}`;

                newValues[newKey] = preservedValues[oldKey] ?? "";
            });

            this.kendoForm.values = newValues;
        },
        handleAddMore() {
            const nextIndex = this.formFields.length;
            const newJobTitleField = { [`job_title_${nextIndex}`]: "" };
            const newOrganizationField = {
                [`organizarion_name_${nextIndex}`]: "",
            };
            const newEducationProviderField = {
                [`education_provider_${nextIndex}`]: "",
            };
            const newStartDateField = { [`start_date_${nextIndex}`]: "" };
            const newEndDateField = { [`end_date${nextIndex}`]: "" };
            const newUploadDocumentField = {
                [`upload_document_${nextIndex}`]: "",
            };

            this.formFields.push({
                [`document_name_${nextIndex}`]: "",
                [`upload_${nextIndex}`]: "",
                is_compulsory: false,
            });
            this.kendoForm.values = {
                ...this.kendoForm.values,
                ...newJobTitleField,
                ...newOrganizationField,
                ...newEducationProviderField,
                ...newStartDateField,
                ...newEndDateField,
                ...newUploadDocumentField,
            };
        },
        getName(data) {
            return "Test Document";
        },
        handleViewDetail(item) {
            console.log("item", item);
            this.getName(item);
            this.dataItem = item;
            this.showPreview = true;
            // const urlPreview = route("agent_preview_offer_letter_pdf_new", [
            //     (item.course_id = 1),
            //     (item.student_id = 2),
            //     (item.student_course_id = 3),
            // ]);
            // replace with actual url
            const urlPreview =
                "http://local.galaxy360.test/uploads/3/college_marterials/**********-8IG7UUdpxfrVBRmx-X6o9dUbO4Kex9isC.pdf";
            this.previewDocument(urlPreview);
        },
        handlePreviewClose() {
            this.showPreview = false;
        },
        previewDocument(item) {
            this.$nextTick(() => {
                const componentRef = this.$refs.actionMenu;
                console.log("Clicked", this.$refs);

                if (!componentRef) {
                    console.error("studentDocument ref is undefined.");
                    return;
                }

                const fileUrl = this.isUrl
                    ? encodeURI(item)
                    : encodeURI(window[item]);

                this.file = fileUrl;

                const existingPdfViewer =
                    $(componentRef).data("kendoPDFViewer");
                console.log("Exi", existingPdfViewer);
                if (existingPdfViewer) {
                    existingPdfViewer.destroy();
                    $(componentRef).empty(); // Clear DOM to prevent duplicate instances
                }

                $.when(
                    $.getScript(
                        "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.js",
                    ),
                    $.getScript(
                        "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js",
                    ),
                )
                    .done(() => {
                        window.pdfjsLib.GlobalWorkerOptions.workerSrc =
                            "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js";
                    })
                    .then(() => {
                        $(componentRef)
                            .kendoPDFViewer({
                                pdfjsProcessing: {
                                    file: fileUrl,
                                },
                                width: "100%",
                                height: "100%",
                            })
                            .data("kendoPDFViewer");
                    });
            });
        },
        handlePreviewChange(item) {
            setTimeout(() => {
                const urlPreview =
                    "http://local.galaxy360.test/uploads/3/college_marterials/**********-8IG7UUdpxfrVBRmx-X6o9dUbO4Kex9isC.pdf";
                this.previewDocument(
                    // route("agent_preview_offer_letter_pdf_new", [
                    //     item.course_id,
                    //     item.student_id,
                    //     item.student_course_id,
                    // ]),
                    urlPreview,
                );
            });
        },
        handleNextClick(item) {
            this.handlePreviewChange(item);
        },
        handlePrevClick(item) {
            this.handlePreviewChange(item);
        },
    },
};
</script>
<style lang=""></style>
