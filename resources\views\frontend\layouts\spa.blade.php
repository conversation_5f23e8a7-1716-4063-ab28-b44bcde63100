<!DOCTYPE html>
<html lang="en">

<head>
    {{-- @include('frontend.newlayout.include.css', ['hideBootstrap' => 1, 'spa' => 1]) --}}
    @include('frontend.layouts.spa-css')
    <link href="{{ asset('v2/css/sadmin/onboard.css') }}" rel="stylesheet" />
    {{--
    <x-v2.includes.head :cssHeader="@$cssHeader" /> --}}

    {!! jsVar([
    'features' => config('features'),
    'authUser' => auth()->user(),
    'APP_URL' => url('/'),
    'API_URL' => url('/api'),
    'APP_NAME' => config('app.name'),
    'kendo_countries_codes' => countries_codes(),
    'time_zone_list' => Config::get('timezone.list'),
    ]) !!}
    <script src="https://unpkg.com/konva@9/konva.min.js"></script>

    @routes
    {{-- <script src="{{ asset(mix('build/js/spa.js')) }}" defer></script> --}}
    @vite(['resources/assets/spa/app.js'])
    @inertiaHead
</head>

<body class="{{ @$_COOKIE['sidebar'] && $_COOKIE['sidebar'] == 'slide' ? 'btn-slide' : '' }}">
    {{-- @php
    echo(phpinfo())
    @endphp --}}
    <div class="root">
        <div class="flex h-screen" style="background-color: #f1f5f9">

            {{-- @include('frontend.newlayout.include.left-sidebar.sadmin', [
            'mainmenu' => 'clients',
            ]) --}}
            @php
            $mainmenu = @$page['props']['data']['mainmenu'];
            @endphp
            <x-v2.includes.sidebar.new-sadmin
                :mainmenu="isset($mainmenu) && !is_null($mainmenu) ? $mainmenu : 'clients'" />

            <div class="main_wrapper flex w-0 flex-1 flex-col overflow-hidden">
                <x-v2.includes.newheader :hideGlobalSearch="isset($isManagementUser) ? !$isManagementUser : false" :back="@$back" />
                <main class="relative flex-1 overflow-y-hidden focus:outline-none" tabIndex="0">
                    <div class="main-wrap h-full">
                        @include('frontend.newlayout.include.breadcrumb')
                        @include('frontend.newlayout.include.message')
                        <div id="vue-app" class="h-full" data-page="{{ json_encode($page) }}">
                            <div class="relative h-full">
                                <div
                                    class="absolute left-1/2 top-2/4 flex h-full w-full -translate-x-1/2 -translate-y-1/2 items-center justify-center gap-4 p-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-loader-circle-icon lucide-loader-circle animate-spin-fast text-primary-blue-500">
                                        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>

        </div>
    </div>
    <x-v2.includes.nav-loader />

    {{-- Already included global search in the new header in this layout so force hide
    the global search logic in the include file,
    The jira issue collector is embedded in the same include file so we are not removing this include. --}}
    {{-- @include('frontend.newlayout.include.globalsearch', [
    'hideGlobalSearch' => true
    ]) --}}
    {{-- @include('frontend.newlayout.include.js', ['hideBootstrap' => 1, 'spa' => 1]) --}}
    @include('frontend.layouts.spa-js')


</body>

</html>