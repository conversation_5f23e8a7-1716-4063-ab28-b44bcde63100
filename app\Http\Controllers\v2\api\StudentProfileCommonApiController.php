<?php

namespace App\Http\Controllers\v2\api;

use App\Exceptions\ApplicationException;
use App\Http\Controllers\Controller;
use App\Http\Requests\FormValidation\StudentProfile\SendQueueLetterEmailRequest;
use App\Process\StudentProfile\UpdateCourseStatusProcess;
use App\Repositories\StudentProfileCommonRepository;
use App\Http\Requests\FormValidation\StudentProfile\AddNewActivityNoteRequest;
use App\Http\Requests\FormValidation\StudentProfile\InsertEnrollStudentCourseRequest;
use App\Model\v2\SmtpSetup;
use App\Model\v2\GalaxyQueue;
use App\Services\StudentProfileCommonService;
use App\Traits\CommonTrait;
use Illuminate\Http\Request;
use App\Traits\ResponseTrait;
use App\Traits\SendEmailTrait;
use App\Traits\SendSMSTrait;
use App\Traits\LetterGenerateAndSendTrait;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Config;
use App\Http\Requests\FormValidation\StudentProfile\SendQueueEmailRequest;
use App\Helpers\Helpers;
use App\Model\v2\StudentCourses;
use Support\Services\UploadService;

class StudentProfileCommonApiController extends Controller
{

    use CommonTrait;
    use ResponseTrait;
    use SendEmailTrait;
    use SendSMSTrait;
    use LetterGenerateAndSendTrait;

    private $studentProfileCommonService;
    protected $studentProfileCommonRepository;
    public function __construct(
        StudentProfileCommonService $studentProfileCommonService,
        StudentProfileCommonRepository $studentProfileCommonRepository) {
        $this->studentProfileCommonService = $studentProfileCommonService;
        $this->studentProfileCommonRepository = $studentProfileCommonRepository;
        ini_set('memory_limit', '-1');
    }

    public function addNewActivityNote(AddNewActivityNoteRequest $request)
    {
        $saveData = $this->studentProfileCommonService->createLog($request);
        return $this->successResponse('Note Added Successfully', 'data', $saveData);
    }

    public function updateStudentCourseStatus(Request $request, UpdateCourseStatusProcess $process)
    {
        $studentId = $request->studentId;
        $studentCourseId = $request->studCourseId;
        DB::beginTransaction();
        try {
            $res = $process->run($request);
            if ($res['status'] == 'success') {
                DB::commit();
                $data = $this->getLatestCourseList($studentId);
                $data['isActiveCourse'] = $this->studentProfileCommonService->isActiveStudentCourse($studentCourseId);
                return $this->successResponse($res['message'], 'data', $data, 200);
            } else {
                DB::rollBack();
                return $this->errorResponse($res['message'], 'data', '', 200);
            }
        }catch(\Exception $e){
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    private function getLatestCourseList($studentId)
    {
        /*$data['mainArray'] = $this->studentProfileCommonService->getStudentCoursesData($studentId,true);
        foreach ($data['mainArray'] as $key=>$value)
        {
            $data['dropDownArray'][$key]['id'] = $value['id'];
            $data['dropDownArray'][$key]['course_title'] = $value['course_title'];
        }
        return $data;*/
        $courseList = $this->studentProfileCommonService->getStudentCoursesData($studentId, true);

        $dropDownArray = array_map(function ($course) {
            return [
                'id' => $course['id'],
                'course_title' => $course['course_title']
            ];
        }, $courseList);

        return [
            'mainArray' => $courseList,
            'dropDownArray' => $dropDownArray
        ];
    }

    public function getStatusHistoryData(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $studentId = $request->input('student_id');
        $courseId = $request->input('student_course_id');
        $data = $this->studentProfileCommonService->getStatusHistoryData($collegeId, $studentId, $courseId);
        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function studentCoursesData(Request $request)
    {
        $studentId = $request->input('student_id');
        $flag = ($request->input('enroll_option') == '1') ? true : false;
        $orderByDate = true;
        $courseDetail = $this->studentProfileCommonService->getStudentCoursesData($studentId, $flag,$orderByDate);
        return $this->successResponse('Data found successfully', 'data', $courseDetail);
    }

    public function getOfferIdList(Request $request)
    {
        $studentId = $request->input('student_id');
        $isNewOffer = $request->input('new_offer') == 'yes';
        $offerIdList = $this->studentProfileCommonService->getOfferIdList($studentId, $isNewOffer);
        return $this->successResponse('Data found successfully', 'data', $offerIdList);
    }

    public function getStudentCourseList(Request $request)
    {
        $collegeId = $request->input('college_id');
        $courseTypeID = $request->input('course_type_id');
        $campusId = $request->input('campus_id');
        $courseList = $this->studentProfileCommonService->getCourseList($collegeId, $courseTypeID, $campusId);
        return $this->successResponse('Data found successfully', 'data', $courseList);
    }

    public function getCollegeCampusList(Request $request)
    {
        $collegeId = $request->input('college_id');
        $courseId = $request->input('course_id');
        $resultArr = $this->studentProfileCommonService->getCollegeCampusList($collegeId, $courseId);
        return $this->successResponse('Data found successfully', 'data', $resultArr);
    }

    public function getResultCalMethod(Request $request)
    {
        $courseId = $request->input('courseId');
        $data = $this->studentProfileCommonService->getResultCalculationMethods($courseId);
        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getAgentList(Request $request)
    {
        $collegeId = $request->input('college_id');
        $agents = $this->studentProfileCommonService->getAgentsByCollegeId($collegeId);

        // New element to be added at the beginning
        $newElement = [
            "Id" => 0,
            "Name" => "All"
        ];

        // Adding the new element at the beginning of the array
        array_unshift($agents, $newElement);


        return $this->successResponse('Data found successfully', 'data', $this->convertToISO8859_1($agents));
    }

    public function getCourseWiseTemplateList(Request $request)
    {
        $collegeId = $request->college_id;
        $courseId = $request->courseId;
        $courseTemplates = $this->studentProfileCommonService->getCourseTemplates($collegeId, $courseId);
        return $this->successResponse('Data found successfully', 'data', $courseTemplates);
    }

    public function insertEnrollStudentCourse(InsertEnrollStudentCourseRequest $request)
    {
        $data = $this->studentProfileCommonService->enrollStudentCourse($request->DTO());
        if ($data) {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function studentSendEmailWithQueue(SendQueueEmailRequest $request)
    {
        try {
            $payloadDTO = $this->preparePayload($request);
            $attachmentFiles = [];

            // Process attachments
            $this->processAttachmentsIfExists($request, 'attachment_file', $attachmentFiles, $payloadDTO->groupName);
            $this->processAttachmentsIfExists($request, 'email_attachment', $attachmentFiles, $payloadDTO->groupName);

            $payloadDTO->attachment_file = $attachmentFiles;

            $this->processStudentEmailChunks($payloadDTO);

            return response()->json(['data' => ['success_msg' => "Email sending process is running in the background."]]);
        } catch (\Exception $e) {
            throw new ApplicationException($e->getMessage());
        }
    }

    private function preparePayload(SendQueueEmailRequest $request)
    {
        //dd($request);
        $email_type = 'generic';
        if ((isset($request->student_course_id) && $request->student_course_id != '') ||
            (isset($request->course_id) && $request->course_id != '')) {
            $email_type = 'course';
        }
        //dd($email_type);

        $payloadDTO = $request->DTO();
        $payloadDTO->college_id = Auth::user()->college_id;
        $payloadDTO->user_id = Auth::user()->id;
        $payloadDTO->email_type = $email_type;
        $payloadDTO->groupName = 'scoutStudentListEmailQueue' . time();
        return $payloadDTO;
    }

    private function processAttachmentsIfExists($request, $inputName, &$attachmentFiles, $groupName)
    {
        if ($request->hasFile($inputName)) {
            $this->processAttachments($request->file($inputName), $attachmentFiles, $groupName);
        }
    }

    private function processStudentEmailChunks($payloadDTO)
    {
        if($payloadDTO->mailSendFromModule == 'communication'){
            $handlerClass = "App\Queues\Handlers\BulkEmailProcessor"; // Handler class implementing QueueHandlerContract

            $studentIds = explode(',', $payloadDTO->student_course_id);
            // $studentIds = StudentCourses::query()->whereIn('id',$studentCourseIds)->pluck('student_id','course_id')->toArray();

            // $studentIds = explode(',', $payloadDTO->student_id);
            $studentIds = array_filter($studentIds, function($value) {
                        return $value !== '0';
            });

           // $studentIds = array_values($studentIds);

            $chunkSize = Config::get('constants.email_batch_size', 10);
            $chunks = array_chunk($studentIds, $chunkSize,true );
            // dd($chunks);
            $config = ['template'=>"emailTemplate"];
            foreach ($chunks as $chunk) {
                $studentIds = StudentCourses::query()->whereIn('id',$chunk)->pluck('student_id')->toArray();
                $courseIds = StudentCourses::query()->whereIn('id',$chunk)->pluck('course_id')->toArray();
                $chunkedStudentIds = implode(',', $studentIds);
                $chunkedCourseIds = implode(',', $courseIds);

                $payloadDTO->student_id = $chunkedStudentIds;
                $payloadDTO->course_id = $chunkedCourseIds;
                $payloadDTO->allStudentId = $chunkedStudentIds;
                GalaxyQueue::SendForProcessing($handlerClass, $payloadDTO,$config, $payloadDTO->groupName,Config::get('constants.HIGHER_PRIORITY'),'Email');
            }
        }else{
            $handlerClass = "App\Queues\Handlers\BulkEmailProcessor"; // Handler class implementing QueueHandlerContract
            $studentIds = explode(',', $payloadDTO->student_id);
            $studentIds = array_filter($studentIds, function($value) {
                        return $value !== '0';
            });
            $studentIds = array_values($studentIds);

            $chunkSize = Config::get('constants.email_batch_size', 10);
            $chunks = array_chunk($studentIds, $chunkSize);
            $config = ['template'=>"emailTemplate"];
            foreach ($chunks as $chunk) {
                $payloadDTO->student_id = implode(',', $chunk);
                $payloadDTO->allStudentId = implode(',', $chunk);
                GalaxyQueue::SendForProcessing($handlerClass, $payloadDTO,$config, $payloadDTO->groupName,Config::get('constants.HIGHER_PRIORITY'),'Email');
            }
        }

    }
    private function processAttachments($files, &$attachmentFiles, $groupName){
        foreach ($files as $file) {
            $yearMonth = date('Ym');
            $filePath = Config::get('constants.uploadFilePath.SendEmailTemp');
            $destinationPath = Helpers::changeRootPath($filePath);
            $uploadPath = Helpers::getCurrentUploadFolder($destinationPath, $yearMonth, $groupName);
            $originalName = $file->getClientOriginalName();
            // $file->move($uploadPath['default'], $originalName);

            $upload_success = UploadService::uploadAs($destinationPath['view'], $file, $originalName);
            info('file uploaded form pre attach file', [$upload_success]);

            // $attachmentFiles[] = asset($uploadPath['view'] . '/' . $originalName);
            $attachmentFiles[] = $upload_success;
        }
    }

    public function studentSendEmail(Request $request)
    {
        //$inputRequest = $request->input();
        $emailStatusData = $this->sendMailToStudentTrait($request);
        return response()->json($emailStatusData);
    }

    public function addSmsDetails(Request $request){
        //$returnArr = $this->studentProfileCommonService->addSmsDetails($request);
        $returnArr = $this->sendSMSToStudentTrait($request);
        return response()->json($returnArr);
    }

    public function studentIssueLetterEmail_OLD(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'letter_content' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->errors()->first(), 'data', '');
        }

        $letterStatusData = $this->sendLetterInMailToStudentTrait($request);
        return response()->json($letterStatusData);
    }

    public function getStudentDetail(Request $request)
    {
        $studentId = $request->student_id;
        $collegeId = $request->college_id;
        $data = $this->studentProfileCommonService->getStudentDetail($studentId, $collegeId,$request->student_course_id);
        $data = utf8ize($data);
        return response()->json(['message' => 'Data found successfully', 'data' => $data]);
    }

    public function updateStudentDetails(Request $request)
    { //TODO::NOT USED
        $studentId = $request->student_id;
        $formData = $request->input('formData');
        $data = $this->studentProfileCommonService->updateStudentDetails($studentId, $formData);
        return response()->json(['message' => 'Data saved successfully', 'data' => $data]);
    }

    public function uploadStudentProfilePic(Request $request)
    {
        $collegeId = $request->post('college_id');
        $studentId = $request->post('student_id');
        $file = $request->file('files');
        if(empty($file)){
            return response()->json(['uploaded' => false, 'fileUid' => '', 'status' => 'error', 'message' => 'Allow jpg, jpeg & png file. File size less from 2MB.']);
        }
        $metaDataArr = json_decode($request->input('metadata'), true);
        $originalFileName = $metaDataArr['fileName'];
        //$originalFileName = $request->input('metadata.fileName');
        $data = $this->studentProfileCommonService->uploadStudentProfilePic($collegeId, $studentId, $file, $originalFileName);
        return response()->json($data);
    }

    public function getAllStudentCoursesList(Request $request)
    {
        $courseList = $this->studentProfileCommonService->getAllCourseList($request->input());
        return $this->successResponse('Data found successfully', 'data', $courseList);
    }
    public function getStudentCourseCampusWiseList(Request $request)
    {
        $campusId = $request->input('campus_id');
        $courseList = $this->studentProfileCommonService->getCourseWiseList($campusId);
        return $this->successResponse('Data found successfully', 'data', $courseList);
    }
    public function getFromEmailId(Request $request){

        $collegeId = Auth::user()->college_id;
        $emailData = SmtpSetup::where('college_id', $collegeId)->first(['email', 'status']);
        $email = $emailData && $emailData->status ? $emailData->email : config('mail.from.address');
        return $this->successResponse('Data found successfully', 'data', $email);
    }




    public function studentIssueLetterEmail(SendQueueLetterEmailRequest $request)
    {
        try {
            $payloadDTO = $this->prepareLetterPayload($request);
            $this->processStudentLetterEmailChunks($payloadDTO);
            return response()->json(['status' => 'success', 'data' => ['success_msg' => "Email sending process is running in the background."]]);
        } catch (\Exception $e) {
            throw new ApplicationException($e->getMessage());
        }
    }

    private function prepareLetterPayload(SendQueueLetterEmailRequest $request)
    {
        $payloadDTO = $request->DTO();
        $payloadDTO->college_id = Auth::user()->college_id;
        $payloadDTO->user_id = Auth::user()->id;
        $payloadDTO->groupName = 'scoutStudentListLetterEmailQueue' . time();
        return $payloadDTO;
    }

    private function processStudentLetterEmailChunks($payloadDTO)
    {
        $handlerClass = "App\Queues\Handlers\BulkLetterEmailProcessor";
        $studentIds = explode(',', $payloadDTO->student_id);
        $studentIds = array_filter($studentIds, function($value) {
            return $value !== '0';
        });
        $studentIds = array_values($studentIds);

        $chunkSize = Config::get('constants.email_batch_size', 10);
        $chunks = array_chunk($studentIds, $chunkSize);
        $config = ['template'=>"letterTemplate"];
        foreach ($chunks as $chunk) {
            $payloadDTO->student_id = implode(',', $chunk);
            $payloadDTO->allStudentId = implode(',', $chunk);
            GalaxyQueue::SendForProcessing($handlerClass, $payloadDTO, $config, $payloadDTO->groupName, Config::get('constants.HIGHER_PRIORITY'),'Email with Letter');
        }
    }
}