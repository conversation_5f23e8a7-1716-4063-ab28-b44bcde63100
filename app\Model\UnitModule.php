<?php

namespace App\Model;

use App\Model\v2\Subject;
use App\Model\v2\SubjectMaterial;
use App\Services\SyncUnitsSetupService;
use App\Traits\AvetMissTrait;
use Domains\Moodle\Traits\MoodleSyncableItem;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class UnitModule extends Model
{
    use MoodleSyncableItem;
    use SoftDeletes;

    protected $table = 'rto_subject_unit';

    use AvetMissTrait;

    protected static function booted()
    {
        static::created(function ($unit) {
            $subject = $unit->subjectLegacy;
            if (! $subject) {
                return;
            }
            SubjectMaterial::CheckAndCreateSubjectFolder($subject);
            SyncUnitsSetupService::SyncUnitModuleToNewSetup($unit, $subject, 'created');
            // SyncUnitsSetupService::SyncCourseSubjectToOldSetup($subject);
        });
        static::updated(function ($unit) {
            $subject = $unit->subjectLegacy;
            if (! $subject) {
                return;
            }
            SubjectMaterial::CheckAndCreateSubjectFolder($subject);
            SyncUnitsSetupService::SyncUnitModuleToNewSetup($unit, $subject, 'updated');
        });
        static::deleting(function ($unit) {
            $subject = $unit->subjectLegacy;
            if (! $subject) {
                return;
            }
            SyncUnitsSetupService::SyncUnitModuleToNewSetup($unit, $subject, 'deleted');
        });
    }

    public function subjectLegacy()
    {
        return $this->belongsTo(Subject::class, 'subject_id');
    }

    public function saveUnitModule($collegeId, $request)
    {

        $userId = Auth::user()->id;
        $objModule = new UnitModule;
        $objModule->college_id = $collegeId;
        if ($request->input('subject_name') == '') {
            $objSubject = new Subject;
            $objModule->subject_id = $objSubject->UnitBySubjectAdd($request);
        } else {
            $objModule->subject_id = $request->input('subject_name');
        }

        $objModule->vet_unit_code = $request->input('vet_unit_code');
        $objModule->unit_name = $request->input('unit_name');
        $objModule->unit_code = $request->input('unit_code');
        $objModule->description = $request->input('description');
        $objModule->unit_type = $request->input('unit_type');
        $objModule->field_education = $request->input('field_education');
        $objModule->nominal_hours = ($request->input('nominal_hours') != '') ? $request->input('nominal_hours') : 0;
        $objModule->tution_fees = $request->input('tution_fees');
        $objModule->module_unit_flag = $request->input('module_unit_flag');
        $objModule->vet_flag = $request->input('vet_flag');
        $objModule->work_placement = $request->input('work_placement');
        $objModule->AVETMISS_Report = ($request->input('AVETMISS_Report') == '1') ? '1' : null;
        $objModule->internal = ($request->input('internal') != '') ? 'Y' : 'N';
        $objModule->external = ($request->input('external') != '') ? 'Y' : 'N';
        $objModule->workplace_based_delivery = ($request->input('workplace_based_delivery') != '') ? 'Y' : 'N';
        $objModule->delivery_mode = $objModule->internal.$objModule->external.$objModule->workplace_based_delivery;
        $objModule->created_by = $userId;
        $objModule->updated_by = $userId;
        $objModule->save();
    }

    public function getUnitCode($subjectId)
    {
        return UnitModule::where('subject_id', '=', $subjectId)
            ->where('work_placement', '=', 'Yes')
            ->get(['id as unit_id', 'unit_code', 'unit_name']);

    }

    public function editUnitModule($moduleId, $request)
    {
        $userId = Auth::user()->id;
        $objModule = UnitModule::find($moduleId);

        $objModule->subject_id = $request->input('subject_name');
        $objModule->vet_unit_code = $request->input('vet_unit_code');
        $objModule->unit_name = $request->input('unit_name');
        $objModule->unit_code = $request->input('unit_code');
        $objModule->description = $request->input('description');
        $objModule->unit_type = $request->input('unit_type');
        $objModule->field_education = $request->input('field_education');
        $objModule->nominal_hours = ($request->input('nominal_hours') != '') ? $request->input('nominal_hours') : 0;
        $objModule->tution_fees = $request->input('tution_fees');
        $objModule->module_unit_flag = $request->input('module_unit_flag');
        $objModule->vet_flag = $request->input('vet_flag');
        $objModule->work_placement = $request->input('work_placement');
        $objModule->AVETMISS_Report = ($request->input('AVETMISS_Report') == '1') ? '1' : null;
        $objModule->internal = ($request->input('internal') != '') ? 'Y' : 'N';
        $objModule->external = ($request->input('external') != '') ? 'Y' : 'N';
        $objModule->workplace_based_delivery = ($request->input('workplace_based_delivery') != '') ? 'Y' : 'N';
        $objModule->delivery_mode = $objModule->internal.$objModule->external.$objModule->workplace_based_delivery;
        $objModule->updated_by = $userId;
        $objModule->save();
    }

    public function deleteUnitModule($moduleId)
    {
        return UnitModule::where('id', '=', $moduleId)->delete();
    }

    public function deleteUnitRecords($subjectId)
    {
        return UnitModule::where('subject_id', '=', $subjectId)->delete();
    }

    public function getUnitModuleList($perPage, $subjectId)
    {

        if ($subjectId == 0) {
            return UnitModule::where('college_id', '=', Auth::user()->college_id)
                ->orderBy('id', 'DESC')
                ->paginate($perPage);
        } else {
            return UnitModule::where('rto_subject_unit.college_id', '=', Auth::user()->college_id)
                ->where('rto_subject_unit.subject_id', '=', $subjectId)
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_subject_unit.subject_id')
                ->orderBy('rto_subject_unit.id', 'DESC')
                ->select('rto_subject.subject_name', 'rto_subject_unit.*')
                ->paginate($perPage);
        }
    }

    public function subjectModuleInsertV2($request, $lastInsertedId)
    {

        $loginData = Auth::user();
        $objUnit = new UnitModule;

        $objUnit->subject_id = $lastInsertedId;
        $objUnit->vet_unit_code = ($request->input('subject_code') != '') ? $request->input('subject_code') : null;
        $objUnit->unit_code = ($request->input('subject_code') != '') ? $request->input('subject_code') : null;
        $objUnit->unit_name = ($request->input('subject_name') != '') ? $request->input('subject_name') : null;
        $objUnit->description = null;
        $objUnit->unit_type = null;
        $objUnit->field_education = null;
        $objUnit->delivery_mode = 'NNN';
        $objUnit->nominal_hours = $request->input('contact_hours');
        $objUnit->tution_fees = null;
        $objUnit->module_unit_flag = null;
        $objUnit->vet_flag = null;
        $objUnit->AVETMISS_Report = null;
        $objUnit->internal = 'N';
        $objUnit->external = 'N';
        $objUnit->workplace_based_delivery = 'N';
        $objUnit->college_id = $loginData->college_id;
        $objUnit->created_by = $loginData->id;
        $objUnit->updated_by = $loginData->id;

        $objUnit->save();
    }

    public function insertExistingUnitsToSubject($request, $lastInsertedId)
    {

        $unitsData = UnitModule::where('college_id', '=', Auth::user()->college_id)
            ->whereIn('id', $request->input('unitNames'))
            ->get()->toArray();

        $loginData = Auth::user();
        for ($i = 0; $i < count($unitsData); $i++) {
            $objUnit = new UnitModule;
            $objUnit->subject_id = $lastInsertedId;
            $objUnit->vet_unit_code = $unitsData[$i]['vet_unit_code'];
            $objUnit->unit_code = $unitsData[$i]['unit_code'];
            $objUnit->unit_name = $unitsData[$i]['unit_name'];
            $objUnit->description = $unitsData[$i]['description'];
            $objUnit->unit_type = $unitsData[$i]['unit_type'];
            $objUnit->field_education = $unitsData[$i]['field_education'];
            $objUnit->delivery_mode = $unitsData[$i]['delivery_mode'];
            $objUnit->nominal_hours = $unitsData[$i]['nominal_hours'];
            $objUnit->tution_fees = $unitsData[$i]['tution_fees'];
            $objUnit->module_unit_flag = $unitsData[$i]['module_unit_flag'];
            $objUnit->vet_flag = $unitsData[$i]['vet_flag'];
            $objUnit->AVETMISS_Report = $unitsData[$i]['AVETMISS_Report'];
            $objUnit->internal = $unitsData[$i]['internal'];
            $objUnit->external = $unitsData[$i]['external'];
            $objUnit->workplace_based_delivery = $unitsData[$i]['workplace_based_delivery'];
            $objUnit->college_id = $loginData->college_id;
            $objUnit->created_by = $loginData->id;
            $objUnit->updated_by = $loginData->id;
            $objUnit->save();
        }
    }

    public function subjectModuleInsert($request, $lastInsertedId)
    {

        $college_id = Auth::user()->college_id;
        $loginUserId = Auth::user()->id;

        $unit_code = ($request->input('unit_code') != '') ? $request->input('unit_code') : null;
        $unit_display_code = ($request->input('unit_display_code') != '') ? $request->input('unit_display_code') : null;
        $unit_name = ($request->input('unit_name') != '') ? $request->input('unit_name') : null;
        $description = ($request->input('description') != '') ? $request->input('description') : null;
        $unit_type = ($request->input('unit_type') != '') ? $request->input('unit_type') : null;
        $field_of_education = ($request->input('field_of_education') != '') ? $request->input('field_of_education') : null;
        // $delivery_mode = ($request->input('delivery_mode') != '') ? $request->input('delivery_mode') : null;
        $scheduled_nominal_hours = ($request->input('scheduled_nominal_hours') != '') ? $request->input('scheduled_nominal_hours') : 0;
        $tution_fee = ($request->input('tution_fee') != '') ? $request->input('tution_fee') : null;
        $unit_flag = ($request->input('unit_flag') != '') ? $request->input('unit_flag') : null;
        $vet_flag = ($request->input('vet_flag') != '') ? $request->input('vet_flag') : null;
        $AVETMISS_Report = ($request->input('AVETMISS_Report') != '') ? $request->input('AVETMISS_Report') : null;
        $internal = ($request->input('internal') != '') ? 'Y' : 'N';
        $external = ($request->input('external') != '') ? 'Y' : 'N';
        $workplace_based_delivery = ($request->input('workplace_based_delivery') != '') ? 'Y' : 'N';
        $delivery_mode = $internal.$external.$workplace_based_delivery;

        $objUnit = new UnitModule;
        $objUnit->unit_code = $unit_code;
        $objUnit->subject_id = $lastInsertedId;
        $objUnit->vet_unit_code = $unit_display_code;
        $objUnit->unit_name = $unit_name;
        $objUnit->description = $description;
        $objUnit->unit_type = $unit_type;
        $objUnit->field_education = $field_of_education;
        $objUnit->delivery_mode = $delivery_mode;
        $objUnit->nominal_hours = $scheduled_nominal_hours;
        $objUnit->tution_fees = $tution_fee;
        $objUnit->module_unit_flag = $unit_flag;
        $objUnit->vet_flag = $vet_flag;
        $objUnit->AVETMISS_Report = $AVETMISS_Report;
        $objUnit->internal = $internal;
        $objUnit->external = $external;
        $objUnit->workplace_based_delivery = $workplace_based_delivery;
        $objUnit->college_id = $college_id;
        $objUnit->created_by = $loginUserId;
        $objUnit->updated_by = $loginUserId;
        $objUnit->save();
    }

    public function getSubjectModuleData($subjectId)
    {
        return UnitModule::where('subject_id', '=', $subjectId)->get();
    }

    public function getSubjectModuleArray($subjectId)
    {
        return UnitModule::where('subject_id', '=', $subjectId)
            ->where('college_id', '=', Auth::user()->college_id)
            ->get(['id', 'unit_code', 'unit_name'])
            ->toarray();
    }

    public function getSubjectModuleArrayV2($subjectId)
    {
        $unitData = UnitModule::where('subject_id', '=', $subjectId)
            ->where('college_id', '=', Auth::user()->college_id)
            ->get(['id', 'unit_code', 'unit_name'])
            ->toarray();

        $returnUnitData = [];
        for ($i = 0; $i < count($unitData); $i++) {
            $returnUnitData[$unitData[$i]['id']] = $unitData[$i]['unit_code'].' : '.$unitData[$i]['unit_name'];
        }

        if (empty($returnUnitData)) {
            $returnUnitData[''] = '-- No Unit Found --';
        }

        return $returnUnitData;
    }

    public function getAllUnits($collegeId)
    {

        $unitData = UnitModule::where('college_id', '=', $collegeId)
            ->get(['id', 'unit_code', 'unit_name'])
            ->groupBy('unit_code')
            ->toArray();

        return $unitData;
    }

    public function getFilterUnitModuleList($college_id, $perPage, $filter_by, $subjectId)
    {
        if ($subjectId == 0) {

            $arrUnitModuleList = UnitModule::where('college_id', '=', $college_id)->orderBy('id', 'DESC');
            if ($filter_by != '') {
                $arrUnitModuleList->where('rto_subject_unit.subject_id', '=', $filter_by);
            }
            $resultData = $arrUnitModuleList->paginate($perPage);

            return $resultData;
        } else {
            $arrUnitModuleList = where('rto_subject_unit.college_id', '=', $college_id)
                ->where('rto_subject_unit.subject_id', '=', $subjectId)
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_subject_unit.subject_id')
                ->orderBy('rto_subject_unit.id', 'DESC');

            if ($filter_by != '') {
                $arrUnitModuleList->where('rto_subject_unit.subject_id', '=', $filter_by);
            }
            $resultData = $arrUnitModuleList->paginate($perPage);

            return $resultData;
        }
    }

    public function getCourseUnitSummary($collegeId, $arrCourseSubjectId, $perPage = '', $studentID = '')
    {

        $arrsubject = [];
        for ($i = 0; $i < count($arrCourseSubjectId); $i++) {
            $arrsubject[$i] = $arrCourseSubjectId[$i]['subject_id'];
        }
        //        echo $perPage;exit;
        if (empty($perPage)) {
            return StudentUnitEnrollment::where('rsse.college_id', '=', $collegeId)
                ->join('rto_student_subject_enrolment as rsse', 'rsse.id', '=', 'rto_student_unit_enrollment.student_subject_enrollment_id')
                ->join('rto_subject_unit as rsu', 'rsu.id', '=', 'rto_student_unit_enrollment.unit_id')
                         //       ->where('rto_subject_unit.college_id', $collegeId)
                ->where('rsse.student_id', $studentID)
                ->whereIn('rsu.subject_id', $arrsubject)
                ->select('rto_student_unit_enrollment.compentency', 'rto_student_unit_enrollment.competency_date', 'rsu.vet_unit_code', 'rsu.unit_name')
                ->get();
        } else {
            return StudentUnitEnrollment::where('rsse.college_id', '=', $collegeId)
                ->join('rto_student_subject_enrolment as rsse', 'rsse.id', '=', 'rto_student_unit_enrollment.student_subject_enrollment_id')
                ->join('rto_subject_unit as rsu', 'rsu.id', '=', 'rto_student_unit_enrollment.unit_id')
                       //        ->where('rto_subject_unit.college_id', $collegeId)
                ->where('rsse.student_id', $studentID)
                ->whereIn('rsu.subject_id', $arrsubject)
                ->select('rto_student_unit_enrollment.compentency', 'rto_student_unit_enrollment.competency_date', 'rsu.vet_unit_code', 'rsu.unit_name')
                ->paginate($perPage);
        }
    }

    public function _getUnitModule($subject_id)
    {
        return UnitModule::join('rto_subject', 'rto_subject.id', '=', 'rto_subject_unit.subject_id')
            ->where('rto_subject_unit.college_id', '=', Auth::user()->college_id)
            ->where('rto_subject_unit.subject_id', '=', $subject_id)
            ->groupBy('rto_subject_unit.unit_name')
            ->get(['rto_subject_unit.unit_name', 'rto_subject_unit.unit_code', 'rto_subject_unit.id']);
    }

    // get unit id based on subject id from student-subject-enrolled
    public function getUnitId($collegeId, $subjectId)
    {
        return UnitModule::where('college_id', '=', $collegeId)
            ->where('subject_id', '=', $subjectId)
            ->get()->toArray();
    }

    public function getUnitData($subjectId)
    {
        $arrUnitModule = [];
        $arrUni = UnitModule::where('college_id', '=', Auth::user()->college_id)
            ->where('subject_id', '=', $subjectId)
            ->pluck('unit_name', 'id')->toArray();
        $arrUnitModule[''] = '--Select Unit--';

        return $arrUnitModule = $arrUnitModule + $arrUni;
    }

    // get subject_unit based on unit_id(primary)
    public function getUnitDetails($collegeId, $unitIdList)
    {
        return UnitModule::where('college_id', '=', $collegeId)
            ->whereIn('id', $unitIdList)
            ->get();
    }

    public function getUnitDetailsData($collegeId, $unitIdList)
    {
        return UnitModule::where('college_id', '=', $collegeId)
            ->where('id', $unitIdList)
            ->get()->toarray();
    }

    public function getUnitModuleInfo($collegeId, $subjectUnitId)
    {
        return UnitModule::where('college_id', $collegeId)
            ->where('id', $subjectUnitId)
            ->get(['id', 'unit_code', 'unit_name']);
    }

    public function getUnitModuleByColageIdForXlsFile($collegeId, $arrFilter)
    {

        $form_date = date('Y-m-d', strtotime($arrFilter['from_date']));
        $to_date = date('Y-m-d', strtotime($arrFilter['to_date']));

        $sql = StudentSubjectEnrolment::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
            ->leftjoin('rto_venue', 'rto_venue.id', '=', 'rto_student_subject_enrolment.vanue_location')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
            ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_subject_unit.subject_id')
            ->leftJoin('rto_student_courses', function ($join) {
                $join->on('rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id');
                $join->on('rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id');
            })
            ->orderby('rto_students.id')
            ->where('rto_students.college_id', '=', $collegeId);

        $sql->groupBy('rto_subject_unit.id');

        $sql = $this->applyGlobalFilter($sql, $arrFilter, 'nat60');

        // if (!empty($arrFilter['year'])) {
        //     if($arrFilter['reportType']==1 || $arrFilter['reportType']==2){
        //         $year = $arrFilter['year'];

        //         $sql->where(function($nest) use($year) {
        //             $nest->whereYear('rto_student_subject_enrolment.activity_finish_date', '=',$year);
        //             $nest->orWhereYear('rto_student_subject_enrolment.activity_start_date', '=', $year);
        //         });

        //     }
        // }
        // if (!empty($arrFilter['month']) && $arrFilter['reportType'] == 2) {
        //     $sql->whereMonth('rto_student_subject_enrolment.activity_start_date', '=', $arrFilter['month']);
        // }
        // if (!empty($arrFilter['stateName']) && $arrFilter['export_type'] == 1) {
        // //    $sql->where('rto_students.current_state', '=', $arrFilter['stateName']);
        //    $sql->where('rto_venue.state', '=', $arrFilter['stateName']);
        // }
        // if (!empty($arrFilter['from_date']) && !empty($arrFilter['to_date']) && $arrFilter['reportType'] == 3) {
        //     $sql->whereBetween('rto_student_subject_enrolment.activity_start_date', array($form_date, $to_date));
        // }
        // if (!empty($arrFilter['claim_only'])) {
        //     $sql->where('rto_student_courses.is_claim', '=', ($arrFilter['claim_only']));
        // }

        $sql = $this->applySmartAndSkillFilter($sql, $arrFilter);

        $result = $sql->get(['rto_subject.subject_name', 'rto_subject_unit.*', 'rto_venue.state',
            'rto_student_courses.is_claim',
            'rto_student_subject_enrolment.id as enrolment_id',
            'rto_student_subject_enrolment.activity_start_date',
            'rto_student_subject_enrolment.activity_finish_date']);

        return $result;
    }

    public function getUnitModuleByColageIdForNSWFile($collegeId, $arrFilter)
    {

        $sql = UnitModule::leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_subject_unit.subject_id')
            ->leftjoin('rto_unit_field_of_educations', 'rto_unit_field_of_educations.id', '=', 'rto_subject_unit.field_education')
            ->leftjoin('rto_student_subject_enrolment', 'rto_student_subject_enrolment.subject_id', '=', 'rto_subject.id')
            ->leftjoin('rto_venue', 'rto_venue.id', '=', 'rto_student_subject_enrolment.vanue_location')
            ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id')
            ->where('rto_subject.college_id', '=', $collegeId)
            ->orderBy('rto_subject_unit.id', 'DESC');
        $sql->groupBy('rto_subject.id');
        if (! empty($arrFilter['claim_stage'])) {
            $sql->leftjoin('rto_student_claim_tracking', 'rto_student_claim_tracking.student_id', '=', 'rto_student_courses.student_id');
            $sql->where('rto_student_claim_tracking.lodgement_type', $arrFilter['claim_stage']);
        }
        $sql->where('rto_venue.state', '=', 'NSW');

        $result = $sql->get(['rto_subject.subject_name', 'rto_subject_unit.*', 'rto_venue.state',
            'rto_student_courses.is_claim',
            'rto_unit_field_of_educations.avaitmiss_id as unit_field_of_educations_id',
            'rto_student_subject_enrolment.activity_start_date',
            'rto_student_subject_enrolment.activity_finish_date']);

        return $result;
    }

    public function countSubjectUnitVocational($subjectId)
    {
        $countSubjectUni = UnitModule::where('college_id', '=', Auth::user()->college_id)
            ->where('subject_id', '=', $subjectId)
            ->where('work_placement', '=', 'Yes')
            ->get(['unit_name', 'id'])->count();

        return $countSubjectUni;
    }
}
