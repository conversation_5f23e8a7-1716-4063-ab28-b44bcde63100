@props([
'logo' => '',
'navbar' => '',
'menus' => menuItemsForCurrentUser(),
'tempMainMenu' => '',
'mainmenu' => '',
'activeIndex' => null,
])

@hasSection('mainmenu')
@php $tempMainMenu = app()->view->getSections()['mainmenu']; @endphp
@endif

@php
$currRoute = Route::current()->getName();
$currUrl = '/' . Request::path();
$userRole = Session::get('userRole');
$currentRole = Session::get('currentRole');
$menuTitle = isset($mainmenu) && !empty($mainmenu) ? $mainmenu : $tempMainMenu;
$setup_letter_and_email_template = Session::get('arrPermissionList.setup_letter_and_email_template');
$isExpanded =
(Cookie::get('_x_isSidebarExpanded') ?? ($_COOKIE['_x_isSidebarExpanded'] ?? 'true')) === 'true' ? 1 : 0;

$initClass = [
'sidebar' => $isExpanded ? 'tw-sidebar__expanded w-64' : 'tw-sidebar__collapsed w-20',
'toggleBtn' => $isExpanded ? 'me-0 text-gray-300' : '-me-8 !bg-primary-blue-500 rotate-180 text-white',
'menus' => $isExpanded ? '!px-2' : '!px-5',
'menuLink' => $isExpanded ? 'pe-3' : 'pe-2',
'logoFull' => $isExpanded ? 'block' : 'none',
'logoSm' => $isExpanded ? 'none' : 'block',
'menuText' => $isExpanded ? 'block' : 'none',
];
$isSidebarExpandedFromCookie = request()->cookie('alpine-cookie:sidebarExpanded') !== 'false';
@endphp

@foreach ($menus as $index => $menuItem)
@if (in_array($currRoute, $menuItem['activeurls']))
@php
$activeIndex = $index;
break;
@endphp
@endif
@endforeach
<div class="tw-sidebar-navigation h-screen max-w-64" id="tw-sidebar" x-ref="sidebar"
    x-data="{ mobileOpen: false, isSidebarExpanded: $persist(true).using(cookieStorage), expandedMenu: {{ $activeIndex !== null ? $activeIndex : 'null' }}, showHoverMenu: null, isDelayed: false }"
    x-on:toggle-sidebar.window="mobileOpen = true" :class="mobileOpen ? 'tw-sidebar-navigation__open' : ''"
    x-init="isDelayed = !isSidebarExpanded" @scroll.window="localStorage.setItem('sidebarScroll', $el.scrollTop)">
    <div class="tw-sidebar-navigation__overlay fixed bg-black/80 inset-0 z-50" style="display: none" x-show="mobileOpen"
        x-on:click="mobileOpen = false"></div>
    <aside
        class="tw-sidebar flex flex-col py-8 lg:py-5 bg-gray-800 gap-5 transition-all duration-300 z-50 {{ $initClass['sidebar'] }}"
        x-ref="sidebar" :class="isSidebarExpanded ? 'tw-sidebar__expanded !w-64' : 'tw-sidebar__collapsed !w-20'">
        <div class="relative w-full h-8 lg:h-6 flex items-center justify-between px-4">
            <a href="{{ url('/') }}" class="flex items-center overflow-hidden">
                @if ($logo->isEmpty())
                <img src="{{ asset('v2/img/logo-dark.svg') }}" alt="Logo" class="h-7 lg:h-5 w-auto"
                    style="display: {{ $initClass['logoFull'] }};" x-show="isSidebarExpanded"
                    x-transition:enter="transition ease-out duration-300 delay-75"
                    x-transition:enter-start="opacity-0 scale-90" x-transition:enter-end="opacity-100 scale-100"
                    x-transition:leave="transition ease-in duration-300"
                    x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-90">
                <img src="{{ asset('v2/img/galaxy-icon-logo.svg') }}" alt="Logo" class="h-8 w-8 ml-2"
                    style="display: {{ $initClass['logoSm'] }};" x-show="!isSidebarExpanded"
                    x-transition:enter="transition ease-out duration-300 delay-100"
                    x-transition:enter-start="opacity-0 scale-90" x-transition:enter-end="opacity-100 scale-100"
                    x-transition:leave="transition-none hidden opacity-0">
                @else
                {{ $logo }}
                @endif

            </a>
            <button
                class="tw-sidebar__toggle-btn p-1 rounded-full hover:shadow-2xl focus:ring-2 hover:bg-primary-blue-500 focus:bg-primary-blue-500/90 focus:ring-offset-1 focus:ring-offset-transparent z-10 transition-all duration-75 !text-white {{ $initClass['toggleBtn'] }}"
                :class="isSidebarExpanded ? '!me-0 !bg-transparent !rotate-0' : '!-me-8 !bg-primary-blue-500 !rotate-180'"
                x-on:click="isSidebarExpanded = !isSidebarExpanded; $refs.sidebar.classList.remove('tw-sidebar__expanded', 'tw-sidebar__collapsed');"
                x-transition>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10 16L6 12M6 12L10 8M6 12L14 12M18 20V4" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </button>
            <button x-on:click="mobileOpen = false" class="block lg:hidden text-white w-6 h-6" x-show="mobileOpen">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <path fill="currentColor"
                        d="m4.397 4.554l.073-.084a.75.75 0 0 1 .976-.073l.084.073L12 10.939l6.47-6.47a.75.75 0 1 1 1.06 1.061L13.061 12l6.47 6.47a.75.75 0 0 1 .072.976l-.073.084a.75.75 0 0 1-.976.073l-.084-.073L12 13.061l-6.47 6.47a.75.75 0 0 1-1.06-1.061L10.939 12l-6.47-6.47a.75.75 0 0 1-.072-.976l.073-.084z" />
                </svg>
            </button>
        </div>
        @if ($navbar->isEmpty())
        <ul class="tw-menu-list overflow-y-auto" :class="isSidebarExpanded ? '!px-2' : '!px-5'"
            data-menu="{{$menuTitle}}">
            @foreach ($menus as $index => $menuItem)
            @if (in_array($menuTitle, $menuItem['mainmenu']))
            @php
            $isActiveUrl = in_array($currRoute, $menuItem['activeurls']);
            $menuClass = [
            'bg' =>
            $isActiveUrl && !isset($menuItem['sub_menu'])
            ? 'active'
            : '',
            'collapsedBg' =>
            $isActiveUrl && isset($menuItem['sub_menu']) ? 'bg-primary-blue-500' : '',
            'withSubmenu' => isset($menuItem['sub_menu']) ? 'tw-menu-item__with-submenu' : '',
            'isActive' => $isActiveUrl ? 'tw-menu-item__active' : '',
            ];
            @endphp
            <li x-ref="menuButton{{ $index }}"
                class="tw-menu-item {{ $menuClass['withSubmenu'] }} {{ $menuClass['isActive'] }}">
                <a href="{{ $menuItem['url'] }}" title="{{ $menuItem['label'] }}"
                    class="glob-toooltip tw-menu-link flex items-center h-auto lg:h-10 rounded-md focus:outline-none focus:shadow-outline leading-5 justify-start ps-2 py-4 lg:py-2 {{ $menuClass['bg'] }}"
                    :class="isSidebarExpanded ? '!pe-3' : '!pe-2 {{ $menuClass['collapsedBg'] }}'"
                    x-on:click="expandedMenu = expandedMenu === {{ $index }} ? null : {{ $index }};"
                    x-on:mouseenter="showHoverMenu = showHoverMenu === {{ $index }} ? null : {{ $index }}"
                    x-on:mouseleave="showHoverMenu = (showHoverMenu === {{ $index }}) ? null : showHoverMenu">
                    <span class="icon-wrapper">
                        {!! $menuItem['svgicon'] !!}
                    </span>
                    <div class="tw-menu-link-text text-base lg:text-sm !ml-2 min-w-fit whitespace-nowrap"
                        style="display: {{ $initClass['menuText'] }};" x-show="isSidebarExpanded"
                        x-transition:enter="transition ease-out duration-300"
                        x-transition:enter-start="opacity-0 scale-90" x-transition:enter-end="opacity-100 scale-100"
                        x-transition:leave="transition ease-in duration-300"
                        x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-90">{{
                        $menuItem['label'] }}</div>
                    @if (isset($menuItem['sub_menu']))
                    @php
                    $shouldRotate = $activeIndex === $index;
                    @endphp
                    <div class="tw-toggle-icon ml-auto transition-all duration-75 {{ $isSidebarExpandedFromCookie ? '!block' : '!hidden' }} {{ $shouldRotate ? 'rotate-90' : '' }}"
                        :class="{
                                            '!block': isSidebarExpanded,
                                            '!hidden': !isSidebarExpanded,
                                            'rotate-90': expandedMenu === {{ $index }}
                                        }">
                        <span class="tw-menu-toggle">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M5.73966 3.20041C5.43613 3.48226 5.41856 3.95681 5.70041 4.26034L9.22652 8L5.70041 11.7397C5.41856 12.0432 5.43613 12.5177 5.73967 12.7996C6.0432 13.0815 6.51775 13.0639 6.7996 12.7603L10.7996 8.51034C11.0668 8.22258 11.0668 7.77743 10.7996 7.48966L6.7996 3.23966C6.51775 2.93613 6.0432 2.91856 5.73966 3.20041Z"
                                    fill="currentColor" />
                            </svg>
                        </span>
                    </div>
                    @endif
                </a>
                @if (isset($menuItem['sub_menu']))
                <ul x-ref="subMenuList" class="tw-submenu-list overflow-hidden ml-7 duration-300 space-y-0.5"
                    style="{{ $activeIndex === $index ? 'display: block;' : 'display: none;' }}"
                    x-show=" isSidebarExpanded && expandedMenu==={{ $index }}" x-collapse x-collapse.duration.500ms>
                    @foreach ($menuItem['sub_menu'] as $subMenuItem)
                    @php
                    $isSubActive =
                    (isset($subMenuItem['subactiveurls']) &&
                    in_array($currRoute, $subMenuItem['subactiveurls'])) ||
                    $currUrl == $subMenuItem['url'];
                    $subMenuClass = [
                    'bg' => $isSubActive
                    ? 'active'
                    : '',
                    ];
                    @endphp
                    <li class="tw-submenu-item">
                        <a href="{{ $subMenuItem['url'] }}"
                            class="tw-submenu-link tw-menu-link flex items-center h-10 px-3 rounded-md transition-all duration-150 ease-in-out focus:outline-none focus:shadow-outline leading-5 {{ $subMenuClass['bg'] }}">
                            <span class="tw-menu-link-text text-base lg:text-sm ease-in-out"
                                :class="isSidebarExpanded ? 'opacity-100' : 'opacity-0'">{{ $subMenuItem['label']
                                }}</span>
                        </a>
                    </li>
                    @endforeach
                </ul>
                {{-- Collapsed Menu --}}
                <div class=" absolute left-[5.25rem] top-0 z-50" style="display: none;"
                    x-show="!isSidebarExpanded && showHoverMenu === {{ $index }}"
                    x-on:mouseenter="showHoverMenu = {{ $index }}" x-on:mouseleave="showHoverMenu = null"
                    x-anchor.left-start="$refs.menuButton{{ $index }}">
                    <ul class="overflow-hidden duration-300 bg-gray-800 !p-2 rounded-md ml-2.5 shadow-lg min-w-40">
                        @foreach ($menuItem['sub_menu'] as $subMenuItem)
                        @php
                        $isSubActive =
                        (isset($subMenuItem['subactiveurls']) &&
                        in_array($currRoute, $subMenuItem['subactiveurls'])) ||
                        $currUrl == $subMenuItem['url'];
                        $hoverMenuClass = [
                        'bg' => $isSubActive
                        ? 'bg-primary-blue-500 hover:bg-primary-blue-500'
                        : 'hover:bg-gray-700',
                        'text' => $isSubActive ? 'text-primary-blue-50' : 'text-gray-300',
                        ];
                        @endphp
                        <li>
                            <a href="{{ $subMenuItem['url'] }}" x-on:click="expandedMenu = {{ $index }};"
                                class="flex items-center h-10 px-3 rounded-md transition-all duration-150 ease-in-out focus:outline-none focus:shadow-outline leading-5 {{ $hoverMenuClass['bg'] }}">
                                <span class="text-base lg:text-sm ease-in-out {{ $hoverMenuClass['text'] }}">{{
                                    $subMenuItem['label'] }}</span>
                            </a>
                        </li>
                        @endforeach
                    </ul>
                </div>
                @endif
            </li>
            @if (@$menuItem['gap_after'])
            <div class="tw-menu-divider h-px w-60 bg-gray-700 mx-auto my-3"></div>
            @endif
            @endif
            @endforeach
        </ul>
        @else
        {{ $navbar }}
        @endif
    </aside>
</div>

<script>
    const sidebar = document.querySelector(".tw-menu-list");

    const previousMenu = localStorage.getItem("previous-menu");
    const currentMenu = sidebar?.getAttribute("data-menu");

    if (previousMenu !== currentMenu) {
        if (sidebar) sidebar.scrollTop = 0;
    } else {
    const savedScroll = localStorage.getItem("sidebar-scroll");
    if (savedScroll !== null && sidebar) {
        sidebar.scrollTop = parseInt(savedScroll, 10);
    }
    }

    // Save scroll position before page unload
    window.addEventListener("beforeunload", () => {
        if (sidebar) {
            localStorage.setItem("sidebar-scroll", sidebar.scrollTop);
            localStorage.setItem("previous-menu", currentMenu);
        }
    });
</script>