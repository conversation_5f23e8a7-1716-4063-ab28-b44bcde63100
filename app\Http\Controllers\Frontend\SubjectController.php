<?php

namespace App\Http\Controllers\Frontend;

use App\Classes\CourseAutoCompleteController;
use App\Http\Controllers\Controller;
// use Illuminate\Support\Facades\Request;
use App\Model\Courses;
use App\Model\CourseSubject;
use App\Model\CourseUnitFieldOfEducations;
use App\Model\ElementOfUnitCompetency;
use App\Model\GradingType;
use App\Model\StudentSubjectEnrolment;
use App\Model\Subject;
use App\Model\UnitModule;
use App\Model\v2\BroadType;
use App\Model\v2\NarrowType;
use App\Model\v2\SubNarrowType;
use Auth;
use Config;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

// use Config;
class SubjectController extends Controller
{
    public $timestamps = false;

    public function __construct()
    {
        // $this->rememberToken
        $this->middleware('auth');
    }

    public function viewSubjectList(Request $request)
    {
        $perPage = Config::get('constants.pagination.perPage');
        $arrGradingType = Config::get('constants.arrGradingType');
        $college_id = Auth::user()->college_id;
        $objSubject = new Subject;

        //        $subject_by = '';
        //        $course_type = '';
        //        $search_string = '';
        //        //$input = Input::All();
        //        if($request->ajax()){
        //            //print_R($request->input('subject_by'));exit;
        //            $subject_by = $request->input('subject_by');
        //            $course_type = $request->input('course_type');
        //            $search_string = $request->input('search_string');
        //        }
        //        if((!empty($subject_by)) || (!empty($course_type)) || (!empty($search_string))){
        //            $subjectListDatas = $objSubject->getSubjectListFilter($college_id, $perPage, $request);
        //        }else{
        //            $subjectListDatas = $objSubject->getSubjectList($college_id, $perPage);
        //        }
        $subjectListDatas = $objSubject->getSubjectList($college_id, $perPage);
        // $subjectListData = $objSubject->getSubjectData($subjectListDatas, $arrGradingType);

        $objCourses = new Courses;
        $arrCourseType = $objCourses->getCourseTypeList($college_id);
        $data['header'] = [
            'title' => 'Subject Manage',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Courses' => '',
                'Subject Manage' => '',
            ]];
        $data['pagetitle'] = 'Subject List';
        $data['plugincss'] = [];
        $data['css'] = [''];
        $data['pluginjs'] = [];
        $data['js'] = ['subject.js'];
        $data['funinit'] = ['subject.initSubjectList()'];
        $data['activateValue'] = 'Courses';
        $data['arrCourseType'] = $arrCourseType;
        $data['pagination'] = $subjectListDatas->links();
        $data['subjectListData'] = $subjectListDatas;
        $data['mainmenu'] = 'administration';

        return view('frontend.subject.view-subject-list', $data);
    }

    public function addSubject(Request $request)
    {
        $college_id = Auth::user()->college_id;
        $loginUserId = Auth::user()->id;

        $objCourses = new Courses;
        $arrCourseTypes = $objCourses->getCourseTypeListing($college_id);
        $arrLevelEducations = $objCourses->getLevelEducation();

        $objFieldEducation = new CourseUnitFieldOfEducations;
        $arrFieldEducation = $objFieldEducation->getUnitFieldEducation();
        $data['arrFieldEducation'] = $arrFieldEducation;

        $data['arrBroadTypeArr'] = (new BroadType)->getAllBroadType($college_id);
        $data['arrNarrowTypeArr'] = (new NarrowType)->getAllNarrowType($college_id);
        $data['arrSubNarrowTypeArr'] = (new SubNarrowType)->getAllSubNarrowType($college_id);

        $objUnit = new UnitModule;
        $arrUnits = $objUnit->getAllUnits($college_id);
        $arrCourseType = [];
        $arrCourseType[''] = '- - Select Course Type - -';
        // $arrCourseType['Any'] = 'Any';
        foreach ($arrCourseTypes as $key => $CourseType) {
            $CourseTypeId = $CourseType['id'];
            $CourseTypeTitle = $CourseType['title'];
            $arrCourseType[$CourseTypeId] = $CourseTypeTitle;
        }
        $objGradingType = new GradingType;
        $arrGradingType = $objGradingType->getGradingTypeList($college_id);
        // $arrGradingTypes = $objGradingType->combineGradingTypeListV2($arrGradingType);
        // dd($arrGradingTypes);
        // $arrGradingTypes = Config::get('constants.arrGradingType');
        $arrDeliveryMode = Config::get('constants.arrDeliveryMode');

        if ($request->isMethod('post')) {

            $is_higher_edu = $request->input('is_higher_education');

            if ($request->input('unit_yes_no') == 0 || $is_higher_edu == 1) {
                $validationArr = [
                    'subject_code' => 'required',
                    'subject_name' => 'required',
                ];
            } elseif ($request->input('existing_unit_yes_no') == 1) {
                $validationArr = [
                    'unitNames' => 'required',
                ];
            } else {
                $validationArr = [
                    'subject_code' => 'required',
                    'subject_name' => 'required',
                    'unit_code' => 'required',
                    'unit_display_code' => 'required',
                    'unit_name' => 'required',
                    'field_of_education' => 'required',
                    // 'delivery_mode'           => 'required',
                    'scheduled_nominal_hours' => 'required|integer',
                    'tution_fee' => 'required',
                ];
            }
            $validator = Validator::make($request->all(), $validationArr);

            if ($validator->fails()) {
                return redirect(route('add-subject'))->withErrors($validator)->withInput();
            }

            $subject = new Subject;
            if ($request->input('existing_unit_yes_no') != 1) {
                $validation = $subject->checkSubjectUnitCode($college_id, $request);
                if ($validation->fails()) {
                    return redirect(route('add-subject'))->withErrors($validation)->withInput();
                }
            }

            // $subject = new Subject();
            $lastInsertedId = $subject->SubjectAdd($request);
            if ($request->input('unit_yes_no') == 0 || $is_higher_edu == 1) {
                if ($is_higher_edu == 1) {
                    (new UnitModule)->subjectModuleInsertV2($request, $lastInsertedId);
                }
                $request->session()->flash('session_success', 'Subject is Saved Successfully.');

                return redirect(route('view-subject-list'));
            }

            if ($request->input('existing_unit_yes_no') == 1) {
                (new UnitModule)->insertExistingUnitsToSubject($request, $lastInsertedId);
                $request->session()->flash('session_success', 'Subject is Saved Successfully.');

                return redirect(route('view-subject-list'));
            }

            $subjectModule = new UnitModule;
            $subjectModule->subjectModuleInsert($request, $lastInsertedId);

            $request->session()->flash('session_success', 'Subject is Saved Successfully.');

            return redirect(route('view-subject-list'));
        }

        $data['header'] = [
            'title' => 'Add Courses Subject',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Subject Manage' => route('view-subject-list'),
                'Add Subject' => '',
            ]];
        $data['pagetitle'] = 'Subject Add';
        $data['plugincss'] = ['select2/select2.min.css'];
        $data['css'] = ['jquery-ui.css'];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'jQuery/additional-methods.min.js',
            'select2/select2.full.min.js'];
        $data['js'] = ['subject.js'];
        $data['funinit'] = ['subject.initSubjectAdd()'];
        $data['activateValue'] = 'Courses';
        $data['arrCourseType'] = $arrCourseType;
        $data['arrUnits'] = $arrUnits;
        $data['arrLevelEducations'] = $arrLevelEducations;
        $data['arrGradingType'] = $arrGradingType;

        $subject = new Subject;
        $subject->is_long_indicator = 0;
        $subject->is_assessment = 1;
        $data['subject'] = $subject;

        $data['arrDeliveryMode'] = $arrDeliveryMode;
        $data['mainmenu'] = 'administration';

        return view('frontend.subject.add-subject', $data);
    }

    public function editSubject(Request $request)
    {
        $subjectId = $request->id;
        $subjectData = Subject::find($subjectId);
        $objUnitData = new UnitModule;
        $arrUnitData = $objUnitData->getSubjectModuleData($subjectId);

        $college_id = Auth::user()->college_id;
        $objCourses = new Courses;
        $arrCourseTypes = $objCourses->getCourseTypeListing($college_id);
        $arrLevelEducations = $objCourses->getLevelEducation();

        $arrCourseType = [];
        $arrCourseType[''] = '- - Select Course Type - -';
        // $arrCourseType['Any'] = 'Any';
        foreach ($arrCourseTypes as $key => $CourseType) {
            $CourseTypeId = $CourseType['id'];
            $CourseTypeTitle = $CourseType['title'];
            $arrCourseType[$CourseTypeId] = $CourseTypeTitle;
        }
        $objGradingType = new GradingType;
        $arrGradingType = $objGradingType->getGradingTypeList($college_id);
        // $arrGradingTypes = $objGradingType->combineGradingTypeListV2($arrGradingType);

        // $arrGradingType = Config::get('constants.arrGradingType');
        $arrDeliveryMode = Config::get('constants.arrDeliveryMode');
        $checkEditCondition = Subject::select('college_id')->where('id', $subjectId)->get()->toArray();
        $editCollegeId = $checkEditCondition[0]['college_id'];

        if ($request->isMethod('post')) {
            $validationArr = [
                'subject_name' => 'required',
            ];
            $validator = Validator::make($request->all(), $validationArr);
            if ($validator->fails()) {
                return redirect(route('add-subject'))->withErrors($validator)->withInput();
            }

            $subject = new Subject;
            $subject->SubjectEdit($request, $subjectId);

            $request->session()->flash('session_success', 'Subject Successfully Updated.');

            return redirect(route('view-subject-list'));
        }

        $data['arrFieldEducation'] = (new CourseUnitFieldOfEducations)->getUnitFieldEducation();
        $data['header'] = [
            'title' => 'Edit Courses Subject',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Subject Manage' => route('view-subject-list'),
                'Edit Subject' => '',
            ]];
        $data['pagetitle'] = 'Subject Edit';
        $data['plugincss'] = [];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'jQuery/additional-methods.min.js'];
        $data['js'] = ['subject.js'];
        $data['funinit'] = ['subject.initSubjectAdd()'];
        $data['activateValue'] = 'Courses';
        $data['arrCourseType'] = $arrCourseType;
        $data['arrLevelEducations'] = $arrLevelEducations;
        $data['arrGradingType'] = $arrGradingType;
        $data['arrDeliveryMode'] = $arrDeliveryMode;
        $data['id'] = $subjectId;
        $data['subject'] = $subjectData;
        $data['unit'] = $arrUnitData;
        $data['mainmenu'] = 'administration';

        $data['arrBroadTypeArr'] = (new BroadType)->getAllBroadType($college_id);
        $data['arrNarrowTypeArr'] = (new NarrowType)->getAllNarrowType($college_id);
        $data['arrSubNarrowTypeArr'] = (new SubNarrowType)->getAllSubNarrowType($college_id);

        if ($editCollegeId == $college_id) {
            return view('frontend.subject.add-subject', $data);
        } else {
            return view('frontend.error_page.unauthorized', $data);
        }
    }

    public function deleteSubject(Request $request, $subjectId)
    {
        $college_id = Auth::user()->college_id;
        $objSubject = new Subject;
        $objUnit = new UnitModule;

        $subjectDatas = $objSubject->getSubjectlistCollegeIdWise($subjectId);
        $deleteCollegeId = $subjectDatas[0]->college_id;
        if ($deleteCollegeId == $college_id) {
            $objCourseSubject = new CourseSubject;
            $arrCourseSubject = $objCourseSubject->_checkSubjectExist($subjectId);

            if (count($arrCourseSubject) > 0) {
                $validator = 'This subject can not be delete. This subject is already assign to course';

                return redirect(route('view-subject-list'))
                    ->withErrors($validator)
                    ->withInput();
            }

            $data = $objSubject->deleteSubjectRecords($subjectId);
            $objUnit->deleteUnitRecords($subjectId);

            if ($data) {
                $request->session()->flash('session_success', 'Subject Successfully Deleted.');
            }

            return redirect(route('view-subject-list'));
        } else {
            return redirect(route('view-subject-list'));
        }
    }

    public function addUnitModule(Request $request)
    {
        $college_id = Auth::user()->college_id;
        $loginUserId = Auth::user()->id;
        $objFieldEducation = new CourseUnitFieldOfEducations;
        $arrFieldEducation = $objFieldEducation->getUnitFieldEducation();
        $objSubject = new Subject;
        $arrSubject = $objSubject->getSubject($college_id);

        $arrDeliveryMode = Config::get('constants.arrDeliveryMode');

        $objCourses = new Courses;
        $arrCourseTypes = $objCourses->getCourseTypeListing($college_id);
        $arrLevelEducations = $objCourses->getLevelEducation();

        $objUnit = new UnitModule;
        $arrUnits = $objUnit->getAllUnits($college_id);

        $arrCourseType = [];
        $arrCourseType[''] = '- - Select Course Type - -';
        // $arrCourseType['Any'] = 'Any';
        foreach ($arrCourseTypes as $key => $CourseType) {
            $CourseTypeId = $CourseType['id'];
            $CourseTypeTitle = $CourseType['title'];
            $arrCourseType[$CourseTypeId] = $CourseTypeTitle;
        }

        $objGradingType = new GradingType;
        $arrGradingType = $objGradingType->getGradingTypeList($college_id);
        $arrGradingTypes = $objGradingType->combineGradingTypeListV2($arrGradingType);

        if ($request->isMethod('post')) {
            $is_higher_edu = $request->input('is_higher_education');
            if ($is_higher_edu == '1') {
                $request->session()->flash('session_error', 'This course type not allowed to add unit data.');

                return redirect(route('view-unit-module-list'));
            }

            if ($request->input('existing_unit_yes_no') == 1) {
                $validator = Validator::make($request->all(), [
                    'unitNames' => 'required',
                    'subject_name' => 'required',
                ]);
            } else {
                if ($request->input('subject_name') == '') {
                    $validator = Validator::make($request->all(), [
                        'unit_code' => 'required|unique:rto_subject_unit',
                        'vet_unit_code' => 'required|unique:rto_subject_unit',
                        'unit_name' => 'required',
                    ]);
                } else {
                    $validator = Validator::make($request->all(), [
                        'course_type' => 'required',
                        'grading_type' => 'required',
                        'unit_code' => 'required|unique:rto_subject_unit',
                        'vet_unit_code' => 'required|unique:rto_subject_unit',
                        'unit_name' => 'required',
                    ]);
                }
            }

            if (isset($request->id) && $validator->fails()) {
                $data['subjectId'] = $request->id;

                return redirect(route('manage-unit', ['id' => $request->id]))
                    ->withErrors($validator)
                    ->withInput();
            }

            if ($validator->fails()) {
                return redirect(route('add-unit-module'))
                    ->withErrors($validator)
                    ->withInput();
            }

            if ($request->input('existing_unit_yes_no') != 1) {
                $validation = $objSubject->checkSubjectUnitCode($college_id, $request);
                if ($validation->fails()) {
                    return redirect(route('add-unit-module'))->withErrors($validation)->withInput();
                }
            }

            if ($request->input('existing_unit_yes_no') == 1) {
                (new UnitModule)->insertExistingUnitsToSubject($request, $request->input('subject_name'));
                $request->session()->flash('session_success', 'Unit Module is Saved Successfully.');

                return redirect(route('view-unit-module-list'));
            }

            $UnitModule = new UnitModule;
            $UnitModule->saveUnitModule($college_id, $request);
            $request->session()->flash('session_success', 'Unit Module is Saved Successfully.');

            return redirect(route('view-unit-module-list'));
        }

        if (isset($request->id)) {
            $data['subjectId'] = $request->id;
            $data['header'] = [
                'title' => $arrSubject[$data['subjectId']].' Unit Add',
                'breadcrumb' => [
                    'Home' => route('user_dashboard'),
                    'Manage Unit' => route('view-subject-list'),
                    'Add Unit' => '',
                ]];
        } else {
            $data['header'] = [
                'title' => 'Courses Unit Add',
                'breadcrumb' => [
                    'Home' => route('user_dashboard'),
                    'Manage Unit' => route('view-subject-list'),
                    'Add Unit' => '',
                ]];
        }

        $data['pagetitle'] = 'Unit Information Add';
        $data['plugincss'] = ['select2/select2.min.css'];
        $data['css'] = ['jquery-ui.css'];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'jQuery/additional-methods.min.js', 'jQueryUI/jquery-ui.js', 'select2/select2.full.min.js'];
        $data['js'] = ['unitModule.js'];
        $data['funinit'] = ['unitModule.initAddUnitModule()'];
        $data['activateValue'] = 'Courses';
        $data['arrCourseType'] = $arrCourseType;
        $data['arrGradingType'] = $arrGradingTypes;
        $data['arrUnits'] = $arrUnits;
        $data['arrFieldEducation'] = $arrFieldEducation;
        $data['arrSubject'] = $arrSubject;
        $data['unitId'] = '';
        $data['arrDeliveryMode'] = $arrDeliveryMode;
        $data['mainmenu'] = 'administration';
        $data['subject_page'] = 'subject_page';

        return view('frontend.subject.add-unit-module', $data);
    }

    public function viewUnitModule(Request $request)
    {

        $perPage = Config::get('constants.pagination.perPage');
        $college_id = Auth::user()->college_id;
        $objUnitModule = new UnitModule;

        $objSubject = new Subject;
        $arrSubject = $objSubject->getSubject($college_id);

        if (count($arrSubject) <= 1) {
            unset($arrSubject['']);
            $arrSubject[''] = 'No data found';
        }
        $arrSubjectData = $objSubject->getSubjectlistCollegeIdWise($request->id);

        // echo $request->id;exit;

        if (isset($request->id)) {
            $unitModuleListDatas = $objUnitModule->getUnitModuleList($perPage, $request->id);
            $data['subjectId'] = $request->id;
        } else {
            $unitModuleListDatas = $objUnitModule->getUnitModuleList($perPage, 0);
        }
        if (! isset($data['subjectId'])) {
            $data['header'] = [
                'title' => 'Unit Module List',
                'breadcrumb' => [
                    'Home' => route('user_dashboard'),
                    'View Unit Module List' => '',
                ]];
        } else {
            $data['header'] = [
                'title' => $arrSubjectData[0]->subject_name.' Unit Module',
                'breadcrumb' => [
                    'Home' => route('user_dashboard'),
                    'Setup Subject' => route('view-subject-list'),
                    $arrSubjectData[0]->subject_name.' Unit' => '',
                ]];
        }

        $data['pagetitle'] = 'Unit Module List';
        $data['plugincss'] = ['select2/select2.min.css'];
        $data['css'] = [''];
        $data['pluginjs'] = ['select2/select2.full.min.js'];
        $data['js'] = ['unitModule.js'];
        $data['funinit'] = ['unitModule.initUnitModule()'];
        $data['activateValue'] = 'Courses';
        $data['arrSubject'] = $arrSubject;
        $data['arrSubjectData'] = $arrSubjectData;

        $data['pagination'] = $unitModuleListDatas->links();
        $data['unitModuleListDatas'] = $unitModuleListDatas;
        $data['mainmenu'] = 'administration';
        $data['unit_module_page'] = 'unit_module_page';

        return view('frontend.subject.view-unit-module-list', $data);
    }

    public function editUnitModule(Request $request)
    {
        $college_id = Auth::user()->college_id;
        $loginUserId = Auth::user()->id;
        $moduleId = $request->id;

        $moduleData = UnitModule::find($moduleId);

        $objFieldEducation = new CourseUnitFieldOfEducations;
        $arrFieldEducation = $objFieldEducation->getUnitFieldEducation();

        $objSubject = new Subject;
        $arrSubject = $objSubject->getSubject($college_id);

        $checkEditCondition = UnitModule::select('college_id')->where('id', $moduleId)->get()->toArray();
        $editCollegeId = $checkEditCondition[0]['college_id'];

        $arrDeliveryMode = Config::get('constants.arrDeliveryMode');
        if ($request->isMethod('post')) {
            DB::beginTransaction();
            $validator = Validator::make($request->all(), [
                'subject_name' => 'required',
                'unit_code' => 'required|unique:rto_subject_unit,unit_code,'.$moduleId,
                'vet_unit_code' => 'required|unique:rto_subject_unit,vet_unit_code,'.$moduleId,
                'unit_name' => 'required',
            ]);

            if ($validator->fails()) {
                return redirect(route('edit-unit-module', ['id' => $moduleId]))
                    ->withErrors($validator)
                    ->withInput();
            }

            $UnitModule = new UnitModule;
            $UnitModule->editUnitModule($moduleId, $request);

            $request->session()->flash('session_success', 'Unit Module is Saved Successfully.');
            DB::commit();

            return redirect(route('view-unit-module-list'));
        }

        $data['header'] = [
            'title' => 'Courses Unit',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Manage Unit' => route('view-subject-list'),
                'Edit Unit' => '',
            ]];

        $data['pagetitle'] = 'Unit Information Add';
        $data['plugincss'] = ['select2/select2.min.css'];
        $data['css'] = ['jquery-ui.css'];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'jQuery/additional-methods.min.js', 'jQueryUI/jquery-ui.js', 'select2/select2.full.min.js'];
        $data['js'] = ['unitModule.js'];
        $data['funinit'] = ['unitModule.initAddUnitModule()'];
        $data['activateValue'] = 'Courses';
        $data['arrFieldEducation'] = $arrFieldEducation;
        $data['arrSubject'] = $arrSubject;
        $data['arrDeliveryMode'] = $arrDeliveryMode;
        $data['moduleData'] = $moduleData;
        $data['mainmenu'] = 'administration';
        $data['subject_page'] = 'subject_page';
        $data['unitId'] = $moduleId;

        if ($editCollegeId == $college_id) {
            return view('frontend.subject.add-unit-module', $data);
        } else {
            return view('frontend.error_page.unauthorized', $data);
        }
    }

    public function deleteunitmodule(Request $request, $moduleId)
    {
        $college_id = Auth::user()->college_id;
        $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
        $arrStudentSubjectEnrolment = $objStudentSubjectEnrolment->checkUnitId($college_id, $moduleId);
        if ($arrStudentSubjectEnrolment == 0) {
            $moduleData = new UnitModule;
            $arrModuleData = $moduleData->deleteUnitModule($moduleId);
            $request->session()->flash('session_success', 'Unit Module Successfully Deleted.');
        } else {
            $request->session()->flash('session_error', 'This unit is enroll so can not delete.');
        }

        return redirect(route('view-unit-module-list'));
    }

    public function elementOfUnitCompetency(Request $request, $subject_id)
    {

        $objUnitModule = new UnitModule;
        $arrUnit = $objUnitModule->getUnitData($subject_id);
        $college_id = Auth::user()->college_id;
        $loginUserId = Auth::user()->id;
        $moduleId = $request->id;

        $objElementOfUnitCompetency = new ElementOfUnitCompetency;
        $arrElementOfUnitCompetency = $objElementOfUnitCompetency->getElementOfUnitCompetency($subject_id);

        if ($request->isMethod('post')) {
            $validator = Validator::make($request->all(), [
                'unit_id' => 'required',
                'competency_criteria' => 'required',
                'notes' => 'required',
            ]);

            if ($validator->fails()) {
                return redirect(route('element-of-unit-competency', ['id' => $subject_id]))
                    ->withErrors($validator)
                    ->withInput();
            }

            $objElementOfUnitCompetency = new ElementOfUnitCompetency;
            $arrElementOfUnitCompetency = $objElementOfUnitCompetency->saveElementOfUnitCompetency($request);
            $request->session()->flash($arrElementOfUnitCompetency['type'], $arrElementOfUnitCompetency['message']);

            return redirect(route('element-of-unit-competency', $subject_id));
        }
        $data['header'] = [
            'title' => 'Subject Competency View',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Subject Manage' => route('view-subject-list'),
                'Subject View' => '',
            ]];
        $data['pagetitle'] = 'Unit Information Add';
        $data['plugincss'] = [];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['unitModule.js'];
        $data['funinit'] = ['unitModule.initElementOfUnitCompetency()'];
        $data['activateValue'] = 'Courses';
        $data['arrUnit'] = $arrUnit;
        $data['subject_id'] = $subject_id;
        $data['arrElementOfUnitCompetency'] = $arrElementOfUnitCompetency;
        $data['mainmenu'] = 'administration';

        return view('frontend.subject.element-of-unit-competency', $data);
    }

    public function editElementCompetency(Request $request, $primaryId)
    {

        $objElementOfUnitCompetency = new ElementOfUnitCompetency;
        $arrElementOfUnitCompetencyData = $objElementOfUnitCompetency->getElementOfUnitCompetencyData($primaryId);
        $subId = $arrElementOfUnitCompetencyData[0]['subject_id'];
        $objUnitModule = new UnitModule;
        $arrUnit = $objUnitModule->getUnitData($subId);
        $college_id = Auth::user()->college_id;
        $loginUserId = Auth::user()->id;
        $moduleId = $request->id;

        if ($request->isMethod('post')) {
            $objElementOfUnitCompetency = new ElementOfUnitCompetency;
            $arrElementOfUnitCompetency = $objElementOfUnitCompetency->editElementOfUnitCompetency($request);
            $request->session()->flash($arrElementOfUnitCompetency['type'], $arrElementOfUnitCompetency['message']);
            //            $request->session()->flash('session_success', 'Element Of Unit Competency is Edit Successfully.');
            if ($arrElementOfUnitCompetency['type'] == 'session_success') {
                return redirect(route('element-of-unit-competency', $subId));
            } else {
                return redirect(route('edit-element-competency', ['id' => $moduleId]))->withInput();
            }
        }
        $data['header'] = [
            'title' => 'Subject Competency Edit',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Subject Manage' => route('view-subject-list'),
                'Subject View' => '',
            ]];
        $data['pagetitle'] = 'Unit Information Add';
        $data['plugincss'] = [];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['unitModule.js'];
        $data['funinit'] = ['unitModule.initElementOfUnitCompetency()'];
        $data['activateValue'] = 'Courses';
        $data['arrUnit'] = $arrUnit;
        $data['subject_id'] = $subId;
        $data['arrElementOfUnitCompetencyData'] = $arrElementOfUnitCompetencyData;
        $data['mainmenu'] = 'administration';

        return view('frontend.subject.edit-element-of-unit-competency', $data);
    }

    public function deleteElementCompetency(Request $request, $primaryId)
    {
        $objElementOfUnitCompetency = new ElementOfUnitCompetency;
        $arrElementOfUnitCompetencyData = $objElementOfUnitCompetency->getElementOfUnitCompetencyData($primaryId);
        $subId = $arrElementOfUnitCompetencyData[0]['subject_id'];
        $objElementOfUnitCompetency = new ElementOfUnitCompetency;
        $arrElementOfUnitCompetency = $objElementOfUnitCompetency->deleteElementOfUnitCompetency($primaryId);
        $request->session()->flash('session_success', 'Element Of Unit Competency is Delete Successfully.');

        return redirect(route('element-of-unit-competency', $subId));
    }

    public function ajaxAction(Request $request)
    {

        $action = $request->input('action');

        switch ($action) {

            case 'searchSubject':
                $subject_by = $request->input('data.subject_by');
                $course_type = $request->input('data.course_type');
                $search_string = $request->input('data.search_string');
                $this->_searchSubject($subject_by, $course_type, $search_string);
                break;
            case 'searchUnitModule':
                $filter_by = $request->input('data.filter_by');
                $this->_searchUnitModule($filter_by, $request);
                break;
            case 'changeStatus':
                $id = $request->input('data.id');
                $status = $request->input('data.status');
                $this->_changeUnitStatus($id, $status);
                break;
            case 'getSubjectList':
                $college_id = Auth::user()->college_id;
                $objSubject = new Subject;
                $subjectListData = $objSubject->getSubjectListV2($college_id, $request);
                echo json_encode($subjectListData);
                exit;
                break;
            case 'getNarrowType':
                $collegeId = Auth::user()->college_id;
                $broadTypeId = $request->input('data.broad_type');
                $arrNarrowTypeList = (new NarrowType)->getNarrowTypeList($collegeId, $broadTypeId);
                echo json_encode($arrNarrowTypeList);
                exit;
                break;
            case 'getSubNarrowType':
                $narrowTypeId = $request->input('data.narrow_type');
                $collegeId = Auth::user()->college_id;
                $arrSubNarrowTypeList = (new SubNarrowType)->getSubNarrowTypeList($collegeId, $narrowTypeId);
                echo json_encode($arrSubNarrowTypeList);
                exit;
                break;
        }
        exit;
    }

    public function _searchSubject($subject_by, $course_type, $search_string)
    {

        $college_id = Auth::user()->college_id;
        $perPage = Config::get('constants.pagination.perPage');
        $objSubject = new Subject;
        $subjectListDatas = $objSubject->getFilterSubjectList($college_id, $perPage, $subject_by, $course_type, $search_string);
        $data['subjectListData'] = $subjectListDatas;

        $resultTable = view('frontend.subject.search-subject', $data)->render();
        echo $resultTable;
        exit;
    }

    public function _searchUnitModule($filter_by, $request)
    {

        $college_id = Auth::user()->college_id;
        $perPage = Config::get('constants.pagination.perPage');

        $objUnitModule = new UnitModule;

        if (isset($request->id)) {
            $unitModuleListDatas = $objUnitModule->getFilterUnitModuleList($college_id, $perPage, $filter_by, $request->id);
            $data['subjectId'] = $request->id;
        } else {
            $unitModuleListDatas = $objUnitModule->getFilterUnitModuleList($college_id, $perPage, $filter_by, 0);
        }
        // print_r($unitModuleListDatas);exit;
        // $subjectListDatas = $objSubject->getFilterSubjectList($college_id,$perPage,$view_by);
        $data['unitModuleListDatas'] = $unitModuleListDatas;
        $data['pagination'] = '';

        $resultTable = view('frontend.subject.search-unit-module', $data)->render();
        echo $resultTable;
        exit;

        //        $pagination = view('frontend.subject.pagination', $data)->render();
        //        $result = array($resultTable, $pagination);
        //        echo json_encode($result);exit;
    }

    public function _changeUnitStatus($id, $status)
    {
        $objUnit = new ElementOfUnitCompetency;
        $arrValueList = $objUnit->changeUnitStatus($id, $status);
        $objUnitResult['status'] = 'alert-success';
        $objUnitResult['message'] = 'Element Of Unit Of Competency Status Change successfully.';
        echo json_encode($objUnitResult);
        exit;
    }

    public function autocomplete(Request $request)
    {
        $term = $request->term;
        $resArray = [];
        $objCourseAutoCompleteController = new CourseAutoCompleteController;
        $result = $objCourseAutoCompleteController->autoSuggestValue($term, 'unit');

        return response()->json($result);
    }
}
