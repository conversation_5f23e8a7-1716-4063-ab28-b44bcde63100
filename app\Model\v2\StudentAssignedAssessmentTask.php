<?php

namespace App\Model\v2;

use App\User;
use DB;
use Auth;
use Config;
use Domains\Moodle\Facades\Moodle;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Support\Traits\CreaterUpdaterTrait;
use App\Traits\ActivityLog\TaskAssessmentActivityLog;

class StudentAssignedAssessmentTask extends Model {

    use CreaterUpdaterTrait;
    use LogsActivity;
    use TaskAssessmentActivityLog;

    protected $table = 'rto_student_assigned_assessment_tasks';
    protected $fillable = [
                            'student_id',
                            'college_id',
                            'assessment_task_id',
                            'course_type_id',
                            'year',
                            'semester_id',
                            'term',
                            'subject_id',
                            'batch',
                            'student_id',
                            'course_id',
                            'marks',
                            'subject_attempts',
                            'competency',
                            'comments',
                            'extended_due_date',
                            'extended_due_date_reason',
                            'is_locked',
                            'is_approved',
                            'result_comment',
                            'created_at',
                            'created_by',
                            'updated_at',
                            'updated_by',
    ];
    protected $logAttributes = [
                            'student_id',
                            'college_id',
                            'assessment_task_id',
                            'course_type_id',
                            'year',
                            'semester_id',
                            'term',
                            'subject_id',
                            'batch',
                            'student_id',
                            'course_id',
                            'marks',
                            'subject_attempts',
                            'competency',
                            'comments',
                            'extended_due_date',
                            'extended_due_date_reason',
                            'is_locked',
                            'is_approved',
                            'result_comment',
                            'created_at',
                            'created_by',
                            'updated_at',
                            'updated_by',
    ];
    public function student(){
        return $this->hasOne(Student::class, 'id', 'student_id');
    }
    public function assessment(){
        return $this->hasOne(AssessmentTask::class, "id", "assessment_task_id");
    }
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn(string $eventName) => "Student Subject Enrolment has been {$eventName}");
    }

    public function tapActivity(Activity $activity, string $eventName){
        $activity->log_name = (new self)->getMorphClass() . '_' . $this->student_id .'_'. $this->course_id;
        $activity->subject_type = self::class;
        $studentName = $this->student->first_name .' '.$this->student->family_name ;
        $detailData['studentName'] = $studentName;
        $detailData['unitName'] = '';
        $detailData['assessmentTask'] = $this->assessment->task_name;
        $detailData['old_group_id'] = '';
        $detailData['studentId'] = @$this->student->generated_stud_id;

        $routeName = (request()->route())?request()->route()->getName():'';
        if($routeName == "galaxy.moodle.webhook"){
            $causerId = $this->getGalaxyUserIdForMoodleUser();
            if($causerId){
                $activity->causer_type = "app_users"; //morphAlias(User::class);
                $activity->causer_id = $causerId;
            }
        }elseif($routeName == ""){
            $activity->causer_type = "app_users"; //morphAlias(User::class);
            $activity->causer_id = $this->created_by;
        }

        $activity->description = $this->getCustomDescriptionForEvent($eventName,$detailData,$activity);
    }

    private function getGalaxyUserIdForMoodleUser()
    {
        $loginMoodleUserId = request()->input('userid');
        $moodleUser = Moodle::users()->getByField('id', $loginMoodleUserId)->first();

        //return $moodleUser ? Users::where('email', $moodleUser->email)->value('id') : null;
        if ($moodleUser) {
            $galaxyUser = Users::where('email', $moodleUser->email)->first();
            return $galaxyUser ? $galaxyUser->id : null;
        }

        return null;
    }

    public function course(){
        return $this->hasOne(StudentAssignedAssessmentTask::class, 'id', 'id')
                ->join('rto_student_subject_enrolment', function ($join) {
                    $join->on('rto_student_subject_enrolment.batch', '=', 'rto_student_assigned_assessment_tasks.batch')
                            ->on('rto_student_subject_enrolment.student_id', '=', 'rto_student_assigned_assessment_tasks.student_id');
                })
                ->leftJoin('rto_courses', function ($join) {
                    $join->on('rto_courses.id', '=', 'rto_student_subject_enrolment.course_id');
                })
                ->select([
                    "rto_student_assigned_assessment_tasks.id",
                    "rto_student_subject_enrolment.id as enrollment_id",
                    "rto_student_subject_enrolment.course_id",
                    "rto_courses.course_type_id",
                    "rto_courses.is_superseded",
                    "rto_courses.superseded_date",
                    "rto_courses.national_code",
                    "rto_courses.course_code",
                    "rto_courses.cricos_code",
                    "rto_courses.course_name",
                    "rto_courses.effective_start",
                    "rto_courses.effective_end",
                    "rto_courses.activated_now",
                    "rto_courses.status",
                ])
                ->groupBy('rto_student_subject_enrolment.student_id')
                ->groupBy('rto_student_subject_enrolment.batch')
                ->groupBy('rto_student_subject_enrolment.course_id');
    }


    public function modifyChangedProperties()
    {
        return [
            // 'status' => fn($model, $currentValue) => new \Support\DTO\LabelValue('Status Label', 'Hello'),
            // 'is_locked' => function($model, $currentValue){
            //     $mapped = [
            //         0 => 'Locked',
            //         1 => 'Unlocked'
            //     ];
            //     return new \Support\DTO\LabelValue('Locked Label', @$mapped[$currentValue]);
            // }
        ];
    }
}
