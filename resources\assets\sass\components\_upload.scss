.k-filemanager-upload-dialog {
    .k-upload {
        border-width: 0;
        .k-dropzone {
            border-width: 1px;
        }

        .k-upload-files .k-file-validation-message {
            margin-top: 0.25rem;
        }
    }
    .k-upload-files {
        margin-top: 1rem;
        width: calc(100% + 3rem);
        margin-left: -1.5rem;
        border-bottom: 1px solid var(--color-gray-200);
        .k-file {
            padding-inline: 1.5rem;
        }
    }
}

.tw-upload {
    .k-upload {
        border-width: 0;
        .k-dropzone,
        .k-upload-dropzone {
            display: none;
        }
        .k-upload-files {
            border: none;
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1rem;

            .k-file {
                padding: unset;
                border-width: 0;
                width: 100%;
            }
        }
    }
    &--default {
        .k-upload {
            .k-upload-files {
                flex-direction: column;
            }
        }
    }
    .k-external-dropzone {
        .k-dropzone-inner {
            border: 1px dashed var(--color-gray-300);
            border-radius: 0.5rem;
            cursor: pointer;

            .k-dropzone-icon {
                color: var(--color-primary-blue-500);
            }

            &:hover {
                border-color: var(--color-primary-blue-500);
            }
        }
    }
    &--custom-list-progress {
        .k-external-dropzone {
            padding-inline: 1rem;
        }
    }

    &--custom-list {
        .k-actions.k-actions-end {
            display: none;
        }
    }
}

.custom-upload-style {
    .k-file {
        width: auto !important;
        .chip-custom-style {
            border-radius: 0.25rem !important;
        }
    }
}

.custom-dropzone-uploader {
    ul {
        li {
            width: max-content !important;
        }
    }
    .k-external-dropzone {
        padding-inline: 0;
    }
}

.custom-dropzone-uploader {
    .k-dropzone-inner {
        .k-i-upload {
            margin-bottom: 16px;
            &::before {
                content: "";
                display: inline-block;
                width: 48px;
                height: 48px;
                background-image: url("data:image/svg+xml,%3Csvg width='39' height='38' viewBox='0 0 39 38' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21.8555 5H5.85547C4.7946 5 3.77719 5.42143 3.02704 6.17157C2.2769 6.92172 1.85547 7.93913 1.85547 9V29M1.85547 29V33C1.85547 34.0609 2.2769 35.0783 3.02704 35.8284C3.77719 36.5786 4.7946 37 5.85547 37H29.8555C30.9163 37 31.9337 36.5786 32.6839 35.8284C33.434 35.0783 33.8555 34.0609 33.8555 33V25M1.85547 29L11.0275 19.828C11.7776 19.0781 12.7948 18.6569 13.8555 18.6569C14.9161 18.6569 15.9334 19.0781 16.6835 19.828L21.8555 25M33.8555 17V25M33.8555 25L30.6835 21.828C29.9334 21.0781 28.9161 20.6569 27.8555 20.6569C26.7948 20.6569 25.7776 21.0781 25.0275 21.828L21.8555 25M21.8555 25L25.8555 29M29.8555 5H37.8555M33.8555 1V9M21.8555 13H21.8755' stroke='%239CA3AF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
                background-size: cover;
                vertical-align: middle;
            }
        }
    }
}
