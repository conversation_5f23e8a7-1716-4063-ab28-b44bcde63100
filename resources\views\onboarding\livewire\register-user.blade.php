<div>
    <p class="w-full text-center text-3xl font-extrabold leading-9 text-gray-900">Sign up to Galaxy</p>
    <form id="registerForm"
          class="space-y-6"
          action="#"
          method="post"
          x-data="{ loading: false }"
          x-on:submit.prevent="cleanAllInvalid(); loading=true; $wire.call('save')"
          x-on:redirect.window="window.location.href=$event.detail;"
          x-on:lwresponse.window="console.log($event);setTimeout(function(){loading=false;hcaptcha.reset()}, 600);">
        <input type="hidden"
               name="_token"
               value="{{ csrf_token() }}">

        {{-- @if (request('plan'))
            <input type="hidden"
                   name="plan"
                   value="{{ request('plan') }}" />
        @endif --}}

        <div
             class="{{ $errors->has('form.college_name') ? 'invalid' : '' }} inline-flex w-full flex-col items-start justify-start space-y-1">
            <p class="text-sm font-medium leading-5 text-gray-700">College Name</p>
            <div class="relative flex w-full flex-wrap items-center">
                <input type="text"
                       name="college_name"
                       wire:model.defer="form.college_name"
                       placeholder="College/Institute Name"
                       class="inputbox w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm leading-5 text-gray-700 shadow-sm">
                @error('form.college_name')
                    <div class="errorsvg pointer-events-none absolute inset-y-2 right-0 flex items-center pr-3">
                        <svg class="h-5 w-5 text-red-500"
                             xmlns="http://www.w3.org/2000/svg"
                             viewBox="0 0 20 20"
                             fill="currentColor"
                             aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                  clip-rule="evenodd" />
                        </svg>
                    </div>
                @enderror
            </div>
            @error('form.college_name')
                <p class="error-message mb-0 w-full text-xs text-red-400">{{ $message }}</p>
            @enderror
        </div>

        <div
             class="{{ $errors->has('form.domain') ? 'invalid' : '' }} mt-6 inline-flex w-full flex-col items-start justify-start space-y-1">
            <p class="text-sm font-medium leading-5 text-gray-700">Subdomain</p>
            <div class="relative flex w-full items-center">
                <input type="text"
                       name="domain"
                       placeholder="Domain"
                       wire:model.defer="form.domain"
                       x-on:keyup="$event.target.value=$event.target.value.toString().replace(/\s+/g, '-')"
                       class="inputbox w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm leading-5 text-gray-700 shadow-sm">
                <div
                     class="{{ $errors->has('form.domain') ? 'right-[30px]' : '' }} absolute flex items-center bg-white right-1" style="top:1px;bottom:1px;">
                    <label class="p-2 font-bold text-gray-500">{{ config('constants.sub_doamin_post_fix') }}</label>
                </div>
                @if ($errors->has('form.domain'))
                    <div class="errorsvg pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <svg class="h-5 w-5 text-red-500"
                             xmlns="http://www.w3.org/2000/svg"
                             viewBox="0 0 20 20"
                             fill="currentColor"
                             aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                  clip-rule="evenodd" />
                        </svg>
                    </div>
                @endif
            </div>
            <p class="mt-2 text-sm text-gray-500">Only alphanumeric characters allowed.</p>
            @error('form.domain')
                <p class="error-message mb-0 w-full text-xs text-red-400">{{ $message }}</p>
            @enderror
        </div>

        <h3>Primary Account</h3>
        <div
             class="{{ $errors->has('form.name') ? 'invalid' : '' }} inline-flex w-full flex-col items-start justify-start space-y-1">
            <p class="text-sm font-medium leading-5 text-gray-700">Full Name</p>
            <div class="relative flex w-full flex-wrap items-center">
                <input type="text"
                       name="name"
                       wire:model.defer="form.name"
                       placeholder="Full Name"
                       class="inputbox w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm leading-5 text-gray-700 shadow-sm">
                @error('form.name')
                    <div class="errorsvg pointer-events-none absolute inset-y-2 right-0 flex items-center pr-3">
                        <svg class="h-5 w-5 text-red-500"
                             xmlns="http://www.w3.org/2000/svg"
                             viewBox="0 0 20 20"
                             fill="currentColor"
                             aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                  clip-rule="evenodd" />
                        </svg>
                    </div>
                @enderror
            </div>
            @error('form.name')
                <p class="error-message mb-0 w-full text-xs text-red-400">{{ $message }}</p>
            @enderror
        </div>
        <div
             class="{{ $errors->has('form.username') ? 'invalid' : '' }} inline-flex w-full flex-col items-start justify-start space-y-1">
            <p class="text-sm font-medium leading-5 text-gray-700">Username</p>
            <div class="relative flex w-full items-center">
                <input type="text"
                       name="username"
                       value="{{ old('username') }}"
                       wire:model.defer="form.username"
                       placeholder="Username"
                       class="inputbox w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm leading-5 text-gray-700 shadow-sm">
                @if ($errors->has('form.username'))
                    <div class="errorsvg pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <svg class="h-5 w-5 text-red-500"
                             xmlns="http://www.w3.org/2000/svg"
                             viewBox="0 0 20 20"
                             fill="currentColor"
                             aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                  clip-rule="evenodd" />
                        </svg>
                    </div>
                @endif
            </div>
            @error('form.username')
                <p class="error-message mb-0 w-full text-xs text-red-400">{{ $message }}</p>
            @enderror
        </div>
        <div
             class="{{ $errors->has('form.email') ? 'invalid' : '' }} inline-flex w-full flex-col items-start justify-start space-y-1">
            <p class="text-sm font-medium leading-5 text-gray-700">Email address</p>
            <div class="relative flex w-full items-center">
                <input type="email"
                       name="email"
                       wire:model.defer="form.email"
                       placeholder="Email Address"
                       class="inputbox w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm leading-5 text-gray-700 shadow-sm">
                @if ($errors->has('form.email'))
                    <div class="errorsvg pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <svg class="h-5 w-5 text-red-500"
                             xmlns="http://www.w3.org/2000/svg"
                             viewBox="0 0 20 20"
                             fill="currentColor"
                             aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                  clip-rule="evenodd" />
                        </svg>
                    </div>
                @endif
            </div>
            @error('form.email')
                <p class="error-message mb-0 w-full text-xs text-red-400">{{ $message }}</p>
            @enderror
        </div>



        @if (session('session_error'))
            <div id="errSection"
                 style="width:100% !important;">
                <div class="inline-flex items-start justify-start space-x-2">
                    <svg class="h-5 w-5 text-red-500"
                         xmlns="http://www.w3.org/2000/svg"
                         viewBox="0 0 20 20"
                         fill="currentColor"
                         aria-hidden="true">
                        <path fill-rule="evenodd"
                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                              clip-rule="evenodd" />
                    </svg>
                    <p class="text-sm leading-5 text-red-500"> {{ session('session_error') }}</p>
                </div>
            </div>
        @endif
        @if (session('session_success'))
            <div id="errSection"
                 style="width:100% !important;">
                <div class="inline-flex items-start justify-start space-x-2">
                    <svg class="h-5 w-5 text-green-500"
                         xmlns="http://www.w3.org/2000/svg"
                         viewBox="0 0 20 20"
                         fill="currentColor"
                         aria-hidden="true">
                        <path fill-rule="evenodd"
                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                              clip-rule="evenodd" />
                    </svg>
                    <p class="text-sm leading-5 text-green-500"> {{ session('session_success') }}</p>
                </div>
            </div>
        @endif




        @if (session('message'))
            <div id="errSection"
                 style="width:100% !important;">
                <div class="inline-flex items-start justify-start space-x-2">
                    <svg class="h-5 w-5 text-green-500"
                         xmlns="http://www.w3.org/2000/svg"
                         viewBox="0 0 20 20"
                         fill="currentColor"
                         aria-hidden="true">
                        <path fill-rule="evenodd"
                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                              clip-rule="evenodd" />
                    </svg>
                    <p class="text-sm leading-5 text-green-500"> {{ session('message') }}</p>
                </div>
            </div>
        @endif

        @php
            $fieldName = 'form.captcha';
        @endphp
        <div x-data="{}"
             x-init="hcaptcha.render('h-captcha-{{ $fieldName }}', { sitekey: '{{ config('services.captcha.site_key') }}', callback: (e) => @this.set('{{ $fieldName }}', e), 'expired-callback': (e) => console.log('expired') })"
             class="{{ $errors->has($fieldName) ? 'invalid' : '' }} space-y-2">
            {{-- <span class="text-sm font-medium leading-4 text-gray-700">
                CAPTCHA (anti-bot)

            </span> --}}

            <div id="h-captcha-{{ $fieldName }}"
                 wire:ignore></div>


            @error($fieldName)
                <p class="error-message mb-0 mt-5 w-full text-xs text-red-400">{{ $message }}</p>
            @enderror
            <p class="text-sm text-gray-600"></p>
        </div>

        <p class="text-sm text-gray-700">
            This site is protected by <a href="https://www.hCaptcha.com">hCaptcha</a> and its
            <a href="https://www.hcaptcha.com/privacy">Privacy Policy</a> and
            <a href="https://www.hcaptcha.com/terms">Terms of Service</a> apply.

        </p>




        {{-- <x-button title="Delete Reason"
                  data-sitekey="{{ config('services.captcha.site_key') }}"
                  data-callback='captchaHandle'
                  data-action='submit'
                  type="button"
                  target="setFormCaptcha"
                  class="g-recaptcha mt-6 inline-flex w-full items-center justify-center rounded-2xl bg-primary-blue-500 px-6 py-2 text-xl leading-7 text-white shadow hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-primary-blue-500 focus:ring-offset-2"
                  loading="Please Wait..">
            Sign Up
        </x-button> --}}

        <button type="submit"
                class="g-recaptcha mt-6 inline-flex w-full items-center justify-center rounded-2xl bg-primary-blue-500 px-6 py-2 text-xl leading-7 text-white shadow hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-primary-blue-500 focus:ring-offset-2"
                x-bind:disabled="loading"
                x-bind:class="{ disabled: loading }"
                x-text="loading ? 'Please Wait...' : 'Sign Up'">
            Sign up
        </button>
    </form>
</div>
