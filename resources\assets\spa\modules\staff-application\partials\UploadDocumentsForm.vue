<template>
    <FormElement class="space-y-6">
        <Card
            :pt="{
                root: 'max-w-full mx-auto p-0 md:p-0 shadow rounded-lg border border-gray-300 mx-8 lg:mx-20',
                header: 'flex items-center justify-between px-6 py-4 border-b border-gray-200',
                content: 'pb-4 pt-3 px-6',
            }"
        >
            <template #header>
                <h2 class="text-lg font-semibold text-gray-900">
                    Upload Documents
                </h2>
                <div class="flex items-center gap-2">
                    <p class="text-xxs">
                        Accepted files:
                        <span class="font-bold">PDF and DOCX</span>
                    </p>
                    <div class="h-2 w-px bg-gray-300"></div>
                    <p class="text-xxs">
                        Max Size:
                        <span class="font-bold">1 MB</span>
                    </p>
                </div>
            </template>
            <template #content>
                <div class="space-y-4">
                    <div
                        class="custom-uploader rounded-lg border border-gray-200"
                    >
                        <PreviewPane
                            :show="showPreview"
                            @close-preview="handlePreviewClose"
                            @show-preview="handleViewDetail"
                            :documentName="documentName"
                            :files="gridData"
                            v-model:documentName="documentName"
                            @next="handleNextClick"
                            @prev="handlePrevClick"
                        >
                            <template #content>
                                <table class="h-fit w-full table-auto">
                                    <thead>
                                        <tr class="bg-gray-50">
                                            <template
                                                v-for="(
                                                    item, index
                                                ) in contractsColumn"
                                                :key="index"
                                            >
                                                <th
                                                    class="h-10 border-b border-gray-200 px-4 py-2 text-left"
                                                    :class="
                                                        item.width
                                                            ? `min-w-[${item.width}px]`
                                                            : 'w-full'
                                                    "
                                                >
                                                    <span
                                                        class="text-left text-xs font-normal uppercase leading-[0.875rem] text-gray-600"
                                                        >{{ item.title }}</span
                                                    >
                                                </th>
                                            </template>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <template
                                            v-for="(field, index) in this.data
                                                .agentChecklistData"
                                            :key="index"
                                        >
                                            <tr>
                                                <td
                                                    class="border-b border-gray-200 px-4 py-3"
                                                >
                                                    <span
                                                        class="text-sm font-medium leading-5 text-gray-800"
                                                    >
                                                        {{
                                                            field.document_name
                                                        }}
                                                        <sup
                                                            class="text-red-500"
                                                            >*</sup
                                                        >
                                                    </span>
                                                </td>
                                                <td
                                                    class="border-b border-gray-200 px-4 py-3"
                                                >
                                                    <Field
                                                        :id="field.id"
                                                        :name="field.id"
                                                        :label="'Upload Photos'"
                                                        :hintMessage="'Hint: Select your additional photos'"
                                                        :component="'myTemplate'"
                                                        :validator="
                                                            field.is_compulsory
                                                                ? validateRequiredFile
                                                                : null
                                                        "
                                                        :multiple="false"
                                                        :autoUpload="false"
                                                        @add="
                                                            handleOnAdd(
                                                                $event,
                                                                field,
                                                            )
                                                        "
                                                        @remove="
                                                            handleOnRemove(
                                                                $event,
                                                                field,
                                                            )
                                                        "
                                                    >
                                                        <template
                                                            v-slot:myTemplate="{
                                                                props,
                                                            }"
                                                        >
                                                            <FileUploader
                                                                v-bind="props"
                                                                @change="
                                                                    props.onChange
                                                                "
                                                                @blur="
                                                                    props.onBlur
                                                                "
                                                                @focus="
                                                                    props.onFocus
                                                                "
                                                                :value="
                                                                    field.uploads
                                                                "
                                                                :restrictions="{
                                                                    allowedExtensions:
                                                                        [
                                                                            '.pdf',
                                                                            '.docx',
                                                                        ],
                                                                    maxFileSize: 1000000,
                                                                }"
                                                                :defaultFiles="
                                                                    field.uploads
                                                                "
                                                                :buttonLabel="'Upload File'"
                                                                :isCustomUploader="
                                                                    false
                                                                "
                                                                :isPadding="
                                                                    true
                                                                "
                                                                :isAgencyUploadForm="
                                                                    true
                                                                "
                                                                :iconName="'download_arrow_up'"
                                                            />
                                                        </template>
                                                    </Field>
                                                </td>
                                                <td
                                                    class="border-b border-gray-200 px-4 py-3"
                                                >
                                                    <span
                                                        class="flex items-center gap-2"
                                                    >
                                                        <Tooltip
                                                            :anchor-element="'target'"
                                                            :position="'top'"
                                                            :parentTitle="true"
                                                            :tooltipClassName="'flex !p-1.5'"
                                                            :class="'w-full'"
                                                            v-if="
                                                                actions[0].value
                                                            "
                                                        >
                                                            <Button
                                                                :variant="'text'"
                                                                class="h-fit text-gray-400"
                                                                :title="
                                                                    actions[0]
                                                                        .text
                                                                "
                                                                :ref="
                                                                    !actions[0]
                                                                        .value
                                                                        ? 'actionMenu'
                                                                        : ''
                                                                "
                                                                @click="
                                                                    handleViewDetail(
                                                                        field,
                                                                    )
                                                                "
                                                            >
                                                                <file-icon
                                                                    :name="
                                                                        actions[0]
                                                                            .icon
                                                                    "
                                                                />
                                                            </Button>
                                                        </Tooltip>
                                                    </span>
                                                </td>
                                            </tr>
                                        </template>
                                    </tbody>
                                </table>
                            </template>
                            <template #preview>
                                <div class="custom-pdf p-8 px-0 pb-0 pt-0">
                                    <div
                                        class="tw-filemanager tw-media-manager !h-full !border-0"
                                        ref="previewPdf"
                                    ></div>
                                </div>
                                <div class="block md:hidden">
                                    <PreviewDocumentModal
                                        :file="file"
                                        :isUrl="true"
                                        :visible="showPreview"
                                        :width="'90%'"
                                        @cancel="handlePreviewClose"
                                    />
                                </div>
                            </template>
                        </PreviewPane>
                    </div>
                </div>
            </template>
        </Card>
        <Card
            :pt="{
                root: 'max-w-full mx-auto p-0 md:p-0 shadow rounded-lg border border-gray-300 mx-8 lg:mx-20',
                header: 'flex items-center justify-between px-6 py-4 border-b border-gray-200',
                content: 'py-4 px-6',
            }"
        >
            <template #header>
                <h2 class="text-lg font-semibold text-gray-900">
                    Upload Extra documents
                    <sub class="ml-2 text-xs font-normal text-gray-500"
                        ><em>* Not Mandatory</em></sub
                    >
                </h2>
                <div class="flex items-center gap-2">
                    <p class="text-xxs">
                        Accepted files:
                        <span class="font-bold">PDF and DOCX</span>
                    </p>
                    <div class="h-2 w-px bg-gray-300"></div>
                    <p class="text-xxs">
                        Max Size:
                        <span class="font-bold">1 MB</span>
                    </p>
                </div>
            </template>
            <template #content>
                <div>
                    <HighlightBox :variant="'info'" :class="'mb-4'">
                        <template #icon>
                            <icon :name="'info-help'" />
                        </template>
                        <template #content>
                            <p class="text-primary-blue-600">
                                Please ensure the file name is clear and
                                descriptive of its content (e.g.,
                                "Contract_2025.pdf"), avoiding random names like
                                "xyz.pdf" or "file123.doc".
                            </p>
                        </template>
                    </HighlightBox>
                    <Field
                        :id="'other'"
                        :name="null"
                        :label="'Upload Photos'"
                        :hintMessage="'Hint: Select your additional photos'"
                        :component="'myTemplate'"
                        :validator="null"
                        :multiple="false"
                        :autoUpload="false"
                        @add="handleOnAdd($event, field)"
                        @remove="handleOnRemove($event, field)"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <FileUploader
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                                :restrictions="{
                                    allowedExtensions: ['.pdf', '.docx'],
                                    maxFileSize: 1000000,
                                }"
                                :buttonLabel="'Upload File'"
                                :isCustomUploader="false"
                                :isPadding="false"
                                :isAgencyUploadForm="false"
                                :iconName="'download_arrow_up'"
                                :variant="'dropzone'"
                                :class="'custom-dropzone-uploader'"
                                :linkedMsg="'Upload files'"
                                :noteMsg="'PDF, WORD, JPG, PNG up to 20MB'"
                                :hintMsg="'or drag and drop'"
                            />
                        </template>
                    </Field>
                </div>
            </template>
        </Card>
        <Card
            :pt="{
                root: 'max-w-full mx-auto p-0 md:p-0 shadow rounded-lg border border-gray-300 mx-8 lg:mx-20',
                header: 'flex items-center justify-between px-6 py-4 border-b border-gray-200',
                content: 'py-4 px-6',
            }"
        >
            <template #content>
                <Field
                    :id="'isAgreeTerms'"
                    :name="'isAgreeTerms'"
                    :label="'I agree to the Terms and conditions and Privacy policy'"
                    :component="'abnTemplate'"
                    :orientation="'horizontal'"
                    :validator="requiredtrue"
                    class="!flex items-center gap-2"
                    :placeholder="'Website'"
                    :isCheckFirst="true"
                    :pt="{
                        label: 'text-gray-900',
                    }"
                >
                    <template #abnTemplate="{ props }">
                        <FormCheckbox
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </Field>
            </template>
        </Card>
    </FormElement>
</template>
<script>
import FileUploader from "@spa/components/Uploader/FileUploader.vue";
import FormCheckbox from "@spa/components/KendoInputs/FormCheckbox.vue";
import { Field, FormElement } from "@progress/kendo-vue-form";
import Button from "@spa/components/Buttons/Button.vue";
import Card from "@spa/components/Card/Card.vue";
import apiClient from "@spa/services/api.client";
import FormInput from "@spa/components/KendoInputs/FormInput.vue";
import { Tooltip } from "@progress/kendo-vue-tooltip";
import PreviewPane from "@spa/components/KendoGrid/PreviewPane.vue";
import FormDatePicker from "@spa/components/KendoInputs/FormDatePicker.vue";
import HighlightBox from "@spa/components/HighlightBox/HighlightBox.vue";

export default {
    props: {
        nextStep: Function, // Accepts nextStep as a prop
        prevStep: Function, // Accepts prevStep as a prop
        skipNextStep: Function, // Accepts prevStep as a prop
        title: String,
        data: { type: Object, default: [] },
    },
    components: {
        FileUploader,
        FormCheckbox,
        Field,
        FormElement,
        Button,
        Card,
        FormInput,
        Tooltip,
        PreviewPane,
        FormDatePicker,
        HighlightBox,
    },
    inject: {
        kendoForm: { default: {} },
    },
    data() {
        return {
            formFields: [],
            actions: [
                {
                    text: "Preview Document",
                    value: "preview",
                    icon: "eye",
                },
                {
                    text: "Delete",
                    value: "delete",
                    icon: "trash",
                    isMore: true,
                },
            ],
            // replace with actual data
            gridData: [
                {
                    selected: true,
                    document_name: "FIle 1",
                    id: 1,
                },
                {
                    selected: false,
                    document_name: "FIle 2",
                    id: 2,
                },
            ],
            contractsColumn: [
                {
                    title: "Contract name",
                    width: 250,
                },
                {
                    title: "File name",
                },
                {
                    title: "Action",
                    width: 100,
                },
            ],
            showPreview: false,
            documentName: "",
            isUrl: true,
        };
    },
    mounted() {
        this.initializeFormValues();
    },
    computed: {
        getFieldClass() {
            return {
                wrap: "custom-date-picker",
            };
        },
    },
    methods: {
        initializeFormValues() {
            this.data.agentChecklistData.forEach((field) => {
                if (field.uploads && field.uploads.length > 0) {
                    this.kendoForm.onChange(field.id, { value: field.uploads });
                }
            });
        },
        checkValidation() {
            if (this.kendoForm.valid) {
                this.goNext();
            }
        },
        validateRequiredFile(value) {
            return value && value.length > 0 ? "" : "A file is required!";
        },
        handleOnAdd(event, field) {
            console.log("Added", field);
            const file = event.newState[0]?.getRawFile(); // Get raw file

            if (!file) {
                console.error("No file found.");
                return;
            }

            console.log(`File added for ${field.id}:`, file);
            // Assign file to the form field
            if (
                this.kendoForm &&
                typeof this.kendoForm.onChange === "function"
            ) {
                setTimeout(() => {
                    this.kendoForm.onChange(field.id, {
                        value: file.name,
                    });
                }, 2000);

                console.log("Updated form value:", this.kendoForm.values);
            } else {
                console.error("kendoForm or onChange function is missing.");
            }

            // Call function to upload the document
            this.handleUploadDocumentsForm(file, field);
        },
        handleOnRemove(event, field) {
            const file = event.affectedFiles[0];

            if (!file) {
                console.error("No file found.");
                return;
            }

            console.log("hewe", file);
            this.handleRemoveDocumentsForm(file, field);
            // Optionally, handle any additional logic for file removal
        },
        handleAction(index) {
            const preservedValues = { ...this.kendoForm.values };
            this.formFields.splice(index, 1);
            const newValues = {};
            this.formFields.forEach((field, i) => {
                const oldKey = Object.keys(field)[0]; // Get original key
                const newKey = `document_name_${i}`;

                newValues[newKey] = preservedValues[oldKey] ?? "";
            });

            this.kendoForm.values = newValues;
        },
        async handleUploadDocumentsForm(file, field) {
            const formData = new FormData();
            formData.append("file", file);
            formData.append("checklist_id", field.id);
            formData.append("document_name", field.document_name);
            formData.append("is_compulsory", field.is_compulsory);
            formData.append("agentId", this.data?.agentData?.id);

            try {
                const response = await apiClient.post(
                    "api/save-agent-application-documents",
                    formData,
                    {
                        headers: {
                            "Content-Type": "multipart/form-data",
                        },
                    },
                );

                console.log("Upload success:", response.data);
                // Refresh the state after successful upload
                // this.refreshState();
            } catch (error) {
                console.error("Upload error:", error);
            }
        },
        async handleRemoveDocumentsForm(file, field) {
            console.log(field);
            const formData = new FormData();
            formData.append("checklist_id", field.id);
            formData.append("agent_id", this.data?.agentData?.id);

            try {
                const response = await apiClient.post(
                    "api/remove-agent-application-documents",
                    formData,
                    {
                        headers: {
                            "Content-Type": "multipart/form-data",
                        },
                    },
                );

                console.log("Upload success:", response.data);
            } catch (error) {
                console.error("Upload error:", error);
            }
        },
        goNext() {
            this.nextStep();
        },
        skipNext() {
            this.nextStep();
        },
        goBack() {
            this.prevStep();
        },
        handleAddMore() {
            const nextIndex = this.formFields.length;
            const newContractNameField = { [`contract_name_${nextIndex}`]: "" };
            const newValidFromField = { [`valid_from_${nextIndex}`]: "" };
            const newValidToField = { [`valid_to_${nextIndex}`]: "" };
            const newFileNameField = { [`file_name_${nextIndex}`]: "" };

            this.formFields.push({
                [`contract_name_${nextIndex}`]: "",
                [`valid_from_${nextIndex}`]: "",
                [`valid_to_${nextIndex}`]: "",
                [`file_name_${nextIndex}`]: "",
                is_compulsory: false,
            });
            this.kendoForm.values = {
                ...this.kendoForm.values,
                ...newContractNameField,
                ...newValidFromField,
                ...newValidToField,
                ...newFileNameField,
            };
        },
        handleAddMoreOtherDoc() {
            const nextIndex = this.formFields.length;
            const newNameField = { [`document_name_${nextIndex}`]: "" };
            const newEmailField = { [`upload_${nextIndex}`]: "" };

            this.formFields.push({
                [`document_name_${nextIndex}`]: "",
                [`upload_${nextIndex}`]: "",
                is_compulsory: false,
            });
            this.kendoForm.values = {
                ...this.kendoForm.values,
                ...newNameField,
                ...newEmailField,
            };
        },
        getActions(dataItem) {
            let actionsArr = this.actions.map((action, index) => {
                // if (["rename", "move", "delete"].includes(action.value)) {
                return {
                    ...action,
                    show: !dataItem.is_compulsory, // Apply conditional logic
                };
                // }
                return { ...action, show: true };
            });

            return actionsArr;
        },
        getName(data) {
            return (this.documentName = data.document_name + "-" + data.id);
        },
        handleViewDetail(item) {
            this.getName(item);
            this.dataItem = item;
            this.showPreview = true;
            // const urlPreview = route("agent_preview_offer_letter_pdf_new", [
            //     (item.course_id = 1),
            //     (item.student_id = 2),
            //     (item.student_course_id = 3),
            // ]);
            // replace with actual url
            const urlPreview =
                "http://local.galaxy360.test/uploads/3/college_marterials/1740649998-8IG7UUdpxfrVBRmx-X6o9dUbO4Kex9isC.pdf";
            this.previewDocument(urlPreview);
        },
        handlePreviewClose() {
            this.showPreview = false;
        },
        previewDocument(item) {
            console.log("FileUrl", item);
            this.$nextTick(() => {
                const componentRef = this.$refs.previewPdf;

                if (!componentRef) {
                    console.error("studentDocument ref is undefined.");
                    return;
                }

                const fileUrl = this.isUrl
                    ? encodeURI(item)
                    : encodeURI(window[item]);

                this.file = fileUrl;
                console.log("FIle>>", this.file);

                const existingPdfViewer =
                    $(componentRef).data("kendoPDFViewer");
                console.log("Exi", existingPdfViewer);
                if (existingPdfViewer) {
                    existingPdfViewer.destroy();
                    $(componentRef).empty(); // Clear DOM to prevent duplicate instances
                }

                $.when(
                    $.getScript(
                        "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.js",
                    ),
                    $.getScript(
                        "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js",
                    ),
                )
                    .done(() => {
                        window.pdfjsLib.GlobalWorkerOptions.workerSrc =
                            "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js";
                    })
                    .then(() => {
                        $(componentRef)
                            .kendoPDFViewer({
                                pdfjsProcessing: {
                                    file: fileUrl,
                                },
                                width: "100%",
                                height: "100%",
                            })
                            .data("kendoPDFViewer");
                    });
            });
        },
        handlePreviewChange(item) {
            setTimeout(() => {
                const urlPreview =
                    "http://local.galaxy360.test/uploads/3/college_marterials/1740649998-8IG7UUdpxfrVBRmx-X6o9dUbO4Kex9isC.pdf";
                this.previewDocument(
                    // route("agent_preview_offer_letter_pdf_new", [
                    //     item.course_id,
                    //     item.student_id,
                    //     item.student_course_id,
                    // ]),
                    urlPreview,
                );
            });
        },
        handleNextClick(item) {
            this.handlePreviewChange(item);
        },
        handlePrevClick(item) {
            this.handlePreviewChange(item);
        },
    },
};
</script>
<style lang=""></style>
