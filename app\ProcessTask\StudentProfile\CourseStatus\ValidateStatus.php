<?php
namespace App\ProcessTask\StudentProfile\CourseStatus;

use App\Exceptions\ApplicationException;
use App\Model\v2\StudentCourses;
use Closure;

class ValidateStatus
{

    public function handle($data, Closure $next)
    {
        $newStatus = $data['newStatus'];
        $res = StudentCourses::find($data['studCourseId']);
        if (!$res) {
            throw new ApplicationException('Course not found.');
        }

        if($this->isStatusMarkedAsCancelled($res->status)){
            throw new ApplicationException('Course has already been cancelled. Status cannot be changed.');
        }

        if (empty($newStatus) || $res->status == $newStatus) {
            throw new ApplicationException('Please choose a different status to proceed.');
        }

        return $next($data);
    }

    private function isStatusMarkedAsCancelled($status)
    {
        $triggerStatuses = [
            StudentCourses::STATUS_CANCELLED,
            StudentCourses::STATUS_DEFERRED,
            StudentCourses::STATUS_WITHDRAWN,
            StudentCourses::STATUS_SUSPENDED
        ];

        return in_array($status, $triggerStatuses);
    }

}