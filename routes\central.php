<?php
use Illuminate\Support\Facades\Route;


// Route::get('/', function () {
//     return view('v2.default');
// });


/* Landing Site Routes */
Route::get('/', [\Domains\Customers\Onboarding\Controllers\LandingSiteController::class, 'home'])->name('central.home');
Route::get('/about', [\Domains\Customers\Onboarding\Controllers\LandingSiteController::class, 'about'])->name('central.about');
Route::get('/pricing', [\Domains\Customers\Onboarding\Controllers\LandingSiteController::class, 'pricing'])->name('central.pricing');
Route::get('/features', [\Domains\Customers\Onboarding\Controllers\LandingSiteController::class, 'features'])->name('central.features');
Route::get('/faq', [\Domains\Customers\Onboarding\Controllers\LandingSiteController::class, 'faq'])->name('central.faq');
Route::get('/contact', [\Domains\Customers\Onboarding\Controllers\LandingSiteController::class, 'contact'])->name('central.contact');
Route::post('/contact', [\Domains\Customers\Onboarding\Controllers\LandingSiteController::class, 'postContact'])->name('central.postcontact');



// Route::match(['get', 'post'],   'qazxswedc',                  ['as' => 'register',        'uses' => 'v2\sadmin\RegisterController@register']);

Route::match(['get', 'post'],   'verify-email/{param?}',      ['as' => 'verify-email',    'uses' => 'v2\sadmin\RegisterController@verifyemail']);


/* Onboarding Routes */
if (config('features.onboarding')) {
    Route::get('onboarding/{id}/subscription-completed', [\Domains\Customers\Onboarding\Controllers\VerifyController::class, 'onSubscriptionCompleted'])->name('onboarding.subscription-completed');
    Route::get('onboarding/{id}/subscription-cancelled', [\Domains\Customers\Onboarding\Controllers\VerifyController::class, 'onSubscriptionCancelled'])->name('onboarding.subscription-cancelled');
    Route::get('onboarding/verify/{id}', [\Domains\Customers\Onboarding\Controllers\VerifyController::class, 'verify'])->name('onboarding.verify');
    Route::get('onboarding/lobby/{id}', [\Domains\Customers\Onboarding\Controllers\RegisterController::class, 'lobby'])->name('onboarding.lobby');
    Route::get('onboarding/{id}/failed', [\Domains\Customers\Onboarding\Controllers\RegisterController::class, 'failed'])->name('onboarding.failed');
    Route::get('onboarding/password/{id}', [\Domains\Customers\Onboarding\Controllers\RegisterController::class, 'password'])->name('onboarding.password');
    Route::get('onboarding/thankyou/{id}', [\Domains\Customers\Onboarding\Controllers\RegisterController::class, 'thankyou'])->name('onboarding.thankyou');
    Route::get('onboarding/register', [\Domains\Customers\Onboarding\Controllers\RegisterController::class, 'register'])->name('onboarding.register');
    Route::get('onboarding/resendverification/{id}', [\Domains\Customers\Onboarding\Controllers\RegisterController::class, 'resendVerification'])->name('onboarding.resendverification');
    Route::post('onboarding/register', [\Domains\Customers\Onboarding\Controllers\RegisterController::class, 'postRegister'])->name('onboarding.postregister');
}
