@extends('landing.layout.base')

@section('styles')
    <script defer
            src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
@stop

@section('body')
    <!-- CONTACTS-
                               ============================================= -->
    <section id="contacts-1"
             class="pb-50 inner-page-hero contacts-section division">
        <div class="container">


            <!-- SECTION TITLE -->
            <div class="row justify-content-center">
                <div class="col-md-10 col-lg-9">
                    <div class="section-title mb-80 text-center">

                        <!-- Title -->
                        <h2 class="s-52 w-700">Questions? Let's Talk</h2>

                        <!-- Text -->
                        <p class="p-lg">Want to learn more about {{ config('app.name')}}, get a quote, or speak with an expert?
                            Let us know what you are looking for and we’ll get back to you right away
                        </p>

                    </div>
                </div>
            </div>


            <!-- CONTACT FORM -->
            <div class="row justify-content-center">
                <div class="col-md-11 col-lg-10 col-xl-8">
                    <div class="form-holder">
                        @if (session('success'))
                            <div class="border border-success border-radius p-3 text-center">
                                <h3 class="text-success">Thank you for your message.</h3>
                                <p>We will get back to you shortly.</p>
                            </div>
                        @else
                            <form name="contactform"
                                  method="post"
                                  action="{{ url()->current() }}"
                                  class="row contact-form"
                                  x-data="{ loading: false, subject: '{{ old('subject') }}' }"
                                  x-on:submit="loading = true;">



                                @csrf
                                <!-- Form Select -->
                                <div class="col-md-12 input-subject">
                                    <p class="p-lg">This question is about: </p>
                                    <span>Choose a topic, so we know who to send your request to: </span>
                                    @error('subject')
                                        <p class="text-danger">{{ $message }}</p>
                                    @enderror
                                    <select class="form-select subject"
                                            name="subject"
                                            x-model="subject"
                                            aria-label="Default select example">
                                        <option value=""
                                                selected>This question is about...</option>
                                        <option value="Registering/Authorising">Registering/Authorising</option>
                                        <option value="Using Application">Using Application</option>
                                        <option value="Troubleshooting">Troubleshooting</option>
                                        <option value="Backup/Restore">Backup/Restore</option>
                                        <option value="Other">Other</option>
                                    </select>

                                </div>

                                <!-- Contact Form Input -->
                                <div class="col-md-12">
                                    <p class="p-lg">Your Name: </p>
                                    <span>Please enter your real name: </span>
                                    @error('name')
                                        <p class="text-danger">{{ $message }}</p>
                                    @enderror
                                    <input type="text"
                                           name="name"
                                           class="form-control name"
                                           required
                                           placeholder="Your Name*"
                                           value="{{ old('name') }}">
                                </div>

                                <div class="col-md-12">
                                    <p class="p-lg">Your Email Address: </p>
                                    <span>Please carefully check your email address for accuracy</span>
                                    @error('email')
                                        <p class="text-danger">{{ $message }}</p>
                                    @enderror
                                    <input type="text"
                                           name="email"
                                           class="form-control email"
                                           required
                                           placeholder="Email Address*"
                                           value="{{ old('email') }}">
                                </div>

                                <div class="col-md-12">
                                    <p class="p-lg">Explain your question in details: </p>
                                    <span>Your OS version, Martex version & build, steps you did. Be VERY precise!</span>
                                    @error('message')
                                        <p class="text-danger">{{ $message }}</p>
                                    @enderror
                                    <textarea class="form-control message"
                                              name="message"
                                              rows="6"
                                              required
                                              placeholder="I have a problem with...">{{ old('message') }}</textarea>
                                </div>

                                <!-- Contact Form Button -->
                                <div class="col-md-12 mt-15 form-btn text-right">
                                    <button type="submit"
                                            :disabled="loading"
                                            :class="{ 'btn-secondary': loading }"
                                            class="btn btn--theme hover--theme submit"
                                            x-text="loading ? 'Submitting...' : 'Submit Request'">Submit Request</button>
                                </div>

                                <div class="contact-form-notice">
                                    <p class="p-sm">We are committed to your privacy. Martex uses the information you
                                        provide us to contact you about our relevant content, products, and services.
                                        You may unsubscribe from these communications at any time. For more information,
                                        check out our <a href="privacy.html">Privacy Policy</a>.
                                    </p>
                                </div>

                                <!-- Contact Form Message -->
                                <div class="col-lg-12 contact-form-msg">
                                    <span class="loading"></span>
                                </div>

                            </form>
                        @endif
                    </div>
                </div>
            </div> <!-- END CONTACT FORM -->


        </div> <!-- End container -->
    </section>

    <!-- END CONTACTS-2 -->


@stop
