<?php
namespace App\ProcessTask\StudentProfile\Scholarship;

use App\Exceptions\ApplicationException;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentScholarship;
use Closure;

class ValidateItem
{

    public function handle($data, Closure $next)
    {
        $studCourse = StudentCourses::find($data['new_student_course_id']);
        if (!$studCourse) {
            throw new ApplicationException('Course not found.');
        }

        if($this->isStatusMarkedAsCancelled($studCourse->status)){
            throw new ApplicationException('Course has already been cancelled. Scholarship cannot be transfer.');
        }

        $scholarship = StudentScholarship::find($data['scholarship_id']);
        if(!$scholarship){
            throw new ApplicationException('scholarship record not found.');
        }

        $isValidAmount = (($scholarship->scholarship_amount - $scholarship->used_scholarship_amount) > 0) ? true : false;
        if(!$isValidAmount){
            throw new ApplicationException('Already used scholarship, It cannot be transfer.');
        }

        return $next($data);
    }

    private function isStatusMarkedAsCancelled($status)
    {
        $triggerStatuses = [
            StudentCourses::STATUS_CANCELLED,
            StudentCourses::STATUS_DEFERRED,
            StudentCourses::STATUS_WITHDRAWN,
            StudentCourses::STATUS_SUSPENDED
        ];
        return in_array($status, $triggerStatuses);
    }

}