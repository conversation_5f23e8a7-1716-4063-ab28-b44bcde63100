<?php

namespace Tests;

use App\Model\v2\Tenant;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;

abstract class TenantTestCase extends BaseTestCase
{
    use CreatesApplication;


    protected function setUp(): void
    {
        parent::setUp();

        $this->initializeTenancy();


        auth()->loginUsingId(78);
    }

    protected function initializeTenancy()
    {
        $tenant = Tenant::find('iie');

        tenancy()->initialize($tenant);
    }
}
