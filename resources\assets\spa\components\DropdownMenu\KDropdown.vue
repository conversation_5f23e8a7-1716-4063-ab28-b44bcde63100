<template lang="">
    <div>
        <k-dropdown-list
            :style="{ width: '100%' }"
            :data-items="dataItems"
            :default-item="defaultItem"
            :text-field="textField"
            :data-item-key="dataItemKey"
            :value="modelValue"
            @input="$emit('update:modelValue', $event.target.value)"
            :filterable="true"
            :value-field="valueField"
            :value-primitive="valuePrimitive"
            @filterchange="$emit('filter', $event)"
            :loading="loading"
            @change="$emit('change', $event)"
            @close="$emit('close', $event)"
            @blur="$emit('blur', $event)"
            @focus="$emit('focus', $event)"
            :popup-settings="popupOptions"
            :class-name="computedSizeClass"
            :disabled="disabled"
        >
        </k-dropdown-list>
    </div>
</template>
<script>
import { defineComponent } from "vue";
import { DropDownList } from "@progress/kendo-vue-dropdowns";

export default defineComponent({
    components: {
        "k-dropdown-list": DropDownList,
    },
    props: {
        optional: Boolean,
        disabled: Boolean,
        label: String,
        name: String,
        modelValue: [String, Object, Number, Boolean],
        className: String,
        textField: String,
        valueField: {
            type: String,
            default: "id",
        },
        valuePrimitive: {
            type: Boolean,
            default: false,
        },
        dataItemKey: String,
        validationMessage: String,
        hint: String,
        id: String,
        valid: Boolean,
        dataItems: Array,
        defaultItem: Object,
        size: String,
    },
    data() {
        return {
            popupOptions: {
                popupClass: "tw-popup",
                animate: false,
            },
        };
    },
    emits: ["filter", "focus", "blur", "change", "close", "update:modelValue"],
    computed: {
        computedSizeClass() {
            switch (this.size) {
                case "xs":
                    return "h-7";
                case "sm":
                    return "h-9";
                case "base":
                    return "h-10";
                case "lg":
                    return "h-11";
                case "xl":
                    return "h-12";
                default:
                    return "h-10";
            }
        },
        value: {
            get() {
                return this.modelValue;
            },
            set(value) {
                this.$emit("update:modelValue", value);
            },
        },
    },
});
</script>
<style lang=""></style>
