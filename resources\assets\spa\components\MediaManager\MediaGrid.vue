<template lang="">
    <grid-wrapper
        :autoHide="false"
        class="tw-media-grid"
        :actionSticky="true"
        :borderlessHeader="true"
    >
        <k-grid
            ref="mediaManagerGrid"
            :columns="gridColumns"
            :data-items="gridData"
            :loading="isGridLoading"
            :selected-field="selectedField"
            :loader="'loaderTemplate'"
            :resizable="true"
            :sortable="{
                allowUnsort: false,
            }"
            :sort="sort"
            :pageable="false"
            :skip="getSkip"
            :take="getPerpageSize"
            :total="getTotalRecordsCount"
            :class="'h-[80vh]'"
            @sortchange="sortChangeHandler"
            @pagechange="pageChangeHandler"
            @selectionchange="onSelectionChange"
            @headerselectionchange="onHeaderSelectionChange"
            :row-render="rowRender"
        >
            <!-- Default Templates  -->
            <k-grid-no-records>
                <empty-state />
            </k-grid-no-records>
            <template #loaderTemplate>
                <table-loader v-if="isGridLoading" />
            </template>
            <template #sortingHeaderCell="{ props }">
                <header-cell v-bind:props="props" />
            </template>
            <!-- /Default Templates  -->
            <!-- Cell Templates  -->
            <template #defaultCell="{ props }">
                <default-cell v-bind:props="props" />
            </template>
            <template #badgeCell="{ props }">
                <badge-cell
                    v-bind:props="props"
                    :badge="{
                        variant: props.dataItem.is_autogenerated
                            ? 'purple'
                            : 'success',
                        class: 'rounded-full',
                    }"
                    :text="
                        props.dataItem.is_autogenerated
                            ? 'Auto Generated'
                            : 'Created'
                    "
                />
            </template>
            <template #dateCell="{ props }">
                <date-cell v-bind:props="props" :format="dateFormat" />
            </template>
            <template #documentCell="{ props }">
                <document-cell
                    v-bind:props="props"
                    @bookmarkClick="handleAction"
                    @openFolder="handleAction"
                />
            </template>
            <template #switchCell="{ props }">
                <switch-cell
                    v-bind:props="props"
                    :value="getCheckBoxValue(props)"
                    @changeSwitch="handleAction"
                />
            </template>
            <!-- /Cell Templates  -->
            <template #actionCell="{ props }">
                <td :class="props?.class || ''">
                    <div class="flex items-center gap-4">
                        <QuickActions
                            :loginType="loginType"
                            :currentTab="currentTab"
                            :actions="getActions(props.dataItem)"
                            :dataItem="props.dataItem"
                            @item-click="handleAction"
                        />
                    </div>
                </td>
            </template>
        </k-grid>
    </grid-wrapper>
    <DragClue
        :top="top"
        :left="left"
        :dataItem="dragActiveItem"
        :dropItem="dragDestinationItem"
        v-if="showDropOverlay"
    />
</template>
<script>
import { Grid, GridNoRecords } from "@progress/kendo-vue-grid";
import GridWrapper from "@spa/components/KendoGrid/GridWrapper.vue";
import EmptyState from "@spa/components/KendoGrid/EmptyState";
import TableLoader from "@spa/components/KendoGrid/TableLoader";
import DefaultCellTemplate from "@spa/components/KendoGrid/templates/DefaultCellTemplate.vue";
import BadgeCellTemplate from "@spa/components/KendoGrid/templates/BadgeCellTemplate.vue";
import DateCellTemplate from "@spa/components/KendoGrid/templates/DateCellTemplate.vue";
import HeaderTemplate from "@spa/components/KendoGrid/templates/HeaderTemplate.vue";
import DocumentCellTemplate from "@spa/components/KendoGrid/templates/DocumentCellTemplate.vue";
import SwitchCellTemplate from "@spa/components/KendoGrid/templates/SwitchCellTemplate.vue";
import Button from "@spa/components/Buttons/Button.vue";
import QuickActions from "@spa/components/KendoGrid/QuickActions.vue";
import DragClue from "@spa/components/MediaManager/partials/DragClue.vue";
import { DEFAULT_DATE_FORMAT } from "@spa/helpers/constants.js";
import { useLoaderStore } from "@spa/stores/modules/global-loader";

export default {
    setup(props) {
        const loaderStore = useLoaderStore();
        return {
            loaderStore,
        };
    },
    props: {
        data: { type: Object, default: {} },
        currentTab: { type: String, default: "" },
        loginType: { type: String, default: "" },
        columns: { type: Object, default: {} },
        pagination: { type: [Array, Object], default: {} },
        selectedRowIndex: Number,
    },
    components: {
        "grid-wrapper": GridWrapper,
        "k-grid": Grid,
        "k-grid-no-records": GridNoRecords,
        "empty-state": EmptyState,
        "table-loader": TableLoader,
        "default-cell": DefaultCellTemplate,
        "badge-cell": BadgeCellTemplate,
        "date-cell": DateCellTemplate,
        "header-cell": HeaderTemplate,
        "document-cell": DocumentCellTemplate,
        "switch-cell": SwitchCellTemplate,
        Button,
        QuickActions,
        DragClue,
    },
    data() {
        return {
            // Drag
            draggable: {
                sourceId: null,
                destinationId: null,
            },
            top: 0,
            left: 0,
            showDropOverlay: false,
            showDragClueContent: null,
            dropPosition: "",
            dragActiveItem: null,
            dragDestinationItem: null,
            selectedField: "selected",
            filter: null,
            searchKey: null,
            sort: [
                { field: "name", dir: "asc" },
                { field: "modified", dir: "desc" },
            ],
            actions: [
                {
                    text: "Preview Document",
                    value: "preview",
                    icon: "eye",
                },
                {
                    text: "Download Document",
                    value: "download",
                    icon: "download",
                },
                {
                    text: "Open Folder",
                    value: "openFolder",
                    icon: "arrow-right",
                },
                {
                    text: "More Options",
                    icon: "more-horizontal",
                },
                {
                    text: "Rename",
                    value: "rename",
                    icon: "pen",
                    isMore: true,
                },
                {
                    text: "Move File",
                    value: "move",
                    icon: "move",
                    isMore: true,
                },
                {
                    text: "File details",
                    value: "viewDetail",
                    icon: "document",
                    isMore: true,
                },
                {
                    text: "Delete",
                    value: "delete",
                    icon: "trash",
                    isMore: true,
                },
            ],
            dateFormat: DEFAULT_DATE_FORMAT,
            isAtScrollEnd: false,
            isLoading: false,
            showLoader: false,
            clearLoaderTimeout: null,
        };
    },
    mounted() {
        this.isLoading = true;
        setTimeout(() => {
            this.isLoading = false;
        }, 500);
    },
    emits: {
        dataItemAction: null,
        sort: null,
        pagechange: null,
        selectionChange: null,
    },
    computed: {
        // isDragging() {
        //     return {
        //         dragging: this.activeItem.id,
        //     };
        // },
        isGridLoading() {
            return this.showLoader || this.isLoading;
        },
        isSwitchCellPresent() {
            return this.gridColumns.some((col) => col.cell === "switchCell");
        },
        gridData() {
            // const updatedData = [...this.data];
            // if (updatedData.length) {
            //     updatedData.sort((a, b) => {
            //         if (a.isDirectory === b.isDirectory) {
            //             return 0;
            //         }
            //         return a.isDirectory ? -1 : 1;
            //     });

            //     return updatedData.map((item) => ({
            //         ...item,
            //     }));
            // }
            return this.data;
        },
        gridColumns() {
            const transformedArray = this.columns.map(
                ({
                    field,
                    title,
                    cell,
                    minResizableWidth,
                    width,
                    sortable,
                    locked,
                }) => ({
                    field,
                    title,
                    cell: cell || "defaultCell",
                    ...(minResizableWidth && { minResizableWidth }),
                    ...(width && { width }),
                    headerCell: sortable ? "sortingHeaderCell" : undefined, // Include headerCell conditionally
                    sortable, // Always include sortable
                    ...(locked && { locked }),
                }),
            );
            const selectionColumn = {
                field: this.selectedField,
                headerSelectionValue: this.areAllSelected,
                width: 48,
                minResizableWidth: 48,
            };
            if (
                this.loginType == "students" &&
                (this.currentTab == "course" || this.currentTab == "subject")
            ) {
                return [...transformedArray, {}];
            } else {
                return [selectionColumn, ...transformedArray, {}];
            }
        },
        areAllSelected() {
            return (
                this.gridData.length > 0 &&
                this.gridData?.findIndex((item) => item.selected === false) ===
                    -1
            );
        },
        getCurrentPage() {
            return this.pagination.currentPage || 1;
        },
        getSkip() {
            return this.pagination.skip || 0;
        },
        getPerpageSize() {
            return this.pagination.pageSizeValue || 10;
        },
        getTotalRecordsCount() {
            return this.pagination.totalItems || 0;
        },
    },
    methods: {
        getCheckBoxValue(propsData) {
            if (propsData.field == "students") {
                return propsData.dataItem.is_student == "1" ? true : false;
            } else {
                return propsData.dataItem.is_teacher == "1" ? true : false;
            }
        },
        rowRender(h, defaultRendering, defaultSlots, props, listeners) {
            const { dataItem } = props;
            return h(
                "tr",
                {
                    ...defaultRendering.props,
                    class: `${defaultRendering.props.class} focus:cursor ${defaultRendering.props["data-grid-row-index"] === this.selectedRowIndex ? "k-selected" : ""}`,
                    draggable: "true",
                    onDragstart: (e) => this.handleDragStart(e, dataItem),
                    onDragover: (e) => this.handleDragOver(e, dataItem),
                    onDragend: (e) => this.handleDragEnd(e, dataItem),
                    onDrop: (e) => this.handleDrop(e, dataItem),
                },
                defaultRendering.children, // Default row children
            );
        },
        handleDragStart(event, dataItem) {
            var dragIcon = document.createElement("img");
            dragIcon.src = "";
            dragIcon.width = 100;
            dragIcon.style.display = "none";
            event.dataTransfer.setDragImage(dragIcon, -100, -100);
            this.dragActiveItem = dataItem;
            this.draggable.sourceId = dataItem.folder_id;
            return false;
        },
        handleDragOver(event, dataItem) {
            event.preventDefault();
            this.top = event.pageY + 10;
            this.left = event.pageX;
            this.dragDestinationItem = dataItem;
            this.showDropOverlay = true;
        },
        handleDrop(event, dataItem) {
            this.draggable.destinationId = dataItem.id;
            if (
                this.dragDestinationItem &&
                this.dragDestinationItem?.isDirectory
            ) {
                let data = {
                    document_id: this.draggable.sourceId,
                    folder_id: this.draggable.destinationId,
                };
                this.$emit("dataItemAction", "dragMove", data);
            }
            this.showDropOverlay = false;
        },
        handleDragEnd(event, dataItem) {
            this.showDropOverlay = false;
        },
        onHeaderSelectionChange(event) {
            let checked = event.event.target.checked;

            // Update the selected field for all items
            this.data.forEach((item) => {
                item.selected = checked;
            });

            // let selected = this.data
            //     .filter((item) => item.selected)
            //     .map((item) => item.folder_id);

            // this.$emit("selectionChange", selected);
            this.emitSelectedItems(this.data);
        },
        onSelectionChange(event) {
            event.dataItem[this.selectedField] =
                !event.dataItem[this.selectedField];
            // let selected = event.dataItems
            //     .filter((item) => item.selected)
            //     .map((item) => item.folder_id);
            // this.$emit("selectionChange", selected);
            this.emitSelectedItems(event.dataItems);
        },
        handleAction(action, dataItem, actionType = "") {
            this.$emit("dataItemAction", action, dataItem, actionType);
        },
        sortChangeHandler: function (e) {
            this.sort = e.sort;
            this.$emit("sortChange", this.sort);
            this.delayForSwitch();
        },
        pageChangeHandler(e) {
            const skip = e.page.skip || 0;
            const take = parseInt(e.page.take || 0);
            let pageNumber = 1;
            if (!isNaN(take) && take > 0) {
                pageNumber = Math.floor(skip / take) + 1;
            }
            if (
                pageNumber != this.getCurrentPage ||
                take != this.getPerpageSize
            ) {
                this.$emit("changepage", pageNumber, take);
            }
            return;
        },
        getActions(dataItem) {
            let actionsArr = this.actions.map((action, index) => {
                if (["rename", "move", "delete"].includes(action.value)) {
                    return {
                        ...action,
                        show: !dataItem.is_autogenerated, // Apply conditional logic
                    };
                }
                return { ...action, show: true };
            });

            return actionsArr;
        },
        emitSelectedItems(data) {
            let selected = data
                .filter((item) => item.selected)
                .map((item, index) => {
                    return {
                        id: item.id,
                        name: item.name,
                        is_autogenerated: item.is_autogenerated,
                        folder_id: item.folder_id,
                    };
                });
            this.$emit("selectionChange", selected);
        },
    },
    watch: {
        "loaderStore.contextLoaders.documents": {
            handler(newValue) {
                if (newValue) {
                    this.isLoading = true;
                    this.showLoader = true;
                    if (this.clearLoaderTimeout) {
                        clearTimeout(this.clearLoaderTimeout);
                    }
                } else {
                    this.isLoading = false;
                    if (this.isSwitchCellPresent) {
                        this.clearLoaderTimeout = setTimeout(() => {
                            this.showLoader = false;
                        }, 200);
                    } else {
                        this.showLoader = false;
                    }
                }
            },
        },
    },
};
</script>
<style lang=""></style>
