<?php

namespace App\Traits;

use App\Classes\SiteConstants;
use Notifications\Contracts\NotificationServiceInterface;
use Notifications\Types\NotificationType;

trait SendNotificationTrait
{
    public function __construct(
        protected NotificationServiceInterface $notificationService
    ) {}

    public function sendNotification($user, NotificationType $notificationType, mixed $notificationData = null, bool $isMail = false): void
    {
        $this->updateUserNotificationPreference($user, $isMail);

        $this->notificationService->send($user, $notificationType, $notificationData);
    }

    protected function updateUserNotificationPreference($user, bool $isMail): void
    {
        $user->setMeta(SiteConstants::NOTIFICATION_PREFERENCE_META_KEY, [
            SiteConstants::NOTIFICATION_CHANNEL_EMAIL => $isMail,
        ]);
    }
}
