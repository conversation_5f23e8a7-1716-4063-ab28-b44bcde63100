import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';
// import { fileURLToPath, URL } from "node:url";
import path from 'node:path';
import fs from 'fs';
// import * as glob from "glob";
// import vitePluginRequire from "vite-plugin-require";
// import galaxyConcat from "./vite-plugin-concat";
import commonjs from 'vite-plugin-commonjs';
import galaxyconcat from './galaxyconcat';
import rendertemplates from './rendertemplates';
// Function to recursively get all JS files in a directory
function getAllFiles(dirPath, arrayOfFiles) {
    const files = fs.readdirSync(dirPath);

    arrayOfFiles = arrayOfFiles || [];

    files.forEach((file) => {
        if (fs.statSync(path.join(dirPath, file)).isDirectory()) {
            arrayOfFiles = getAllFiles(path.join(dirPath, file), arrayOfFiles);
        } else if (file.endsWith('.js')) {
            arrayOfFiles.push(path.join(dirPath, file));
        }
    });

    return arrayOfFiles;
}

const studentProfileFiles = getAllFiles('resources/assets/scripts/student-profile');
// console.log()

export default defineConfig(({ mode }) => {
    // console.log(mode);
    return {
        plugins: [
            commonjs(),
            galaxyconcat({
                groupedFiles: [
                    {
                        files: studentProfileFiles,
                        outputFile: './public/build/student-profile-build.js',
                    },
                ],
                compress: true,
            }),
            laravel({
                buildDirectory: 'build/dist',
                input: [
                    'resources/assets/js/taskapp.js',
                    'resources/assets/spa/app.js',
                    'resources/assets/sass/tailwind.css',
                    'resources/assets/scripts/index.js',
                    // "resources/assets/scripts/components.js",
                    'resources/assets/sass/main.scss',
                    'resources/assets/sass/kendo-theme.scss',
                    // ...studentProfileFiles,

                    // ...glob.sync("resources/assets/scripts/student-profile/*.js")
                    // "resources/assets/scripts/student-profile/index.js",
                ],
                refresh: true,
            }),
            vue({
                template: {
                    transformAssetUrls: {
                        base: null,
                        includeAbsolute: false,
                    },
                    compilerOptions: {
                        compatConfig: {
                            MODE: 3,
                        },
                    },
                },
            }),
            // rendertemplates(),
        ],
        build: {
            rollupOptions: {
                maxParallelFileOps: 2,
                output: {
                    manualChunks(id) {
                        // console.log('FILE', id);
                        //   if (id.includes('student-profile')) { // Package the files in the components file into components.js
                        //       return 'student-profile'
                        //   }
                        if (id.includes('@progress/kendo-vue-scheduler')) {
                            return 'kendo-vue-scheduler';
                        }
                        // if (id.includes('node_modules')) {
                        //     // Split each node_modules dependency into its own chunk
                        //     return `vendor_${id.toString().split('node_modules/')[1].split('/')[0]}`;
                        //   }
                    },
                },
                cache: false,
            },
            sourcemapIgnoreList: (relativeSourcePath) => {
                const normalizedPath = path.normalize(relativeSourcePath);
                return normalizedPath.includes('node_modules');
            },
            sourcemap: false,
            outDir: './public/build/dist',
            // manifest: "public/build/dist/manifest.json",
            commonjsOptions: {
                include: [/node_modules/],
                extensions: ['.js', '.cjs'],
                // strictRequires: true,
                // https://stackoverflow.com/questions/62770883/how-to-include-both-import-and-require-statements-in-the-bundle-using-rollup
                transformMixedEsModules: true,
            },
        },
        resolve: {
            alias: {
                '@spa': __dirname + '/resources/assets/spa',
                '@studentportal': __dirname + '/resources/assets/spa/pages/studentportal',
                '@agentportal': __dirname + '/resources/assets/spa/pages/agentportal',
                '@agentstaffportal': __dirname + '/resources/assets/spa/pages/agentstaffportal',
                konva: path.resolve(__dirname, 'resources/assets/spa/shims/konva-global.js'),
                vue:
                    mode === 'development'
                        ? 'vue/dist/vue.esm-browser.js'
                        : 'vue/dist/vue.esm-browser.prod.js',
            },
            extensions: ['.*', '.wasm', '.mjs', '.js', '.jsx', '.json', '.vue'],
        },
        define: {
            // By default, Vite doesn't include shims for NodeJS/
            // necessary for segment analytics lib to work
            global: {},
        },
        css: {
            preprocessorOptions: {
                scss: {
                    quietDeps: true,
                },
            },
        },
        optimizeDeps: {
            esbuildOptions: {
                // Node.js global to browser globalThis
                define: {
                    // global: {},
                },
                // Enable esbuild polyfill plugins
                //   plugins: [
                //     NodeGlobalsPolyfillPlugin({
                //       buffer: true,
                //     }),
                //   ],
            },
        },
    };
});
