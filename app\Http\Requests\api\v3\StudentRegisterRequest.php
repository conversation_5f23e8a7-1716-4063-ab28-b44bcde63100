<?php

namespace App\Http\Requests\api\v3;

use App\Model\v2\Colleges;
use App\Model\v2\Student;
use App\Model\v2\Tenant;
use App\Model\v2\Users;
use App\Rules\HcaptchaValidator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\ValidationException;

class StudentRegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation()
    {
        $this->full_name = $this->full_name ?? null;
        $first_name = $this->first_name ?? null;
        $family_name = $this->family_name ?? null;
        $middel_name = $this->middel_name ?? null;

        if($this->full_name){
            $nameParts = explode(" ", $this->full_name);
            $first_name = array_shift($nameParts);
            $family_name = array_pop($nameParts) ?? $family_name;
            $middel_name = !empty($nameParts) ? implode(" ", $nameParts) : $middel_name;
        }

        try{

            $hostUrl = $this->redirect_uri ?? "";
            $redirectUri = null;
            if(!empty($hostUrl)){
                $redirectUri = validateRedirectUri($hostUrl, false);
                if(!$hostUrl){
                    throw ValidationException::withMessages([
                        'redirect_uri' => 'This Domain is not a registered as allowed domains.',
                    ]);
                }
            }else{
                $redirectUri = validateRedirectUri();
            }
            
        } catch(\Exception $e){
            throw ValidationException::withMessages([
                'redirect_uri' => $e->getMessage(),
            ]);
        }
        
        $this->merge([
            'first_name' => $first_name,
            'family_name' => $family_name,
            'middel_name' => $middel_name,
            'redirect_uri' => $redirectUri,
        ]);
        
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        if(config('constants.app_environment') == 'production'){
            $captchaFld = ['required'/* , new HcaptchaValidator() */];
        }else{
            $captchaFld = ['required'];
        }
        return [
            'name_title' => ['nullable', 'string', 'in:Mr.,Mrs.,Ms.,Other'],
            /* user name can be posted with out splitting as full name */
            'full_name' => ['nullable', 'required_without_all:first_name,family_name', 'string', 'max:450'],
            /* if full_name not provided then first_name is required */
            'first_name' => ['nullable', 'required_without:full_name', 'string', 'max:150'],
            'middel_name' => ['nullable', 'string', 'max:150'],
            /* if full_name not provided then family_name is required */
            'family_name' => ['nullable', 'required_without:full_name', 'string', 'max:150'],
            'email' => [
                            'required', 
                            'email', 
                            function ($attribute, $value, $fail) {
                                $existsInUsers = Users::where('email', $value)->exists();
                                $existsInStudents = Student::where('email', $value)->exists();
                                /* if the email is registered as users but the email is not in students, then the email is of admin or teacher or agent so we need to return error */
                                if (!$existsInStudents && $existsInUsers) {
                                    $fail('The email address is already in use.');
                                }
                            },
                        ],
            'gender' => ['nullable', 'string', 'in:Male,Female,Other'],
            'phone' => ['nullable', 'regex:/^(\+?\d{1,3})?[-.\s]?\(?\d{1,4}\)?[-.\s]?\d{1,4}[-.\s]?\d{1,9}$/'],
            /* Password must be at least 8 Characters long, with at least 1 uppercase and lowercase letter, at least one number and at least one symbol */
            'password' => ['required', 'confirmed', Password::min(8)->mixedCase()->numbers()->symbols()],
            'read_and_agree' => ['required', 'accepted'],
            /* Captcha token */
            'captcha' => $captchaFld,
            'redirect_uri' => ['required', 'string'],
        ];
    }
    public function messages(){
        return [
            'phone.regex' => 'Please enter a valid 10-digit phone number.',
            'read_and_agree' => 'Please read and agree the terms and conditions.',
        ];
    }
    protected function passedValidation()
    {
        $collegeId = auth()->user()->college_id ?? null;
        //if there is no college id get one
        if(!$collegeId) $collegeId = Colleges::orderByDesc("status")->orderByDesc("id")->value("id");
        
        $phone = $this->phone ?? null;

        $this->merge([
            'college_id' => $collegeId,
            'current_mobile_phone' => $phone,
        ]);
        
    }
}
