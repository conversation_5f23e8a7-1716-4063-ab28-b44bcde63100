<?php

namespace App\Model\v2;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class CourseType extends Model {

    protected $table = 'rto_college_course_type';

    protected $fillable = ['title', 'description', 'status'];

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('order', function (Builder $builder) {
            $builder->orderBy('display_order')
                    ->orderBy('title');
        });
    }

    public function getCourseType($collegeId){
        return CourseType::where('status', 1)->whereIn('college_id', [0 ,$collegeId])->pluck('title','id');
    }
    
    public function getTypeName($id, $collegeId){
        return CourseType::where(['id' => $id, 'college_id' => $collegeId])->get(['title'])->first();
    }

    public function checkHigherEdGradingType($id,$collegeId = '') {
        if(empty($collegeId)){
            $collegeId = Auth::user()->college_id;
        }
        $courseTypeName = $this->getTypeName($id, $collegeId);
        if(isset($courseTypeName) && !empty($courseTypeName)){
            $arrSpecialGradeTypeList = Config::get('constants.arrSpecialCourseTypeName');
            $isHigherGradeType = ($courseTypeName != str_ireplace($arrSpecialGradeTypeList,"XX",$courseTypeName))? true: false;
            return $isHigherGradeType;
        }
        return false;
    }
    public static function getDropdownList(){
        $collegeIds = array(0, auth()->user()->college_id);
        $types = CourseType::Where('status', 1)
                    ->whereIn('college_id', $collegeIds)
                    ->select('id', 'title')
                    ->orderBy('display_order')
                    ->get()->map(function ($item) {
                        return [
                            'value' => (int) $item->id,
                            'label' => $item->title,
                        ];
                    });
        return $types;
    }

    public function checkHigherEdGradInCourseType() {
        $collegeId = Auth::user()->college_id;
        $CourseTypeList = CourseType::where(['college_id' => $collegeId,'status'=>1])->select('title')->get()->toArray();

        $arrSpecialGradeTypeList = Config::get('constants.arrSpecialCourseTypeName');
        $titles = array_column($CourseTypeList, 'title');
        $intersect = array_intersect(array_map('strtolower', $titles), array_map('strtolower', $arrSpecialGradeTypeList));
        $isHigherGradeType = !empty($intersect);

        return $isHigherGradeType;
    }
}
