@props([
'show' => true,
'showText' => true,
'loadingText' => 'Please Wait...',
'pt' => [],
'size' => 48
])

@if ($show)
<div {{$attributes->twMerge('flex h-full w-full items-center justify-center gap-4 p-4')}}>
    {{-- <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 50 50" fill="none"
        class="animate-spin">
        <circle cx="25" cy="25" r="22.5" stroke="#E2E8F0" stroke-width="5" />
        <mask id="path-2-inside-1_5811_136532" fill="white">
            <path
                d="M41.5841 40.2108C42.6002 41.1429 44.191 41.0813 45.0171 39.9773C48.1972 35.7273 49.9556 30.5547 49.9992 25.2037C50.0428 19.8528 48.3689 14.6522 45.2586 10.351C44.4506 9.23366 42.861 9.14615 41.8298 10.0615C40.7986 10.9768 40.7195 12.5474 41.4994 13.6846C43.8042 17.0452 45.0396 21.0485 45.006 25.163C44.9725 29.2776 43.6721 33.2603 41.3128 36.5829C40.5145 37.7071 40.5679 39.2788 41.5841 40.2108Z" />
        </mask>
        <path
            d="M41.5841 40.2108C42.6002 41.1429 44.191 41.0813 45.0171 39.9773C48.1972 35.7273 49.9556 30.5547 49.9992 25.2037C50.0428 19.8528 48.3689 14.6522 45.2586 10.351C44.4506 9.23366 42.861 9.14615 41.8298 10.0615C40.7986 10.9768 40.7195 12.5474 41.4994 13.6846C43.8042 17.0452 45.0396 21.0485 45.006 25.163C44.9725 29.2776 43.6721 33.2603 41.3128 36.5829C40.5145 37.7071 40.5679 39.2788 41.5841 40.2108Z"
            stroke="#1890FF" stroke-width="10" stroke-linejoin="round" mask="url(#path-2-inside-1_5811_136532)" />
    </svg> --}}
    <svg xmlns="http://www.w3.org/2000/svg" width="{{ $size }}" height="{{ $size }}" viewBox="0 0 24 24" fill="none"
        stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
        class="lucide lucide-loader-circle-icon lucide-loader-circle animate-spin-fast text-primary-blue-500">
        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
    </svg>
    @if ($showText)
    <span {{$attributes->twMergeFor('text', 'text-base font-medium')}}>{{ $loadingText }}</span>
    @endif
</div>
@endif