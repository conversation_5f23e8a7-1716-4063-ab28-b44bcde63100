<?php

namespace App\Model;

use App\Model\v2\SubjectMaterial;
use App\Services\SyncUnitsSetupService;
use Auth;
use DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class Subject extends Model
{
    use SoftDeletes;

    protected $table = 'rto_subject';

    protected static function booted()
    {
        static::created(function ($subject) {
            SubjectMaterial::CheckAndCreateSubjectFolder($subject);
        });
        static::updated(function ($subject) {
            SubjectMaterial::CheckAndCreateSubjectFolder($subject);
            SyncUnitsSetupService::updateSubjectDataFromOld($subject);
        });
    }

    public function getSubjectList($college_id, $perPage)
    {

        $arrSubjectLists = Subject::leftjoin('rto_college_course_type', 'rto_college_course_type.id', '=', 'rto_subject.course_type')
            ->leftjoin(DB::raw('(SELECT subject_id, SUM(rto_subject_unit.nominal_hours) as total_houres FROM rto_subject_unit GROUP BY subject_id) as rsu'), 'rto_subject.id', '=', 'rsu.subject_id')
            ->leftjoin('rto_grading_type', 'rto_grading_type.id', '=', 'rto_subject.grading_type')
            ->select('rto_subject.id', 'rto_college_course_type.title as courseTypeTitle', 'total_houres', 'rto_grading_type.grading_name', 'rto_subject.subject_code', 'rto_subject.subject_name', 'rto_subject.created_at')
            ->where('rto_subject.college_id', $college_id)
            ->orderBy('rto_subject.id', 'desc')
                //   ->groupBy('rto_subject.id')
            ->paginate($perPage);

        return $arrSubjectLists;
    }

    public function SubjectAdd($request)
    {

        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;

        $objSubject = new Subject;

        $is_higher_edu = ($request->input('is_higher_education') == '1') ? true : false;
        $course_type = ($request->input('course_type') != '') ? $request->input('course_type') : null;
        $subject_code = ($request->input('subject_code') != '') ? $request->input('subject_code') : null;
        $subject_name = ($request->input('subject_name') != '') ? $request->input('subject_name') : null;
        $grading_type = ($request->input('grading_type') != '') ? $request->input('grading_type') : null;

        $objSubject->course_type = $course_type;
        $objSubject->subject_code = $subject_code;
        $objSubject->subject_name = $subject_name;
        $objSubject->grading_type = $grading_type;

        if ($is_higher_edu) {
            $objSubject->max_marks_allow = $request->input('max_marks_allow');
            $objSubject->contact_hours = $request->input('contact_hours');
            $objSubject->level = $request->input('level');
            $objSubject->credit_point = $request->input('credit_point');
            $objSubject->is_long_indicator = $request->input('is_long_indicator');
            $objSubject->is_assessment = $request->input('is_assessment');
            $objSubject->discipline_broad_type = $request->input('discipline_broad_type');
            $objSubject->discipline_narrow_type = $request->input('discipline_narrow_type');
            $objSubject->discipline_narrow_sub_type = $request->input('discipline_narrow_sub_type');
        }

        $objSubject->college_id = $collegeId;
        $objSubject->created_by = $userId;
        $objSubject->updated_by = $userId;

        if ($objSubject->save()) {
            $objSubjectMaterial = new SubjectMaterial;
            $objSubjectMaterial->createFolderSubjectName($collegeId, $userId, $subject_code, $subject_name);
        }

        return $objSubject->id;
    }

    public function UnitBySubjectAdd($request)
    {

        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;

        $objSubject = new Subject;

        $course_type = ($request->input('course_type') != '') ? $request->input('course_type') : null;
        $subject_code = ($request->input('unit_code') != '') ? $request->input('unit_code') : null;
        $subject_name = ($request->input('unit_name') != '') ? $request->input('unit_name') : null;
        $grading_type = ($request->input('grading_type') != '') ? $request->input('grading_type') : null;

        $objSubject->course_type = $course_type;
        $objSubject->subject_code = $subject_code;
        $objSubject->subject_name = $subject_name;
        $objSubject->grading_type = $grading_type;

        $objSubject->college_id = $collegeId;
        $objSubject->created_by = $userId;
        $objSubject->updated_by = $userId;

        if ($objSubject->save()) {
            $objSubjectMaterial = new SubjectMaterial;
            $objSubjectMaterial->createFolderSubjectName($collegeId, $userId, $subject_code, $subject_name);
        }

        return $objSubject->id;
    }

    // insert time check validation on subject code
    public function checkSubjectUnitCode($collegeId, $request)
    {

        $subjectCode = $request->input('subject_code');
        $unitCode = $request->input('unit_code');
        $unitDisplayCode = $request->input('unit_display_code');
        $validations = [];

        $subjectInfo = new Subject;
        $subjectData = Subject::where('subject_code', '=', $subjectCode)->where('college_id', '=', $collegeId)->get();

        // $unitModuleInfo = new UnitModule();
        $unitCodeData = UnitModule::where('unit_code', '=', $unitCode)->where('college_id', '=', $collegeId)->get();
        $vetUnitCodeData = UnitModule::where('vet_unit_code', '=', $unitDisplayCode)->where('college_id', '=', $collegeId)->get();

        if ($subjectData->count() > 0) {
            $validations['subject_code'] = 'unique:rto_subject,subject_code';
            $failedValidations = [
                'subject_code.unique' => 'Subject code already exist.',
            ];
            $validator12 = Validator::make($request->all(), $validations, $failedValidations);

            return $validator12;
        } elseif ($unitCodeData->count() > 0) {
            $validations['unit_code'] = 'unique:rto_subject_unit,unit_code';
            $failedValidations = [
                'unit_code.unique' => 'Unit code already exist.',
            ];
            $validator12 = Validator::make($request->all(), $validations, $failedValidations);

            return $validator12;
        } elseif ($vetUnitCodeData->count() > 0) {
            $validations['unit_display_code'] = 'unique:rto_subject_unit,vet_unit_code';
            $failedValidations = [
                'unit_display_code.unique' => 'Vet unit code already exist.',
            ];
            $validator12 = Validator::make($request->all(), $validations, $failedValidations);

            return $validator12;
        } else {
            $validations['subject_code'] = 'min:1';
            if ($request->input('is_higher_education') != 1) {
                $validations['vet_unit_code'] = 'min:1';
                $validations['unit_code'] = 'min:1';
            }

            $validator12 = Validator::make($request->all(), $validations);

            return $validator12;
        }
    }

    public function SubjectEdit($request, $subjectId)
    {

        $objSubject = Subject::find($subjectId);

        $college_id = Auth::user()->college_id;
        $loginUserId = Auth::user()->id;

        $is_higher_edu = ($request->input('is_higher_education') == '1') ? true : false;
        $course_type = ($request->input('course_type') != '') ? $request->input('course_type') : null;
        $subject_name = ($request->input('subject_name') != '') ? $request->input('subject_name') : null;
        $grading_type = ($request->input('grading_type') != '') ? $request->input('grading_type') : null;

        $objSubject->course_type = $course_type;
        $objSubject->subject_name = $subject_name;
        $objSubject->grading_type = $grading_type;

        $objSubject->max_marks_allow = ($is_higher_edu) ? $request->input('max_marks_allow') : null;
        $objSubject->contact_hours = ($is_higher_edu) ? $request->input('contact_hours') : null;
        $objSubject->level = ($is_higher_edu) ? $request->input('level') : null;
        $objSubject->credit_point = ($is_higher_edu) ? $request->input('credit_point') : null;
        $objSubject->is_long_indicator = ($is_higher_edu) ? $request->input('is_long_indicator') : 0;
        $objSubject->is_assessment = ($is_higher_edu) ? $request->input('is_assessment') : 0;
        $objSubject->discipline_broad_type = ($is_higher_edu) ? $request->input('discipline_broad_type') : null;
        $objSubject->discipline_narrow_type = ($is_higher_edu) ? $request->input('discipline_narrow_type') : null;
        $objSubject->discipline_narrow_sub_type = ($is_higher_edu) ? $request->input('discipline_narrow_sub_type') : null;

        $objSubject->college_id = $college_id;
        $objSubject->created_by = $loginUserId;
        $objSubject->updated_by = $loginUserId;
        $objSubject->save();
    }

    public function getSubjectlistCollegeIdWise($subjectId)
    {
        $data = Subject::where('id', $subjectId)->get();

        return $data;
    }

    public function deleteSubjectRecords($subjectId)
    {
        return Subject::where('id', $subjectId)->delete();
    }

    public function getSubjectData($subjectListDatas, $arrGradingType)
    {

        $arrGradingTypeData = $arrGradingType;
        $tempsubjectListData = $subjectListDatas;
        foreach ($subjectListDatas as $key => $subjectListData) {
            $gradingType = $subjectListData->grading_type;
            if ($gradingType == 0) {
                $firstLang = '';
                $tempsubjectListData[$key]['gradingType'] = $firstLang;
            } else {
                $gradingName = $arrGradingTypeData[$gradingType];
                $tempsubjectListData[$key]['gradingType'] = $gradingName;
            }
        }

        return $tempsubjectListData;
    }

    public function getSubject($collegeId)
    {
        //        $arrSubject = Subject::where('college_id','=',$collegeId)->pluck('subject_name','id')->toArray();
        //        $arrSubjectList[''] = "- - Select Subject - -";
        //        return $rtoArrSubjectList = $arrSubjectList + $arrSubject;

        $arrSubject = Subject::where('college_id', '=', $collegeId)
            ->select('subject_name', 'subject_code', 'id')
            ->get()
            ->toArray();

        $arrSubjectList[''] = '- - Select Subject - -';
        foreach ($arrSubject as $row) {
            $arrSubjectList[$row['id']] = $row['subject_code'].' : '.Str::ascii($row['subject_name']);
        }

        return $arrSubjectList;
    }

    public function getSubjectUnitList($college_id, $subject_id)
    {
        $arrSubjectUnitList = Subject::leftjoin('rto_subject_unit', 'rto_subject_unit.subject_id', '=', 'rto_subject.id')
            ->where('rto_subject_unit.college_id', $college_id)
            ->where('rto_subject_unit.subject_id', $subject_id)
            ->get(['rto_subject_unit.subject_id', 'rto_subject_unit.id', 'rto_subject_unit.vet_unit_code', 'rto_subject_unit.unit_name'])
            ->toArray();

        return $arrSubjectUnitList;
    }

    public function getFilterSubjectList($college_id, $perPage, $subject_by, $course_type, $search_string)
    {

        $arrSubjectLists = Subject::leftjoin('rto_college_course_type', 'rto_college_course_type.id', '=', 'rto_subject.course_type')
            ->leftjoin(DB::raw('(SELECT subject_id, SUM(rto_subject_unit.nominal_hours) as total_houres FROM rto_subject_unit GROUP BY subject_id) as rsu'), 'rto_subject.id', '=', 'rsu.subject_id')
            ->leftjoin('rto_grading_type', 'rto_grading_type.id', '=', 'rto_subject.grading_type')
            ->select('rto_subject.id', 'rto_college_course_type.title as courseTypeTitle', 'total_houres', 'rto_grading_type.grading_name', 'rto_subject.subject_code', 'rto_subject.subject_name', 'rto_subject.created_at')
            ->where('rto_subject.college_id', $college_id);

        if ($course_type != 'Any' && $course_type != '') {
            $arrSubjectLists->where('rto_college_course_type.id', $course_type);
        }

        if ($subject_by == 'code' && $search_string != '') {
            $arrSubjectLists->where('rto_subject.subject_code', 'like', '%'.$search_string.'%');
        }

        if ($subject_by == 'name' && $search_string != '') {
            $arrSubjectLists->where('rto_subject.subject_name', 'like', '%'.$search_string.'%');
        }

        $arrSubjectLists->orderBy('rto_subject.id', 'desc');

        $resultData = $arrSubjectLists->get();

        //        $resultData = $arrSubjectLists->paginate($perPage);
        return $resultData;
    }

    // get course_type_id based on subject_id from timetable-teacher-replacement
    public function _getCourseTypeId($collegeId, $subjectId)
    {
        $courseTypeId = Subject::where('id', '=', $subjectId)
            ->where('college_id', '=', $collegeId)
            ->get(['course_type']);

        return $courseTypeId[0]->course_type;
    }

    public function _getSubject($courseTypeId)
    {
        $dataArr = Subject::where('course_type', '=', $courseTypeId)
            ->where('college_id', '=', Auth::user()->college_id)
            ->get(['subject_code', 'subject_name', 'id'])
            ->toarray();

        return $dataArr;
    }

    public function _getGradingTypeBySubject($subjectId, $college_id)
    {

        $gradingType = Subject::join('rto_grading_type', 'rto_grading_type.id', '=', 'rto_subject.grading_type')
            ->where('rto_subject.college_id', $college_id)
            ->where('rto_subject.id', $subjectId)
            ->get(['rto_grading_type.grading_name'])
            ->toArray();

        if (count($gradingType) != 0) {
            return $gradingType[0]['grading_name'];
        } else {
            return 'No Grade Found';
        }
    }

    public function _getGradingTypeBySubjectV2($subjectId, $college_id)
    {

        $gradingType = Subject::join('rto_grading_type as rgt', 'rgt.id', '=', 'rto_subject.grading_type')
            ->leftjoin('rto_result_grade as rrg', 'rrg.grading_type', '=', 'rgt.id')
            ->where('rto_subject.college_id', $college_id)
            ->where('rto_subject.id', $subjectId)
            ->get(['rgt.grading_name', 'rrg.use_marks'])
            ->toArray();

        if (count($gradingType) != 0) {
            return $gradingType[0];
        } else {
            return 'No Grade Found';
        }
    }

    public function getSubjectIDFromCode($subjectCode, $collegeId)
    {
        $resultArr = Subject::where('college_id', '=', $collegeId)
            ->where('subject_code', $subjectCode)
            ->get(['id']);

        return $resultArr[0]->id;
    }

    public function getGradingTypeBySubject($collegeId, $subjectId)
    {
        return Subject::from('rto_subject as rs')
            ->leftjoin('rto_grading_type as rgt', 'rgt.id', '=', 'rs.grading_type')
            ->where('rs.college_id', $collegeId)
            ->where('rs.id', $subjectId)
            ->value('grading_name');
    }

    public function getGradingTypeWithMarksBySubject($collegeId, $subjectId)
    {
        return Subject::from('rto_subject as rs')
            ->leftjoin('rto_grading_type as rgt', 'rgt.id', '=', 'rs.grading_type')
            ->leftjoin('rto_result_grade as rrg', 'rrg.grading_type', '=', 'rgt.id')
            ->where(['rs.college_id' => $collegeId, 'rs.id' => $subjectId])
            ->get(['rgt.grading_name', 'rrg.use_marks', 'rs.max_marks_allow', 'rs.course_type'])
            ->first();
    }

    public function getSubjectListV2($college_id, $request)
    {

        $requestData = $_REQUEST;
        $columns = [
            // datatable column index  => database column name
            0 => 'rto_grading_type.grading_name',
            1 => 'rto_subject.subject_code',
            2 => 'rto_subject.subject_name',
            3 => 'rto_college_course_type.title',
            4 => 'rto_subject_unit.nominal_hours',
            5 => 'rto_subject.created_at',
            6 => 'rto_college_course_type.description',
        ];

        $query = Subject::leftjoin('rto_college_course_type', 'rto_college_course_type.id', '=', 'rto_subject.course_type')
            ->leftjoin(DB::raw('(SELECT subject_id, SUM(rto_subject_unit.nominal_hours) as total_houres FROM rto_subject_unit GROUP BY subject_id) as rsu'), 'rto_subject.id', '=', 'rsu.subject_id')
            ->leftjoin('rto_grading_type', 'rto_grading_type.id', '=', 'rto_subject.grading_type')
            ->where('rto_subject.college_id', $college_id);

        if (! empty($requestData['search']['value'])) {   // if there is a search parameter, $requestData['search']['value'] contains search parameter
            $searchVal = $requestData['search']['value'];
            $query->where(function ($query) use ($columns, $searchVal, $requestData) {

                $flag = 0;
                foreach ($columns as $key => $value) {
                    $searchVal = $requestData['search']['value'];
                    if ($key == 5) {
                        $searchVal = date('Y-m-d', strtotime($searchVal));
                    }
                    if ($requestData['columns'][$key]['searchable'] == 'true') {
                        if ($flag == 0) {
                            $query->where($value, 'like', '%'.$searchVal.'%');
                            $flag = $flag + 1;
                        } else {
                            $query->orWhere($value, 'like', '%'.$searchVal.'%');
                        }
                    }
                }
            });
        }

        $temp = $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);

        $totalData = count($temp->get());
        $totalFiltered = count($temp->get());

        $resultArr = $query->skip($requestData['start'])
            ->take($requestData['length'])
            ->select('rto_subject.id', 'rto_college_course_type.title', 'total_houres', 'rto_grading_type.grading_name', 'rto_subject.subject_code', 'rto_subject.subject_name', 'rto_subject.created_at', 'rto_college_course_type.description')
            ->get();

        $data = [];
        foreach ($resultArr as $row) {

            $nestedData = [];
            $actionHtml = '';
            $actionHtml .= '<li><a href="'.route('edit-subject', ['id' => $row['id']]).'" class="link-black text-sm" data-toggle="tooltip" data-original-title="Edit"><i class="fa fa-edit"></i></a></li>';
            $actionHtml .= '<li><a href="'.route('element-of-unit-competency', ['id' => $row['id']]).'" class="link-black text-sm" data-toggle="tooltip" data-original-title="Elements of unit of competency"> <i class="fa fa-building" aria-hidden="true"></i> </a></li>';
            $actionHtml .= '<li><a href="'.route('edit-subject-module', ['id' => $row['id']]).'" class="link-black text-sm" data-toggle="tooltip" data-original-title="Manage Unit"> <i class="fa fa-cog" aria-hidden="true"></i> </a></li>';
            $actionHtml .= '<li><span data-toggle="modal" class="delete" data-id="'.$row['id'].'" data-target="#deleteModal"><a class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Delete" href="javascript:;"><i class="fa fa-remove"></i></a></span></li>';

            $action = '<div class="action-overlay">
                            <ul class="icon-actions-set">
                              '.$actionHtml.'
                            </ul>
                        </div>';
            $nestedData[] = $row['grading_name'].$action;
            $nestedData[] = $row['subject_code'];
            $nestedData[] = preg_replace('/[^A-Za-z0-9. -]/', '', $row['subject_name']);
            $nestedData[] = $row['title'];
            $nestedData[] = 'Contact Hours: <u>'.($row['total_houres'] > 0 ? $row['total_houres'] : 0).'</u> hours';
            $nestedData[] = date('d M Y', strtotime($row['created_at']));
            $nestedData[] = $row['description'];
            $data[] = $nestedData;
        }

        $json_data = [
            'draw' => intval($requestData['draw']), // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
            'recordsTotal' => intval($totalData), // total number of records
            'recordsFiltered' => intval($totalFiltered), // total number of records after searching, if there is no searching then totalFiltered = totalData
            'data' => $data,   // total data array
        ];

        return $json_data;
    }
}
