<?php

namespace App\Http\Controllers\v3\api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Resources\api\v3\CourseSummaryResource;
use App\Model\v2\Colleges;
use App\Model\v2\Tenant;
use App\Services\api\v3\CourseApiService;
use App\Services\api\v3\StudentApiService;
use App\Traits\ResponseTrait;
use Barryvdh\Debugbar\Facades\Debugbar;
use Exception;
use Http\Client\Exception\HttpException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Support\Services\UploadService;

class GeneralApiController extends Controller
{
    use ResponseTrait;

    protected $service;
    protected $course;

    public function __construct(StudentApiService $commonService, CourseApiService $courseService){
        $this->service = $commonService;
        $this->course = $courseService;
    }
    
    /**
     * Get The Base Url for the API endpoints.
     *
     * @unauthenticated
     * 
     * @response AnonymsResource[]
     * 
     */
    public function getBaseUrlForHosts(Request $request){
        try{
            $input = [];
            
            //$rawHost = $hostUrl = $request->header("origin") ?? $request->input('origin') ?? "";
            
            $rawHost = $hostUrl = $request->input('host') ?? "";


            if (!preg_match('/^https?:\/\//', $rawHost)) {
                $hostUrl = 'http://' . $rawHost;
            }
            $parsedUrl = parse_url($hostUrl);

            $host = $parsedUrl['host'] ?? null;

            //$input['origin'] = $hostUrl;
            $input['host'] = $hostUrl;

            $request->merge($input);

            $request->validate([
                /* 
                Provide origin host name as a query parameter 
                */
                //'origin' => ['required', 'url'],
                'host' => ['required', 'url'],
            ]);

            $tenant = Tenant::GetTenantInfoByAllowedDomain($host);

            $apiEnabled = false;
            
            if($tenant){
                $domain = $tenant->GetApiBaseurl();
                $apiEnabled = $tenant->getMeta('short_course_api_enabled') === "enabled";
            }else{
                $domain = null;
            }

            if(empty($tenant) || !$domain) throw new \Exception("API end point not found for the host.");
            if(!$apiEnabled) throw new \Exception("API not enabled for the host.");

            tenancy()->initialize($tenant);
            
            $collegeData = $this->getCollegeSettingsData();

            $colorProfile = Colleges::getCollegeInfoQuery()->value("color_profile");

            $data = array(
                    "url" => $domain,
                    "api_url" => $domain."/api/".config("app.current_api_version"),
                    "captcha_secret" => config("services.captcha.secret") ?? "",
                    "color_profile" => $colorProfile ?? null,
            );
            return $this->successResponse("API Endpoint found", "constants", [...$data, ...$collegeData]);
        }catch(\Exception $e){
            return $this->errorResponse($e->getMessage());
        }
    }
    public function getCollegeSettingsData(){
        
        //$data = Colleges::with(['collegedetail'])->find($this->loginUser->college_id);
        $data = Colleges::with(['collegedetail'])->orderByDesc("status")->orderByDesc("id")->first();
        //$data = $this->colleges->withId(['collegedetail'], $this->loginUser->college_id)->toArray();
        

        $filePath = config('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath, NULL, $data['id']);
        
        $logo_picture_path = $destinationPath['view'] . $data['college_logo'];

        $logo_picture_url = UploadService::exists($logo_picture_path) ? UploadService::url($logo_picture_path) : null;

        $defaultCollegeTimezone = config('constants.default_college_timezone');

        $collegeData = array(
            "RTO_code" => $data->RTO_code ?? null,
            "CRICOS_code" => $data->CRICOS_code ?? null,
            "college_name" => $data->college_name ?? null,
            "public_RTO" => $data->public_RTO ?? null,
            "contact_phone" => $data->contact_phone ?? null,
            "contact_email" => $data->contact_email ?? null,
            "college_url" => $data->college_url ?? null,
            "college_logo" => $logo_picture_url ?? null,
            "timezone" => $data->timezone ?? $defaultCollegeTimezone,
            "marketing_email" => $data->collegedetail->marketing_email ?? null,
            "admission_email" => $data->collegedetail->admission_email ?? null,
            "it_email" => $data->collegedetail->it_email ?? null,
            "acedemic_email" => $data->collegedetail->acedemic_email ?? null,
            //"account_email" => $data->collegedetail->account_email ?? null,
            "fax" => $data->collegedetail->fax ?? null,
            "street_address" => $data->collegedetail->street_address ?? null,
            "street_address2" => $data->collegedetail->street_address2 ?? null,
            "street_address3" => $data->collegedetail->street_address3 ?? null,
            "street_suburb" => $data->collegedetail->street_suburb ?? null,
            "street_state" => $data->collegedetail->street_state ?? null,
            "street_postcode" => $data->collegedetail->street_postcode ?? null,
            "postal_same_street" => $data->collegedetail->postal_same_street ?? null,
            "postal_address" => $data->collegedetail->postal_address ?? null,
            "postal_suburb" => $data->collegedetail->postal_suburb ?? null,
            "postal_state" => $data->collegedetail->postal_state ?? null,
        );
        
        return $collegeData;
    }
}
