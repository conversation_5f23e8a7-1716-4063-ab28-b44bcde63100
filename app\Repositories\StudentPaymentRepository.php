<?php

namespace App\Repositories;

use App\Helpers\Helpers;
use App\Model\v2\Agent;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentInitialPaymentDetails;
use App\Model\v2\StudentInitialPaymentTransaction;
use App\Model\v2\PaymentMode;
use App\Model\v2\InvoiceNumber;
use App\Model\v2\StudentAdditionalServiceRequest;
use App\Model\v2\StudentAgentCommission;
use App\Model\v2\StudentMiscellaneousPayment;
use App\Model\v2\StudentScholarship;
use App\Model\v2\StudentInitialPayment;
use App\Model\v2\ResceiptNumber;
use App\Model\v2\StudentCommunicationLog;
use App\Model\v2\AgentCommission;
use App\Model\TransactionNumber;
use App\Model\v2\EmailTemplate;
use App\Model\v2\Student;
use App\Model\v2\StudentServicePayment;
use App\Traits\CommonTrait;
use Domains\Xero\Models\XeroCreditNote;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Domains\Xero\Models\XeroInvoice;
use Domains\Xero\Facades\Xero;
use Config;
use Auth;
use Maatwebsite\Excel\Concerns\ToArray;

class StudentPaymentRepository extends CommonRepository
{

    use CommonTrait;

    protected $model;

    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    /*public function getPaymentModeName($fields)
    {
        return PaymentMode::select($fields)->get()->toArray();
    }

    public function getInvoiceNumber($fields)
    {
        return InvoiceNumber::where($fields)->get('invoice_number');
    }

    public function getReceiptNumber()
    {
        return ResceiptNumber::where('college_id',Auth::user()->college_id)->value('resceipt_number');
    }

    public function getTransactionNumber($college_id)
    {
        $objTransactionNumber =  TransactionNumber::where('college_id', $college_id)->get(['transaction_number']);
        return $objTransactionNumber[0]->transaction_number;
    }

    public function getStudentInitialPaymentDetails($keyValue)
    {
        $getInitialPaymentValue = StudentInitialPaymentDetails::find($keyValue);
        return $getInitialPaymentValue;
    }

    public function getInitialPaymentDetails($paymentDetailId){
        return StudentInitialPaymentDetails::where('id', $paymentDetailId)->get();
    }


    public function findStudentInitialPaymentDetails($paymentDetailId)
    {
        return StudentInitialPaymentDetails::find($paymentDetailId);
    }*/

    public function saveStudentInitialPayment($checkRecord, $request)
    {
        return StudentInitialPayment::firstOrCreate($checkRecord, $request);
    }

    public function updateStudentInitialPaymentDetail($checkRecord, $request)
    {
        return StudentInitialPaymentDetails::UpdateOrCreate($checkRecord, $request);
    }
    public function UpdateStudentInitialPaymentTransaction($checkRecord, $request)
    {
        return StudentInitialPaymentTransaction::UpdateOrCreate($checkRecord, $request);
    }

    /*public function getStudentInitialPayment($college_id, $student_id, $course_id,$student_course_id)
    {
        return StudentInitialPayment::where(['college_id' => $college_id,'student_id' => $student_id,'course_id' => $course_id,'student_course_id' => $student_course_id])->get()->toArray();
    }*/

    // public function studentPaymentDetailsGet($collegeId, $courseId, $studentId, $invoiceNo = '',$student_course_id) {
    //     $sql = StudentInitialPaymentDetails::from('rto_student_initial_payment_details')
    //             ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rto_student_initial_payment_details.agent_id')
    //             ->where('rto_student_initial_payment_details.college_id', '=', $collegeId)
    //             ->where('rto_student_initial_payment_details.student_id', '=', $studentId);
    //         if (!empty($courseId)) {
    //             $sql->where('rto_student_initial_payment_details.course_id', '=', $courseId);
    //         }
    //         if (!empty($invoiceNo)) {
    //             $sql->where('rto_student_initial_payment_details.invoice_number', '=', $invoiceNo);
    //         }
    //         if (!empty($student_course_id)) {
    //             $sql->where('rto_student_initial_payment_details.student_course_id', '=', $student_course_id);
    //         }
    //     return $sql->sum('rto_student_initial_payment_details.upfront_fee_to_pay');
    // }

    public function studentPaymentDetailsGet($collegeId, $courseId, $studentId, $invoiceNo = '', $student_course_id)
    {
        $sql = StudentInitialPaymentDetails::from('rto_student_initial_payment_details')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rto_student_initial_payment_details.agent_id')
            ->where('rto_student_initial_payment_details.college_id', '=', $collegeId)
            ->where('rto_student_initial_payment_details.student_id', '=', $studentId);
        if (!empty($courseId)) {
            $sql->where('rto_student_initial_payment_details.course_id', '=', $courseId);
        }
        if (!empty($invoiceNo)) {
            $sql->where('rto_student_initial_payment_details.invoice_number', '=', $invoiceNo);
        }
        if (!empty($student_course_id)) {
            $sql->where('rto_student_initial_payment_details.student_course_id', '=', $student_course_id);
        }
        return $sql->select('rto_student_initial_payment_details.*')->get()->toArray();
    }

    public function saveStudentInitialPaymentDetailsOnly($request)
    {
        $initialPaymentDetail = StudentInitialPaymentDetails::create($request);
        return $initialPaymentDetail->id;
    }

    public function saveStudentInitialPaymentTransaction($request)
    {
        StudentInitialPaymentTransaction::create($request);
        StudentCommunicationLog::create($request);
        return true;
    }

    public function saveStudentInitialPaymentDetails($request)
    {

        $request['initial_payment_detail_id'] = $this->saveStudentInitialPaymentDetailsOnly($request);
        $this->editTransactionNumber();
        $this->saveStudentAgentCommission($request);
        $this->saveStudentInitialPaymentTransaction($request);
        return true;
    }

    public function updateInvoiceNumber($invoiceExist, $NewInvoiceNumber = false)
    {
        $invoiceNumber = InvoiceNumber::where('college_id', Auth::user()->college_id)->where('invoice_number', $invoiceExist)->get(['id', 'invoice_number'])->toArray();
        if (!empty($NewInvoiceNumber)) {
            InvoiceNumber::where('id', $invoiceNumber[0]['id'])->update(['invoice_number' => $NewInvoiceNumber]);
        } else {
            if (isset($invoiceNumber) && count($invoiceNumber)) {
                $incNumber = sprintf("%'03d", $invoiceNumber[0]['invoice_number'] + 1);
                InvoiceNumber::where('id', $invoiceNumber[0]['id'])->update(['invoice_number' => $incNumber]);
            }
        }
    }

    public function saveStudentAgentCommission($request)
    {
        return StudentAgentCommission::create($request);
    }

    /*public function getCourseId($studentCourseId){
        return StudentCourses::where('id', $studentCourseId)->value('course_id');
    }*/

    public function saveStudentCredit($studentId, $studentCredit)
    {
        $maxCredit = StudentInitialPayment::where('college_id', '=', Auth::user()->college_id)
            ->where('student_id', '=', $studentId)
            ->max('student_credit');
        $updateCredit = StudentInitialPayment::where('college_id', '=', Auth::user()->college_id)
            ->where('student_id', '=', $studentId)
            ->update(['student_credit' => $maxCredit + $studentCredit]);
    }

    public function updateStudentCredit($studentId, $studentCredit)
    {
        $maxCredit = StudentInitialPayment::select('student_credit')->where('college_id', '=', Auth::user()->college_id)->where('student_id', '=', $studentId)->first()->toArray();
        $remainingCreditAmount = abs($studentCredit - $maxCredit['student_credit']);
        $updateCredit = StudentInitialPayment::where('college_id', '=', Auth::user()->college_id)->where('student_id', '=', $studentId)->update(['student_credit' => $remainingCreditAmount]);
    }

    public function editTransactionNumber()
    {
        $transctionNumber = TransactionNumber::where('college_id', '=', Auth::user()->college_id)->get(['id', 'transaction_number'])->toArray();
        $findTransactionNumber = $transctionNumber[0]['transaction_number'];
        $incNumber = sprintf("%'03d", $findTransactionNumber + 1);
        $objsaveTransactionNumber = TransactionNumber::find($transctionNumber[0]['id']);
        $objsaveTransactionNumber->transaction_number = $incNumber;
        $objsaveTransactionNumber->save();
    }

    public function saveMiscellaneousPaySchedule($studentId, $courseId, $request, $amount, $paymentType, $invoiceNumber, $studentCourseId, $transactionNumber)
    {
        $loginData = auth()->user();
        $dataArr = [
            'college_id'            => $loginData->college_id,
            'student_id'            => $studentId,
            'course_id'             => $courseId,
            'student_course_id'     => $studentCourseId,
            'invoice_number'        => $invoiceNumber,
            'resceipt_number'       => ($request->receipt_number != "") ? $request->receipt_number : null,
            'transaction_number'    => $transactionNumber,
            'payment_mode'          => $request->payment_mode,
            'payment_type'          => $paymentType,
            'amount'                => $amount,
            'paid_amount'           => $amount,
            'remarks'               => ($request->remarks != "") ? $request->remarks : null,
            'due_date'              => date('Y-m-d', strtotime($request->payment_date)),
            'reserved'              => 'No',
            'refund'                => 0,
            'resceipt'              => 0,
            'paid_on'               => date('Y-m-d', strtotime($request->payment_date)),
            'payment_status'        => 'paid',
            'created_by'            => $loginData->id,
            'updated_by'            => $loginData->id
        ];
        StudentMiscellaneousPayment::create($dataArr);
    }

    public function savePayAgentCommission($arrAgentData)
    {
        return StudentAgentCommission::create($arrAgentData);
    }

    public function saveCommunicationLog($logData)
    {
        StudentCommunicationLog::create($logData);
    }

    public function saveStudentPaymentTransaction($data)
    {
        return StudentInitialPaymentTransaction::create($data);
    }



    public function editResceiptNumber()
    {
        $resceiptNumber = ResceiptNumber::where('college_id', Auth::user()->college_id)->get(['id', 'resceipt_number'])->toArray();
        $findResceiptNumber = $resceiptNumber[0]['resceipt_number'];
        $incNumber = sprintf("%'03d", $findResceiptNumber + 1);
        return ResceiptNumber::where('id', $resceiptNumber[0]['id'])->update(['resceipt_number' => $incNumber]);
    }


    /*public function getAgentTotalPayAmount($data)
    {
        return StudentInitialPaymentDetails::where('college_id', '=', $data['college_id'])
            ->where('student_course_id', '=',$data['student_course_id'])
            ->where('student_id', '=', $data['student_id'])
            ->sum('upfront_fee_pay');
    }*/

    public function getAgentTotalCommissionPaid($whereArr)
    {
        $totalCommissionPaid = StudentAgentCommission::where($whereArr)->sum('commission_paid');
        return $totalCommissionPaid ?? 0;
    }

    public function getAgentCommissionPayable($whereArr)
    {
        $totalCommissionPayable = StudentAgentCommission::where($whereArr)
            ->selectRaw('SUM(commission_payable + gst_amount - comm_to_refund - GST_to_refund) as total')
            ->value('total');
        return $totalCommissionPayable ?? 0;
    }

    public function getAgentData($studCourseId)
    {
        $studentCourses = StudentCourses::with('agent')->find($studCourseId);
        $agentData = $studentCourses->agent ?? null;
        return $agentData;
    }

    public function getAgentName($studCourseId)
    {
        $studentCourses = StudentCourses::with('agent')->find($studCourseId);
        $agentName = $studentCourses->agent->agency_name ?? null;
        return trim($agentName);
    }

    public function getAgentCommissionRate($collegeId, $commissionPeriod, $courseId, $agentId, $courseStart)
    {
        return AgentCommission::where('college_id', $collegeId)
            ->where('commission_period', $commissionPeriod)
            ->where('course', $courseId)
            ->where('agent_id', $agentId)
            ->where('rate_valid_from', '<=', $courseStart)
            ->where('rate_valid_to', '>=', $courseStart)
            ->select('commission', 'gst')
            ->get()->toArray();
    }

    public function modifyAgentCommissionData($data)
    {
        $collegeId = Auth::user()->college_id;
        return StudentAgentCommission::from('rto_student_agent_commission')
            ->join('rto_student_initial_payment_details', 'rto_student_initial_payment_details.invoice_number', '=', 'rto_student_agent_commission.invoice_no')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_agent_commission.agent_id')
            ->where([
                'rto_student_agent_commission.college_id' => $collegeId,
                'rto_student_agent_commission.id' => $data['agent_commission_id'],
            ])
            ->get([
                'rto_agents.agency_name',
                'rto_student_initial_payment_details.commission_value',
                'rto_student_initial_payment_details.GST as commission_gst',
                'rto_student_agent_commission.*'
            ]);
    }

    public function getStudentScholarshipAmount($collegeId, $studentId, $courseId)
    {
        $scholarshipAmount = StudentScholarship::where([
                'college_id'    => $collegeId,
                'student_id'    => $studentId,
                'course_id'     => $courseId,
                'is_transfer'   => 0
            ])
            ->select(DB::raw('sum(scholarship_amount) - sum(used_scholarship_amount)  AS total_amount'))
            ->get()
            ->toArray();
        return $scholarshipAmount;
    }

    public function getStudentCreditAmount($collegeId, $studentId)
    {
        $credit = StudentInitialPayment::where([
            'college_id' => $collegeId,
            'student_id' => $studentId
        ])->value('student_credit');

        return ($credit > 0) ? $credit : 0;
    }

    public function getInitialPendingPaymentDetails($paymentDetailId, $studentCourseId, $studentId,  $collegeId)
    {
        return StudentInitialPaymentDetails::where('id', '!=', $paymentDetailId)
            ->where('student_course_id',  $studentCourseId)
            ->where('student_id',  $studentId)
            ->where('college_id', $collegeId)
            ->whereNot('payment_status', 'paid')
            ->get();
    }

    public function getAllPaymentsData($request, $countOnly = false)
    {

        $post = ($request->input()) ? $request->input() : [];

        // Query 1: Initial Payments
        $initialWhereArr = [
            'rto_student_initial_payment_details.college_id'          => Auth::user()->college_id,
            'rto_student_initial_payment_details.student_id'          => $request->student_id,
            'rto_student_initial_payment_details.student_course_id'   => $request->student_course_id,
        ];
        $initialColumnArr = [
            'rpm.name as payment_modes',
            'rto_student_initial_payment_details.*',
            'rsipt.payment_date as paid_on',
            'rto_student_initial_payment_details.upfront_fee_pay as paid_amount',
            DB::raw("upfront_fee_to_pay - upfront_fee_pay - invoice_credit as amount_due"),
            DB::raw("CASE WHEN rto_student_initial_payment_details.payment_type = 'Initial' THEN 'Pre-Payment' ELSE 'Tuition Fee' END as fee_type"),
            DB::raw("CASE WHEN rto_student_initial_payment_details.payment_type = 'Initial' THEN 'Pre-Payment' ELSE 'Tuition Fee' END as invoice_type"),
            DB::raw("
                CASE
                    WHEN CURDATE() > rto_student_initial_payment_details.due_date
                        THEN (upfront_fee_to_pay - upfront_fee_pay - invoice_credit)
                    ELSE 0
                END as amount_overdue
            "),
        ];
        $filterInitialColumns = [
            "formatted_invoice_number"  => dbRawL10("CONCAT('GAL-', rto_students.generated_stud_id, '-', rto_courses.course_code, '-AG', rto_student_initial_payment_details.agent_id, '-', rto_student_initial_payment_details.invoice_number) as formatted_invoice_number"),
            //"payment_type"            => 'Tuition fees',
            "fee_type"                  => dbRawL10("CASE WHEN rto_student_initial_payment_details.payment_type = 'Initial' THEN 'Pre-Payment' ELSE 'Tuition Fee' END as fee_type"),
            "amount_due"                => dbRawL10("upfront_fee_to_pay - upfront_fee_pay - invoice_credit as amount_due"),
            "due_date"                  => dbRawL10("rto_student_initial_payment_details.due_date"),
            'payment_status'            => 'rto_student_initial_payment_details.payment_status',
            "paid_on"                   => 'rsipt.payment_date as paid_on',
        ];
        $initialPaymentQuery = StudentInitialPaymentDetails::withTrashed()
            ->leftJoin('rto_student_initial_payment_transaction as rsipt', 'rsipt.initial_payment_detail_id', '=', 'rto_student_initial_payment_details.id')
            ->leftJoin('rto_payment_mode as rpm', 'rpm.id', '=', 'rto_student_initial_payment_details.payment_mode')
            ->join('rto_students', 'rto_students.id', '=', 'rto_student_initial_payment_details.student_id')
            ->join('rto_courses', 'rto_courses.id', '=', 'rto_student_initial_payment_details.course_id')
            ->where($initialWhereArr)
            ->select($initialColumnArr)
            ->groupBy('rto_student_initial_payment_details.id')
            ->with(['xeroInvoice']);

        $this->gridDataFilter($initialPaymentQuery, $post, $filterInitialColumns);

        // Query 2: Miscellaneous Payments
        $miscellaneousWhereArr = [
            'rto_student_miscellaneous_payment.college_id'          => $post['college_id'],
            'rto_student_miscellaneous_payment.student_id'          => $post['student_id'],
            'rto_student_miscellaneous_payment.student_course_id'   => $post['student_course_id']
        ];
        $miscellaneousColumnArr = [
            'rpm.name as payment_mode_name',
            'rto_student_miscellaneous_payment.*',
            DB::raw("IF(rto_student_miscellaneous_payment.payment_status = 'unpaid', '-', rto_student_miscellaneous_payment.paid_on) as paid_on"),
            DB::raw("IF(rto_student_miscellaneous_payment.payment_status = 'unpaid', 'N/A', rpm.name) as payment_mode_name"),
            DB::raw("amount - paid_amount as amount_due"),
            DB::raw("CONCAT('Miscellaneous (', rto_student_miscellaneous_payment.payment_type, ')') as fee_type"),
            DB::raw("CONCAT('Miscellaneous') as invoice_type"),
            DB::raw("
                CASE
                    WHEN CURDATE() > rto_student_miscellaneous_payment.due_date
                        THEN amount - paid_amount
                    ELSE 0
                END as amount_overdue
            "),
        ];
        $filterMiscellaneousColumns = [
            "formatted_invoice_number"  => dbRawL10("CONCAT('GAL-MISC-', rto_students.generated_stud_id, '-', rto_courses.course_code, '-', rto_student_miscellaneous_payment.invoice_number) as formatted_invoice_number"),
            //"payment_type"            => 'rto_student_miscellaneous_payment.payment_type',
            "fee_type"                  => dbRawL10("CONCAT('Miscellaneous (', rto_student_miscellaneous_payment.payment_type, ')') as fee_type"),
            "amount_due"                => dbRawL10("rto_student_miscellaneous_payment.amount - rto_student_miscellaneous_payment.paid_amount as amount_due"),
            "due_date"                  => dbRawL10("rto_student_miscellaneous_payment.due_date"),
            "payment_status"            => 'rto_student_miscellaneous_payment.payment_status',
            "paid_on"                   => dbRawL10("IF(rto_student_miscellaneous_payment.payment_status = 'unpaid', '-', rto_student_miscellaneous_payment.paid_on) as paid_on"),
        ];
        $miscellaneousPaymentQuery = StudentMiscellaneousPayment::withTrashed()
            ->leftJoin('rto_payment_mode as rpm', 'rpm.id', '=', 'rto_student_miscellaneous_payment.payment_mode')
            ->join('rto_students', 'rto_students.id', '=', 'rto_student_miscellaneous_payment.student_id')
            ->join('rto_courses', 'rto_courses.id', '=', 'rto_student_miscellaneous_payment.course_id')
            //->leftJoin('rto_oshc_providers as rop', 'rop.id', '=', 'rto_student_miscellaneous_payment.oshc_provider')
            ->where($miscellaneousWhereArr)
            ->select($miscellaneousColumnArr)
            ->with(['xeroInvoice']);

        $this->gridDataFilter($miscellaneousPaymentQuery, $post, $filterMiscellaneousColumns);

        // Query 3: Service Payments
        $serviceColumnArr = [
            'rto_student_service_payment.*',
            DB::raw("rto_student_service_payment.amount - rto_student_service_payment.paid_amount as amount_due"),
            'rto_student_service_payment.paid_date as paid_on',
            DB::raw("CONCAT('Service') as fee_type"),
            DB::raw("CONCAT('Service') as invoice_type"),
            DB::raw("
                CASE
                    WHEN CURDATE() > rto_student_service_payment.due_date
                        THEN rto_student_service_payment.amount - rto_student_service_payment.paid_amount
                    ELSE 0
                END as amount_overdue
            "),
        ];
        $serviceWhereArr = [
            'rto_student_service_payment.college_id'        => $post['college_id'],
            'rto_student_service_payment.student_id'        => $post['student_id'],
            'rto_student_service_payment.student_course_id' => $post['student_course_id']
        ];
        $filterServiceColumns = [
            "formatted_invoice_number"  => dbRawL10("CONCAT('GAL-SRV-', rto_students.generated_stud_id, '-', rto_courses.course_code, '-', rto_student_service_payment.invoice_number) as formatted_invoice_number"),
            //"payment_type"            => '',
            "fee_type"                  => dbRawL10("CONCAT('Service') as fee_type"),
            "amount_due"                => dbRawL10("rto_student_service_payment.amount - rto_student_service_payment.paid_amount as amount_due"),
            "due_date"                  => dbRawL10("rto_student_service_payment.due_date"),
            "payment_status"            => 'rto_student_service_payment.payment_status',
            "paid_on"                   => 'rto_student_service_payment.paid_date',
        ];
        $servicePaymentQuery = StudentServicePayment::withTrashed()
            ->join('rto_student_additional_service_request as rsasr', 'rsasr.id', '=', 'rto_student_service_payment.additional_services_id')
            ->join('rto_students', 'rto_students.id', '=', 'rto_student_service_payment.student_id')
            ->join('rto_courses', 'rto_courses.id', '=', 'rto_student_service_payment.course_id')
            ->where($serviceWhereArr)
            ->groupBy('rto_student_service_payment.id')
            ->select($serviceColumnArr)
            ->with(['xeroInvoice']);

        $this->gridDataFilter($servicePaymentQuery, $post, $filterServiceColumns);

        //$this->gridDataFilter($query, $post, $columns);

        // Fetch data from both queries
        $initialPayments = $initialPaymentQuery->get();
        $miscellaneousPayments = $miscellaneousPaymentQuery->get();
        $servicePayments = $servicePaymentQuery->get();

        // Combine the two collections
        $combinedData = $initialPayments->merge($miscellaneousPayments)->merge($servicePayments);
        $totalPaidAmount = $combinedData->sum('paid_amount');
        $totalDueAmount = $combinedData->sum('amount_due');
        $totalOverdueAmount = $combinedData->sum('amount_overdue');

        // Apply filtering and sorting using collections
        if (!empty($post['filter'])) {
            $combinedData = $combinedData->filter(function ($item) use ($post) {
                // Example: Filter by payment status
                return $item['payment_status'] === ($post['filter']['payment_status'] ?? $item['payment_status']);
            });
        }

        // Apply sorting
        if (isset($post['sort'])) {
            foreach ($post['sort'] as $sort) {
                $combinedData = $combinedData->sortBy([
                    [$sort['field'], $sort['dir'] === 'asc' ? SORT_ASC : SORT_DESC]
                ]);
            }
        }

        // Return paginated data
        if ($countOnly) {
            return $combinedData->count();
        }

        $perPage = $post['take'] ?? 10;
        $page = $post['page'] ?? 1;
        $total = $combinedData->count();
        $paginatedData = $combinedData->forPage($page, $perPage)->values();

        return [
            'total'         => $total,
            'data'          => $paginatedData,
            'amountData'    => [
                'total_paid'    => $totalPaidAmount ?? 0,
                'total_due'     => $totalDueAmount ?? 0,
                'total_overdue' => $totalOverdueAmount ?? 0
            ]
        ];
    }

    public function getInitialOrSchedulePaymentData($request, $countOnly = false)
    {

        $post = ($request->input()) ? $request->input() : [];

        $whereArr = [
            'rto_student_initial_payment_details.college_id'          => Auth::user()->college_id,
            'rto_student_initial_payment_details.student_id'          => $request->student_id,
            'rto_student_initial_payment_details.student_course_id'   => $request->student_course_id,
            'rto_student_initial_payment_details.payment_type'        => $request->payment_type   //TODO::GNG-2576
        ];

        $columnArr = [
            'rto_student_initial_payment_details.id as student_payment_detail_id',
            'rpm.name as payment_modes',
            'ra.agency_name',
            'rsac.is_approved',
            'rsac.id as stud_agent_comm_id',
            //'rto_student_initial_payment_details.invoice_number as formatted_invoice_number',
            DB::raw("upfront_fee_to_pay - upfront_fee_pay - invoice_credit as amount"),
            DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as full_name"),
            DB::raw("SUM(rsac.commission_paid) as paid_commission"),
            //DB::raw("CONCAT(DATE_FORMAT(rto_student_initial_payment_details.invoiced_start_date, '%d %b %Y'),' - ',DATE_FORMAT(DATE_SUB(rto_student_initial_payment_details.due_date, INTERVAL 1 DAY), '%d %b %Y')) as fee_duration"),
            DB::raw("CASE
                        WHEN rto_student_initial_payment_details.invoiced_start_date < rto_student_initial_payment_details.due_date THEN CONCAT(DATE_FORMAT(rto_student_initial_payment_details.invoiced_start_date, '%d %b %Y'), ' - ', DATE_FORMAT(DATE_SUB(rto_student_initial_payment_details.due_date, INTERVAL 1 DAY), '%d %b %Y'))
                        ELSE CONCAT(DATE_FORMAT(rto_student_initial_payment_details.invoiced_start_date, '%d %b %Y'), ' - ', DATE_FORMAT(rto_student_initial_payment_details.due_date, '%d %b %Y'))
                      END as fee_duration"),
            'rto_student_initial_payment_details.*'
        ];

        /* This array use for filterable only */
        $columns = [
            "formatted_invoice_number"  => dbRawL10("CONCAT('GAL-', rs.generated_stud_id, '-', rto_courses.course_code, '-AG', rto_student_initial_payment_details.agent_id, '-', rto_student_initial_payment_details.invoice_number) as formatted_invoice_number"),
            "invoice_number"            => 'rto_student_initial_payment_details.invoice_number',
            "invoiced_start_date"       => 'rto_student_initial_payment_details.invoiced_start_date',
            "upfront_fee_pay"           => dbRawL10("upfront_fee_to_pay - upfront_fee_pay - invoice_credit as amount"),
            "due_date"                  => 'rto_student_initial_payment_details.due_date',
            "payment_status"            => 'rto_student_initial_payment_details.payment_status',
            //"fee_duration"            => dbRawL10("CONCAT(DATE_FORMAT(rto_student_initial_payment_details.invoiced_start_date, '%d %b %Y'),' - ',DATE_FORMAT(DATE_SUB(rto_student_initial_payment_details.due_date, INTERVAL 1 DAY), '%d %b %Y')) as fee_duration"),
            "fee_duration"              => dbRawL10("CASE
                                                    WHEN rto_student_initial_payment_details.invoiced_start_date < rto_student_initial_payment_details.due_date THEN CONCAT(DATE_FORMAT(rto_student_initial_payment_details.invoiced_start_date, '%d %b %Y'), ' - ', DATE_FORMAT(DATE_SUB(rto_student_initial_payment_details.due_date, INTERVAL 1 DAY), '%d %b %Y'))
                                                    ELSE CONCAT(DATE_FORMAT(rto_student_initial_payment_details.invoiced_start_date, '%d %b %Y'), ' - ', DATE_FORMAT(rto_student_initial_payment_details.due_date, '%d %b %Y'))
                                                  END as fee_duration"),
            "commission"                => "rto_student_initial_payment_details.commission",
            //"xero_invoice"            => '',
            //"xero_synced_at"          => '',
        ];

        $query = StudentInitialPaymentDetails::withTrashed()
            ->leftjoin('rto_student_initial_payment_transaction as rsipt', 'rsipt.initial_payment_detail_id', '=', 'rto_student_initial_payment_details.id')
            ->leftjoin('rto_student_agent_commission as rsac', 'rsac.invoice_no', '=', 'rto_student_initial_payment_details.invoice_number')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rto_student_initial_payment_details.agent_id')
            ->leftjoin('rto_payment_mode as rpm', 'rpm.id', '=', 'rto_student_initial_payment_details.payment_mode')
            ->join('rto_students as rs', 'rs.id', '=', 'rto_student_initial_payment_details.student_id')
            ->join('rto_courses', 'rto_courses.id', '=', 'rto_student_initial_payment_details.course_id')
            ->where($whereArr)
            ->select($columnArr)
            ->with(['xeroInvoice'])
            ->groupBy('rto_student_initial_payment_details.id');

        $this->gridDataFilter($query, $post, $columns);

        if(isset($post['sort']) && isset($post['sort'][0]['field']) && $post['sort'][0]['field'] == 'formatted_invoice_number'){
            $post['sort'][0]['field'] = 'invoice_number';
        }

        $this->gridDataSorting($query, $post);
        return $this->gridDataPaginationWithTotal($query, $post, 'Schedule');

        //return $this->gridDataPaginationV2($query, $post, $countOnly);
        //return $this->cacheStorageForGrid('paymentScheduleData', $query, $post, $countOnly);
    }

    public function getXeroFailedPaymentsData($request, $countOnly = false)
    {
        $post = ($request->input()) ? $request->input() : [];
        $customFilterParts = (isset($post['filter']) && isset($post['filter']['filters'])) ? $post['filter']['filters'] : [];

        $query = XeroInvoice::with('invoiceable.student')->whereNotNull('xero_failed_at');

        foreach ($customFilterParts as $filter) {
            if ($filter['field'] == 'extra' && isset($filter['value'])) {
                $exFilter = $filter['value'];
                if ($exFilter['user_type'] == 'student') {
                    $query->whereIn('invoiceable_type', ['rsipdt', 'rsipd', 'rsmp', 'rssrvp']);
                } elseif ($exFilter['user_type'] == 'agent') {
                    $query->where('invoiceable_type', 'rsac');
                }
                if ($exFilter['end_date'] != "") {
                    $startDate = date('Y-m-d 00:00:00', strtotime($exFilter['start_date']));
                    $endDate = date('Y-m-d 00:00:00', strtotime($exFilter['end_date']));
                    $query->whereBetween('created_at', array($startDate, $endDate));
                }
            }
        }

        $this->gridDataSorting($query, $post);
        //$result = $this->gridDataPagination($query, $post, $countOnly);

        $invoiceResult = $query->paginate(10);

        $creditNoteResult = XeroCreditNote::with('creditable.student')->whereNotNull('xero_failed_at')->paginate(10);
        $results = $invoiceResult->map(function ($row) {
            if ($row['invoiceable_type'] == 'rsac') {
                $agent = Agent::find($row['invoiceable']['agent_id']);
                $row['invoiceable']['agent'] = $agent ? $agent->toArray() : null;
            }
            return $row;
        });

        $results = $results->merge($creditNoteResult);

        return [
            'total' => $invoiceResult->total() + $creditNoteResult->total(),
            'data' => $results->toArray()
        ];
    }

    public function checkValidAgentCommissionDaterange($id)
    {
        $columnArr = [
            'rto_student_initial_payment_details.invoice_number',
            'rto_student_initial_payment_transaction.payment_date',
            'rto_agent_commission.rate_valid_from',
            'rto_agent_commission.rate_valid_to'
        ];

        $res = StudentAgentCommission::from('rto_student_agent_commission')
            ->join('rto_agent_commission', function($join) {
                $join->on('rto_agent_commission.agent_id', '=', 'rto_student_agent_commission.agent_id');
                $join->on('rto_agent_commission.course', '=', 'rto_student_agent_commission.course_id');
            })
            ->join('rto_student_initial_payment_details', 'rto_student_initial_payment_details.invoice_number', '=', 'rto_student_agent_commission.invoice_no')
            ->join('rto_student_initial_payment_transaction', 'rto_student_initial_payment_transaction.initial_payment_detail_id', '=', 'rto_student_initial_payment_details.id')
            ->where('rto_student_agent_commission.id', $id)
            ->select($columnArr)
            ->groupBy('rto_student_agent_commission.id')
            ->first();

        if ($res) {
            if ($res->payment_date > $res->rate_valid_to) {
                return true; //$res->invoice_number; // Return the invoice number if condition is true
            }
            return false; // Otherwise, return true
        }

        return null; // Return null if no record found
    }

    public function getAgentCommissionData($request, $countOnly = false)
    {
        $sqlDateFormat = Helpers::toMysqlDateFormat();
        $post = ($request->input()) ? $request->input() : [];
        $isPrimaryId = (isset($post['primary_id']) && $post['primary_id'] > 0) ? true : false;
        if($isPrimaryId){
            $post['take'] = 1;
        }

        $whereArr = [
            'rto_student_agent_commission.college_id' => $post['college_id'],
            'rto_student_agent_commission.student_id' => $post['student_id'],
            'rto_student_agent_commission.student_course_id' => $post['student_course_id']
        ];
        if($isPrimaryId){
            $whereArr['rto_student_agent_commission.id'] = $post['primary_id'];
        }

        $columnArr = [
            'rto_agents.agency_name',
            'rto_student_agent_commission.*',
            'rto_payment_mode.name as payment_mode',
            'rto_student_initial_payment_details.invoice_number',
            'rto_student_initial_payment_transaction.payment_date',
            'rto_agent_commission.id as agent_commission_id',
            'rto_agent_commission.rate_valid_from',
            'rto_agent_commission.rate_valid_to',
            DB::raw("CONCAT(DATE_FORMAT(rto_agent_commission.rate_valid_from, '$sqlDateFormat'), ' - ', DATE_FORMAT(rto_agent_commission.rate_valid_to, '$sqlDateFormat')) as comm_valid_range"),
            DB::raw("CASE WHEN rto_student_initial_payment_transaction.payment_date > rto_agent_commission.rate_valid_to THEN 1 ELSE 0 END as is_payment_past_validity")
        ];

        $columns = [
            "invoice_no"            => dbRawL10("CONCAT('GAL-', rto_students.generated_stud_id, '-', rto_courses.course_code, '-AG', rto_student_initial_payment_details.agent_id, '-', rto_student_initial_payment_details.invoice_number, '-', rto_student_agent_commission.id) as formatted_invoice_number"),
            "payment_mode"          => 'rto_payment_mode.name',
            "paid_date"             => 'rto_student_agent_commission.paid_date',
            "due_date"              => 'rto_student_agent_commission.due_date',
            "commission_payable"    => 'rto_student_agent_commission.commission_payable',
            "gst_amount"            => 'rto_student_agent_commission.gst_amount',
            "commission_paid"       => 'rto_student_agent_commission.commission_paid',
            "refund_amount"         => 'rto_student_agent_commission.refund_amount',
            "GST_to_refund"         => 'rto_student_agent_commission.GST_to_refund',
            "is_approved"           => dbRawL10("CASE WHEN rto_student_agent_commission.is_approved THEN 'Approved' ELSE 'Not Approved' END as is_approved"),
            "remarks"               => 'rto_student_agent_commission.remarks',
        ];

        $query = StudentAgentCommission::from('rto_student_agent_commission')
            ->leftjoin('rto_agent_commission', function($join) {
                $join->on('rto_agent_commission.agent_id', '=', 'rto_student_agent_commission.agent_id');
                $join->on('rto_agent_commission.course', '=', 'rto_student_agent_commission.course_id');
            })
            ->join('rto_student_initial_payment_details', 'rto_student_initial_payment_details.invoice_number', '=', 'rto_student_agent_commission.invoice_no')
            ->join('rto_student_initial_payment_transaction', 'rto_student_initial_payment_transaction.initial_payment_detail_id', '=', 'rto_student_initial_payment_details.id')
            ->join('rto_students', 'rto_students.id', '=', 'rto_student_initial_payment_details.student_id')
            ->join('rto_courses', 'rto_courses.id', '=', 'rto_student_initial_payment_details.course_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_agent_commission.agent_id')
            ->leftjoin('rto_payment_mode', 'rto_payment_mode.id', '=', 'rto_student_agent_commission.mode')
            ->groupBy('rto_student_agent_commission.id')
            ->where($whereArr)
            ->with(['xeroInvoice'])
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        return $this->gridDataPaginationV2($query, $post, $countOnly);
        //return $query->paginate(10);
    }

    public function getAgentBonusData($request, $countOnly = false)
    {

        $post = ($request->input()) ? $request->input() : [];

        $whereArr = [
            'rto_student_agent_commission.college_id' => $post['college_id'],
            'rto_student_agent_commission.student_id' => $post['student_id'],
            'rto_student_agent_commission.student_course_id' => $post['student_course_id']
        ];

        $columnArr = [
            'ra.agency_name',
            'rto_student_agent_commission.*',
            'rto_payment_mode.name as payment_mode'
        ];

        $columns = [
            "invoice_no"            => dbRawL10("CONCAT('GAL-', rto_students.generated_stud_id, '-', rto_courses.course_code, '-AG', rto_student_initial_payment_details.agent_id, '-', rto_student_initial_payment_details.invoice_number, '-', rto_student_agent_commission.id) as formatted_invoice_number"),
            "mode"                  => 'rto_payment_mode.name',
            "paid_date"             => 'rto_student_agent_commission.paid_date',
            "bonus_amount"          => 'rto_student_agent_commission.bonus_amount',
            "gst_amount"            => 'rto_student_agent_commission.gst_amount',
            "GST_to_refund"         => 'rto_student_agent_commission.GST_to_refund',
            "remarks"               => 'rto_student_agent_commission.remarks',
        ];

        $query = StudentAgentCommission::from('rto_student_agent_commission')
            ->join('rto_student_initial_payment_details', 'rto_student_initial_payment_details.invoice_number', '=', 'rto_student_agent_commission.invoice_no')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rto_student_agent_commission.agent_id')
            ->leftjoin('rto_payment_mode', 'rto_payment_mode.id', '=', 'rto_student_agent_commission.mode')
            ->join('rto_students', 'rto_students.id', '=', 'rto_student_agent_commission.student_id')
            ->join('rto_courses', 'rto_courses.id', '=', 'rto_student_agent_commission.course_id')
            ->where($whereArr)
            ->where('rto_student_agent_commission.bonus_amount', '>', 0)
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        return $this->gridDataPaginationV2($query, $post, $countOnly);
        //return $query->paginate(10);
    }

    public function getPaymentRefundData($request, $countOnly = false)
    {

        $post = ($request->input()) ? $request->input() : [];

        $whereArr = [
            'rsipt.college_id' => $post['college_id'],
            'rsipt.student_id' => $post['student_id'],
            'rsipt.student_course_id' => $post['student_course_id']
        ];

        $columnArr = [
            'rsipt.*', 'rsac.GST_to_refund',
            'rpm.name as payment_mode_name'
        ];

        $query = StudentInitialPaymentTransaction::from('rto_student_initial_payment_transaction as rsipt')
            ->leftjoin('rto_student_initial_payment_details as rsipd', 'rsipd.id', 'rsipt.initial_payment_detail_id')
            ->leftjoin('rto_student_agent_commission as rsac', 'rsac.invoice_no', 'rsipd.invoice_number')
            ->leftjoin('rto_payment_mode as rpm', 'rpm.id', '=', 'rsipt.payment_mode')
            ->where('rsipt.amount_refund', '!=', 0)
            ->where($whereArr)
            ->select($columnArr);

        $this->gridDataSorting($query, $post);
        return $this->gridDataPagination($query, $post, $countOnly);
    }

    public function getStudentScholarshipData($request, $countOnly = false)
    {
        $post = ($request->input()) ? $request->input() : [];

        $whereArr = [
            'rsc.college_id' => $post['college_id'],
            'rsc.student_id' => $post['student_id'],
            'rsc.student_course_id' => $post['student_course_id']
        ];
        $columnArr = [
            'course.course_code', 'course.course_name', 'rsc.*'
        ];

        $query = StudentScholarship::from('rto_student_scholarship as rsc')
            ->leftjoin('rto_courses as course', 'course.id', '=', 'rsc.course_id')
            ->where($whereArr)
            ->select($columnArr)
            ->with(['xeroCreditNote']);

        $this->gridDataSorting($query, $post);
        return $this->gridDataPaginationV2($query, $post, $countOnly);
    }

    public function getMiscellaneousPaymentData($request, $countOnly = false)
    {
        $post = ($request->input()) ? $request->input() : [];

        $whereArr = [
            'rto_student_miscellaneous_payment.college_id' => $post['college_id'],
            'rto_student_miscellaneous_payment.student_id' => $post['student_id'],
            'rto_student_miscellaneous_payment.student_course_id' => $post['student_course_id']
        ];

        $columnArr = [
            'course.course_name',
            'rpm.name as payment_mode_name',
            'users.name',
            'rto_student_miscellaneous_payment.invoice_number as formatted_invoice_number',
            'rto_student_miscellaneous_payment.*',
            DB::raw("(CASE WHEN rto_student_miscellaneous_payment.payment_status !='paid' THEN 'rto_student_miscellaneous_payment.payment_status = NA' WHEN rto_student_miscellaneous_payment.payment_status !='paid' THEN 'rto_student_miscellaneous_payment.paid_on = NA' ELSE '' END) as name"),
            DB::raw("IF(rto_student_miscellaneous_payment.payment_status = 'unpaid', '-', rto_student_miscellaneous_payment.paid_on) as paid_on"),
            DB::raw("IF(rto_student_miscellaneous_payment.payment_status = 'unpaid', 'N/A', rpm.name) as payment_mode_name"),
            'rop.provider_name as oshc_provider_name'
        ];

        $query = StudentMiscellaneousPayment::withTrashed()
            ->leftjoin('rto_courses as course', 'course.id', '=', 'rto_student_miscellaneous_payment.course_id')
            ->join('rto_users as users', 'users.id', '=', 'rto_student_miscellaneous_payment.updated_by')
            ->leftjoin('rto_payment_mode as rpm', 'rpm.id', '=', 'rto_student_miscellaneous_payment.payment_mode')
            ->leftjoin('rto_oshc_providers as rop', 'rop.id', '=', 'rto_student_miscellaneous_payment.oshc_provider')
            ->where($whereArr)
            ->select($columnArr)
            ->with(['xeroInvoice']);

        $this->gridDataSorting($query, $post);
        //return $this->gridDataPaginationV2($query, $post, $countOnly);
        return $this->gridDataPaginationWithTotal($query, $post, 'Miscellaneous');
    }

//    public function getServicePaymentData($request, $countOnly=false){
//
//        $collegeId = Auth::user()->college_id;
//        $studentId = $request->student_id;
//        $post = ($request->input()) ? $request->input() : [];
//
//        $columnArr = array(
//            'rto_service_provider_facilities_setup.provider_price',
//            'rsasr.id as additionalId',
//            'rsasr.*',
//            'rsasr.id as additionalServicesId',
//            'rto_services_setup.facility_name as facilityName',
//            'rto_courses.course_code',
//            'rto_courses.course_name',
//            'rto_students.generated_stud_id',
//            'rto_setup_services_name.services_name',
//            'rto_setup_provider.company_name as providerName',
//            'rto_setup_services_category.category_name'
//        );
//
//        $servicesArr = StudentServicePayment::where('college_id', $collegeId)->where('student_id', $studentId)
//                        ->get([DB::raw('GROUP_CONCAT(rto_student_service_payment.additional_services_id SEPARATOR ",") AS additionalServicesId')])->toArray();
//
//        $servicesId = explode(',', $servicesArr[0]['additionalServicesId']);
//        $query = StudentAdditionalServiceRequest::from('rto_student_additional_service_request as rsasr')
//                ->join('rto_services_setup', 'rto_services_setup.id', '=', 'rsasr.facility_id')
//                ->join('rto_service_provider_facilities_setup', 'rto_service_provider_facilities_setup.facility_id', '=', 'rsasr.facility_id')
//                ->leftjoin('rto_setup_services_category', 'rto_setup_services_category.id', '=', 'rsasr.category_id')
//                ->leftjoin('rto_setup_services_name', 'rto_setup_services_name.id', '=', 'rsasr.service_name_id')
//                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rsasr.course_id')
//                ->leftjoin('rto_setup_provider', 'rto_setup_provider.id', '=', 'rsasr.service_provider_id')
//                ->leftjoin('rto_students', 'rto_students.id', '=', 'rsasr.student_id')
//                ->where('rsasr.student_id', '=', $studentId)
//                ->where('rsasr.college_id', '=', $collegeId)
//                ->whereNotIn('rsasr.id', $servicesId)
//                ->select($columnArr);
//
//
//        $this->gridDataSorting($query, $post);
//
//        $result = $this->gridDataPagination($query, $post, $countOnly);
//
//        return $result;
//    }
//    public function getServicePaymentData($request, $countOnly=false){
//
//        $collegeId = Auth::user()->college_id;
//        $studentId = $request->student_id;
//        $post = ($request->input()) ? $request->input() : [];
//
//        $columnArr = array(
//            'rto_service_provider_facilities_setup.provider_price',
//            'rsasr.id as additionalId',
//            'rsasr.*',
//            'rsasr.id as additionalServicesId',
//            'rto_services_setup.facility_name as facilityName',
//            'rto_courses.course_code',
//            'rto_courses.course_name',
//            'rto_students.generated_stud_id',
//            'rto_setup_services_name.services_name',
//            'rto_setup_provider.company_name as providerName',
//            'rto_setup_services_category.category_name'
//        );
//
//        $servicesArr = StudentServicePayment::where('college_id', $collegeId)->where('student_id', $studentId)
//            ->get([DB::raw('GROUP_CONCAT(rto_student_service_payment.additional_services_id SEPARATOR ",") AS additionalServicesId')])->toArray();
//
//        $servicesId = explode(',', $servicesArr[0]['additionalServicesId']);
//        $query = StudentAdditionalServiceRequest::from('rto_student_additional_service_request as rsasr')
//            ->join('rto_services_setup', 'rto_services_setup.id', '=', 'rsasr.facility_id')
//            ->join('rto_service_provider_facilities_setup', 'rto_service_provider_facilities_setup.facility_id', '=', 'rsasr.facility_id')
//            ->leftjoin('rto_setup_services_category', 'rto_setup_services_category.id', '=', 'rsasr.category_id')
//            ->leftjoin('rto_setup_services_name', 'rto_setup_services_name.id', '=', 'rsasr.service_name_id')
//            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rsasr.course_id')
//            ->leftjoin('rto_setup_provider', 'rto_setup_provider.id', '=', 'rsasr.service_provider_id')
//            ->leftjoin('rto_students', 'rto_students.id', '=', 'rsasr.student_id')
//            ->where('rsasr.student_id', '=', $studentId)
//            ->where('rsasr.college_id', '=', $collegeId)
//            ->whereNotIn('rsasr.id', $servicesId)
//            ->select($columnArr);
//
//
//        $this->gridDataSorting($query, $post);
//
//        $result = $this->gridDataPagination($query, $post, $countOnly);
//
//        return $result;
//    }
    public function getServicePaymentData($request, $countOnly=false)
    {
        $collegeId = Auth::user()->college_id;
        $studentId = $request->student_id;
        $student_course_id = $request->student_course_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'rspfs.provider_price as original_provider_price',
            'rsasr.id as additionalId',
            'rsasr.*',
            'rsasr.id as additionalServicesId',
            'rss.facility_name as facilityName',
            'rc.course_code',
            'rc.course_name',
            'rs.generated_stud_id',
            'rssn.services_name',
            'rsp.company_name as providerName',
            'rssc.category_name',
            'rto_student_service_payment.id as serviceId',
            'rto_student_service_payment.payment_status',
            'rto_student_service_payment.invoice_number as invoiceNumber',
            'rto_student_service_payment.invoice_number as formatted_invoice_number',
            'rto_student_service_payment.invoice_number',
            'rto_student_service_payment.is_reversed',
            'rto_student_service_payment.refund',
            'rto_student_service_payment.remarks',
            'rto_student_service_payment.amount',
            'rto_student_service_payment.paid_amount',
            'rto_student_service_payment.is_deduct_agent',
            'rto_student_service_payment.rebate_amount',
            'rto_student_service_payment.rebate_agent_date',
            'rto_student_service_payment.id as rssp_id'
        ];

        $whereArr = [
            'rto_student_service_payment.college_id' => $collegeId,
            'rto_student_service_payment.student_id' => $studentId,
            'rto_student_service_payment.student_course_id' => $student_course_id
        ];

        $query = StudentServicePayment::withTrashed()
            ->join('rto_student_additional_service_request as rsasr', 'rsasr.id', '=', 'rto_student_service_payment.additional_services_id')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'rto_student_service_payment.created_by')
            ->leftjoin('rto_service_provider_facilities_setup as rspfs', 'rspfs.facility_id', '=', 'rsasr.facility_id')
            ->leftjoin('rto_services_setup as rss', 'rss.id', '=', 'rsasr.facility_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rto_student_service_payment.course_id')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rto_student_service_payment.student_id')
            ->leftjoin('rto_setup_services_name as rssn', 'rssn.id', '=', 'rsasr.service_name_id')
            ->leftjoin('rto_setup_provider as rsp', 'rsp.id', '=', 'rsasr.service_provider_id')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsasr.student_id')
            ->leftjoin('rto_setup_services_category as rssc', 'rssc.id', '=', 'rsasr.category_id')
            ->where($whereArr)
            ->groupBy('rto_student_service_payment.id')
            ->select($columnArr)
            ->with(['xeroInvoice']);

        $this->gridDataSorting($query, $post);

        //$result = $this->gridDataPagination($query, $post, $countOnly);
        return $this->gridDataPaginationWithTotal($query, $post, 'Service');

        /*if(!$countOnly) {
            foreach ($result as $k => $row) {
                $result[$k]['rebate_amount'] = (!empty($row['rebate_amount'])) ? "$".$row['rebate_amount'] : '-';
            }
        }
        return $result;*/
    }

    public function getPaymentStatementData($request, $countOnly = false)
    {
        $collegeId = Auth::user()->college_id;
        $studentId = $request->student_id;
        $studCourseId = $request->student_course_id;
        $post = ($request->input()) ? $request->input() : [];

        $whereArr = [
            'college_id' => $collegeId,
            'student_id' => $studentId,
            'student_course_id' => $studCourseId
        ];

        $query1 = StudentInitialPaymentTransaction::where($whereArr)
            ->select([
                'paid_amount',
                'payment_date',
                DB::raw('(CASE WHEN remarks != "" THEN remarks ELSE "N/A" END) as description'),
                DB::raw("'Tuition Fee' as payment_name")
            ]);

        $query2 = StudentMiscellaneousPayment::where($whereArr)
            ->where('payment_status', 'paid')
            ->select([
                'paid_amount',
                'created_at as payment_date',
                DB::raw('(CASE WHEN remarks != "" THEN remarks ELSE "N/A" END) as description'),
                DB::raw("'Miscellaneous Fee' as payment_name")
            ]);

        $query3 = StudentServicePayment::join('rto_student_additional_service_request', 'rto_student_additional_service_request.id', '=', 'rto_student_service_payment.additional_services_id')
            //->where(array_merge($whereArr, ['rto_student_service_payment.payment_status' => 'paid']))
            ->where([
                'rto_student_service_payment.college_id' => $collegeId,
                'rto_student_service_payment.student_id' => $studentId,
                'rto_student_service_payment.student_course_id' => $studCourseId,
                'rto_student_service_payment.payment_status' => 'paid'
            ])
            ->select([
                'rto_student_service_payment.paid_amount',
                'rto_student_service_payment.paid_date as payment_date',
                DB::raw('(CASE WHEN rto_student_service_payment.remarks != "" THEN rto_student_service_payment.remarks ELSE "N/A" END) as description'),
                DB::raw("'Service Fee' as payment_name")
            ])
            ->groupBy('rto_student_service_payment.id');

        $mergedQuery = $query1->unionAll($query2)->union($query3);

        $this->gridDataSorting($mergedQuery, $post);

        $result = $this->gridDataPaginationV2($mergedQuery, $post, $countOnly);

        return $result;
    }

    public function addStudentInitialPaymentDetails($objStudentInitialPaymentData)
    {
        return  StudentInitialPayment::create($objStudentInitialPaymentData);
    }

    public function addStudentInitialPaymentDetailData($objStudentInitialPaymentDetailData)
    {
        return StudentInitialPaymentDetails::create($objStudentInitialPaymentDetailData);
    }

    public function addStudentInitialPaymentTranjactionDetails($objStudentInitialPaymentTranjactionData)
    {
        return StudentInitialPaymentTransaction::create($objStudentInitialPaymentTranjactionData);
    }

    public function getStudentCourse($studentId, $selectedCourseId)
    {
        return StudentCourses::from('rto_student_courses as rsc')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->where([
                'rsc.student_id' => $studentId,
                'rsc.id' => $selectedCourseId
            ])
            ->get([
                'rc.course_code',
                'rc.course_name',
                'ra.agency_name',
                'rsc.*',
                DB::raw("CONCAT(DATE_FORMAT(rsc.start_date, '%d %b %Y'),' - ',DATE_FORMAT(rsc.finish_date, '%d %b %Y')) as course_duration")
            ]);
    }

    public function checkStudentUpFrontFee($collageId, $student_id, $student_course_id, $course_id)
    {
        return StudentInitialPaymentDetails::from('rto_student_initial_payment_details')
            ->join('rto_student_initial_payment_transaction as rsipt', 'rsipt.initial_payment_detail_id', '=', 'rto_student_initial_payment_details.id')
            ->where([
                'rto_student_initial_payment_details.college_id'  =>  $collageId,
                'rto_student_initial_payment_details.student_id'  => $student_id,
                'rto_student_initial_payment_details.course_id'   => $course_id,
                'rto_student_initial_payment_details.payment_type' => 'Initial',
                'rto_student_initial_payment_details.student_course_id' => $student_course_id
            ])
            ->select('rto_student_initial_payment_details.*', 'rsipt.agent_commission_deduct')
            ->get()->toArray();
    }

    public function getPaidPaymentTransaction($request, $countOnly = false)
    {
        $collegeId = Auth::user()->college_id;
        $studentId = $request->student_id;
        $selectedCourseId = $request->student_course_id;
        $post = ($request->input()) ? $request->input() : [];

        $whereArr = [
            'rsipt.college_id' => $collegeId,
            'rsipt.student_id' => $studentId,
            'rsipt.student_course_id' => $selectedCourseId,
            //'rsipt.course_id'=> $courseId,
            'rsipt.is_delete' => 0
        ];

        $columnArr = [
            'rsipt.*',
            'rsac.GST_to_refund',
            DB::raw("DATE_FORMAT(rsipt.payment_date, '%d %b %Y') as payment_date"),
            'rpm.name as payment_mode_name',
            'rsac.commission_paid',
            'rto_student_initial_payment_details.invoice_number'
        ];

        $query = StudentInitialPaymentTransaction::from('rto_student_initial_payment_transaction as rsipt')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rsipt.updated_by')
            ->leftjoin('rto_student_initial_payment_details', 'rto_student_initial_payment_details.id', '=', 'rsipt.initial_payment_detail_id')
            ->leftjoin('rto_student_agent_commission as rsac', 'rsac.invoice_no', '=', 'rto_student_initial_payment_details.invoice_number')
            ->leftjoin('rto_payment_mode as rpm', 'rpm.id', '=', 'rsipt.payment_mode')
            ->where($whereArr)
            ->select($columnArr);

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPaginationV2($query, $post, $countOnly);

        return $result;
    }

    public function getOfferId($student_course_id)
    {
        return  StudentCourses::where('id', $student_course_id)->value('offer_id');
    }

//    public function saveStudentServicesDetails($data)
//    {
//        return StudentAdditionalServiceRequest::create($data)->id;
//    }

    public  function saveStudentAdditionalServicePayment($request)
    {
        return StudentServicePayment::create($request);
    }

    public function getStudentServicePaymentEditData($serviceId, $studentId, $isRefund = '')
    {
        $sql = StudentServicePayment::join('rto_student_additional_service_request as rsasr', 'rsasr.id', '=', 'rto_student_service_payment.additional_services_id')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'rto_student_service_payment.created_by')
            ->leftjoin('rto_setup_services_name', 'rto_setup_services_name.id', '=', 'rsasr.service_name_id')
            ->where(['rto_student_service_payment.id' => $serviceId, 'rto_student_service_payment.student_id' => $studentId]);
        if ($isRefund == true) {
            $sql->where('rto_student_service_payment.refund', '>', 0);
        }
        return $sql->get([
            'rto_student_service_payment.*',
            'rto_student_service_payment.course_id as courseId',
            'rto_student_service_payment.refund_date as refund_date',
            'rsasr.service_name_id',
            'rsasr.category_id',
            'rsasr.facility_id',
            'rsasr.service_provider_id',
            'rsasr.student_price',
            'rsasr.service_start_date',
            'rsasr.service_end_date',
            'rsasr.comment',
            'rto_setup_services_name.services_name',
        ])->toArray();
    }

    public function getStudentUpfrontFeeSchedule($studentId, $studentCourseId)
    {
        return StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rsc.student_id', '=', $studentId)
            ->where('rsc.id', '=', $studentCourseId)
            ->get(['rc.course_code', 'rc.course_name', 'rsc.*']);
    }

    public function studentPaymentDetailsGetV2($collegeId, $studentId, $studentCourseId, $invoiceNo = '')
    {
        $sql = StudentInitialPaymentDetails::withTrashed()
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rto_student_initial_payment_details.agent_id')
            ->where([
                'rto_student_initial_payment_details.college_id' => $collegeId,
                'rto_student_initial_payment_details.student_id' => $studentId,
                'rto_student_initial_payment_details.student_course_id' => $studentCourseId
            ])
            ->select(['rto_student_initial_payment_details.id as student_payment_detail_id', 'ra.agency_name', 'rto_student_initial_payment_details.*','rto_student_initial_payment_details.invoice_number as formatted_invoice_number']);

        if (!empty($invoiceNo)) {
            $sql->where('rto_student_initial_payment_details.invoice_number', $invoiceNo);
        }

        $result =  $sql->get();
         foreach($result as $key => $value){
            $result[$key]['formatted_invoice_number'] = $value['formatted_invoice_number'];
         }
         return $result;
    }

    public function getStudentPaymentTransactionDetails($transactionId)
    {
        $result =  StudentInitialPaymentTransaction::from('rto_student_initial_payment_transaction as rsipt')
            ->join('rto_student_initial_payment_details', 'rto_student_initial_payment_details.id', '=', 'rsipt.initial_payment_detail_id')
            ->join('rto_agents as ra', 'ra.id', '=', 'rto_student_initial_payment_details.agent_id')
            ->leftjoin('rto_payment_mode as rpm', 'rpm.id', '=', 'rsipt.payment_mode')
            ->where('rsipt.id', $transactionId)
            ->with('invoice')
            ->get([
                'ra.agency_name',
                'rto_student_initial_payment_details.invoiced_start_date',
                'rto_student_initial_payment_details.invoice_number',
                'rto_student_initial_payment_details.payment_type',
                'rpm.name as payment_mode_value',
                'rsipt.agent_bonus as AgentBonus',
                'rsipt.*'
            ]);
            foreach($result as $key => $res){
                $result[$key]['formatted_invoice_number'] = $res['invoice']->getFormattedInvoiceNumberAttribute();
            }
            return $result;
    }
    public function getStudentAllPaymentTransactionDetails($transactionId)
    {
        $result =  StudentInitialPaymentTransaction::from('rto_student_initial_payment_transaction as rsipt')
            ->join('rto_student_initial_payment_details', 'rto_student_initial_payment_details.id', '=', 'rsipt.initial_payment_detail_id')
            ->join('rto_agents as ra', 'ra.id', '=', 'rto_student_initial_payment_details.agent_id')
            ->leftjoin('rto_payment_mode as rpm', 'rpm.id', '=', 'rsipt.payment_mode')
            ->where('rsipt.initial_payment_detail_id', $transactionId)
            ->with('invoice')
            ->get([
                'ra.agency_name',
                'rto_student_initial_payment_details.invoiced_start_date',
                'rto_student_initial_payment_details.invoice_number',
                'rto_student_initial_payment_details.payment_type',
                'rpm.name as payment_mode_value',
                'rsipt.agent_bonus as AgentBonus',
                'rsipt.*'
            ]);
            foreach($result as $key => $res){
                $result[$key]['formatted_invoice_number'] = $res['invoice']->getFormattedInvoiceNumberAttribute();
            }
            return $result;
    }

    public function studentPaymentDetailsGetPrimary($collegeId, $scheduleId)
    {
        return StudentInitialPaymentDetails::withTrashed()
            ->join('rto_agents as ra', 'ra.id', '=', 'rto_student_initial_payment_details.agent_id')
            ->join('rto_student_initial_payment_transaction as rsipt', 'rsipt.initial_payment_detail_id', '=', 'rto_student_initial_payment_details.id')
            ->join('rto_payment_mode as rpm', 'rpm.id', '=', 'rsipt.payment_mode')
            ->where([
                'rto_student_initial_payment_details.id' => $scheduleId,
                'rto_student_initial_payment_details.college_id' => $collegeId,
                'rsipt.is_delete' => 0,
            ])
            ->get([
                'ra.agency_name',
//                DB::raw('GROUP_CONCAT(rsipt.transection_no) AS transection_no'),
//                DB::raw('GROUP_CONCAT(rsipt.receipt_no) AS receipt_no'),
//                DB::raw('SUM(rsipt.paid_amount) AS paid_amount'),
//                DB::raw('SUM(rsipt.bonus_gst) AS bonusGst'),
//                DB::raw('SUM(rsipt.agent_bonus) AS AgentBonus'),
//                DB::raw('SUM(rsipt.amount_refund) AS amount_refund'),
                'rsipt.transection_no',
                'rsipt.paid_amount',
                'rsipt.bonus_gst as bonusGst',
                'rsipt.receipt_no',
                'rsipt.agent_bonus as AgentBonus',
                'rsipt.amount_refund',
                'rpm.name as payment_mode_value',
                'rto_student_initial_payment_details.*'
            ]);
    }

    public function getStudentPdfCourseDataV2($courseId, $studentId, $studentCourseId)
    {
        return StudentCourses::from('rto_student_courses as rsc')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rs.updated_by')
            ->where('rsc.id', $studentCourseId)
            ->get([
                'rc.course_code',
                'rc.course_name',
                'rsc.coe_name',
                'rsc.start_date',
                'rsc.finish_date',
                'rsc.updated_at',
                'ru.name as updated_by'
            ]);
    }

    public function getMiscellaneousPayment($collegeId, $invoiceNumber)
    {
        return StudentMiscellaneousPayment::withTrashed()
            ->join('rto_courses as rc', 'rc.id', '=', 'rto_student_miscellaneous_payment.course_id')
            ->join('rto_users as ru', 'ru.id', '=', 'rto_student_miscellaneous_payment.updated_by')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rto_student_miscellaneous_payment.student_id')
            ->where([
                'rto_student_miscellaneous_payment.college_id'      => $collegeId,
                'rto_student_miscellaneous_payment.invoice_number'  => $invoiceNumber
            ])
            ->get([
                'rc.course_name',
                'ru.name',
                'rsd.arrange_OSHC',
                'rto_student_miscellaneous_payment.*'
            ]);
    }

    public function getStudentServicePaymentData($primaryId)
    {
        return StudentServicePayment::join('rto_student_additional_service_request as rsasr', 'rsasr.id', '=', 'rto_student_service_payment.additional_services_id')
            ->leftjoin('rto_setup_services_name', 'rto_setup_services_name.id', '=', 'rsasr.service_name_id')
            ->where('rto_student_service_payment.id', $primaryId)
            ->select('rto_setup_services_name.services_name', 'rto_student_service_payment.*','rto_student_service_payment.invoice_number as formatted_invoice_number')
            ->get()
            ->toArray();
    }

    public function studentPaymentDetailsForStudentServicePaymentGet($collegeId, $courseId, $studentId, $invoiceNo = '')
    {
        $sql = StudentServicePayment::join('rto_student_additional_service_request as rsasr', 'rsasr.id', '=', 'rto_student_service_payment.additional_services_id')
            ->leftjoin('rto_setup_services_name', 'rto_setup_services_name.id', '=', 'rsasr.service_name_id')
            ->where(['rto_student_service_payment.college_id' => $collegeId, 'rto_student_service_payment.student_id' => $studentId, 'rto_student_service_payment.course_id' => $courseId]);
        if (!empty($invoiceNo)) {
            $sql->where('rto_student_service_payment.invoice_number', '=', $invoiceNo);
        }
        return  $sql->select('rto_setup_services_name.services_name', 'rto_student_service_payment.*')->get()->toArray();
    }

    public function getStudentServicePayment($invoiceNumber)
    {
        return StudentServicePayment::join('rto_student_additional_service_request as rsasr', 'rsasr.id', '=', 'rto_student_service_payment.additional_services_id')
            ->leftjoin('rto_setup_services_name', 'rto_setup_services_name.id', '=', 'rsasr.service_name_id')
            ->where('rto_student_service_payment.college_id', Auth::user()->college_id)
            ->where('rto_student_service_payment.invoice_number', $invoiceNumber)
            ->select('rto_setup_services_name.services_name', 'rto_student_service_payment.*')
            ->get()->toArray();
    }

    public function studentMiscellaneousPaymentData($miscellaneousId, $college_id)
    {
        return StudentMiscellaneousPayment::withTrashed()
            ->leftjoin('rto_student_agent_commission as rsac', 'rsac.miscellaneous_payment_id', '=', 'rto_student_miscellaneous_payment.id')
            ->leftjoin('rto_student_profile_oshc as rspo', 'rspo.student_miscellaneous_primary_id', '=', 'rto_student_miscellaneous_payment.id')
            ->where('rto_student_miscellaneous_payment.id', $miscellaneousId)
            ->where('rto_student_miscellaneous_payment.college_id', $college_id)
            ->get([
                'rto_student_miscellaneous_payment.*',
                'rsac.id as commissionId',
                'rsac.is_agent_rebate',
                'rsac.rebate_amount',
                'rsac.rebate_date',
                'rspo.card_arrival_date',
                'rspo.card_pickup_date',
            ])->toArray();
    }

    public function studentServicePaymentData($miscellaneousId, $college_id)
    {
        return StudentMiscellaneousPayment::from('rto_student_miscellaneous_payment')
            ->leftjoin('rto_student_agent_commission as rsac', 'rsac.miscellaneous_payment_id', '=', 'rto_student_miscellaneous_payment.id')
            ->where('rto_student_miscellaneous_payment.id', $miscellaneousId)
            ->where('rto_student_miscellaneous_payment.college_id', $college_id)
            ->get([
                'rto_student_miscellaneous_payment.*',
                'rsac.id as commissionId',
                'rsac.is_agent_rebate',
                'rsac.rebate_amount',
                'rsac.rebate_date',
            ])->toArray();
    }

    public function deletePaymentDetailsByTransaction($collegeId, $paymentDetailId)
    {
        $getPaymentTransaction = StudentInitialPaymentTransaction::where('initial_payment_detail_id', $paymentDetailId)->get(['id']);
        $creditAmount = 0;
        $scholarshipAmount = 0;
        $arrInvoiceNo = array();
        $arrTransactionNo = array();
        foreach ($getPaymentTransaction as  $rowTransaction) {
            $objDeletePaymentTransaction = StudentInitialPaymentTransaction::find($rowTransaction->id);
            if($objDeletePaymentTransaction->reversed == 0){
                $creditAmount += $objDeletePaymentTransaction->student_credit;
                $scholarshipAmount += $objDeletePaymentTransaction->scholarship;
            }
            //$arrTransactionNo[] = $objDeletePaymentTransaction->transection_no;
            $paymentDetailId = $objDeletePaymentTransaction->initial_payment_detail_id;
            $getPaymentDetail = StudentInitialPaymentDetails::find($paymentDetailId);
            if($getPaymentDetail){
                $arrInvoiceNo[] = $getPaymentDetail->invoice_number;
            }

            $objDeletePaymentTransaction->delete();
        }

        if (!empty($getPaymentTransaction[0])) {
            return array('arrTransactionNo' => $arrTransactionNo, 'arrInvoiceNo' => $arrInvoiceNo, 'scholarshipAmount' => $scholarshipAmount, 'creditAmount' => $creditAmount);
        } else {
            return 1;
        }
    }

    public function revertCreditAmount($collegeId, $studentId, $creditAmount)
    {
        if ($creditAmount > 0) {
            StudentInitialPayment::where([
                'college_id' => $collegeId,
                'student_id' => $studentId
            ])->increment('student_credit', $creditAmount);
        }

        /*if($creditAmount > 0){
            $whereArr = ['college_id' => $collegeId, 'student_id' => $studentId];
            $studentInitialPayment = StudentInitialPayment::where($whereArr)->first();
            if($studentInitialPayment){
                $totalCredit = $studentInitialPayment->student_credit;

                $newCreditAmount = $totalCredit + $creditAmount;
                StudentInitialPayment::where($whereArr)->update([
                    'student_credit' => $newCreditAmount
                ]);
            }
        }*/
    }

    public function deletePaidScholarshipAmount($collegeId, $studentId, $courseId, $scholarshipAmount)
    {
        $getScholarshipAmount = StudentScholarship::where([
            'college_id' => $collegeId,
            'student_id' => $studentId,
            'course_id'  => $courseId
        ])
            ->where('used_scholarship_amount', '!=', 0)
            ->get(['id', 'used_scholarship_amount']);

        if ($getScholarshipAmount->count() > 0) {
            $remianingScholarship = $scholarshipAmount;
            for ($i = 0; $i < count($getScholarshipAmount); $i++) {
                $objUpdateScholarship = StudentScholarship::find($getScholarshipAmount[$i]->id);
                $remianingScholarship = $getScholarshipAmount[$i]->used_scholarship_amount - abs($remianingScholarship);
                if ($remianingScholarship > 0) {
                    $objUpdateScholarship->used_scholarship_amount = $remianingScholarship;
                    $objUpdateScholarship->is_transfer = 0;
                    $objUpdateScholarship->save();
                    break;
                } else {
                    $objUpdateScholarship->used_scholarship_amount = 0;
                    $objUpdateScholarship->is_transfer = 0;
                    $objUpdateScholarship->save();
                }
            }
        }
    }

    public function getPaymentTransactionData($paymentTransactionId)
    {
        return StudentInitialPaymentTransaction::from('rto_student_initial_payment_transaction')
            ->leftjoin('rto_student_initial_payment_details', 'rto_student_initial_payment_details.id', '=', 'rto_student_initial_payment_transaction.initial_payment_detail_id')
            ->leftjoin('rto_student_agent_commission', 'rto_student_agent_commission.invoice_no', '=', 'rto_student_initial_payment_details.invoice_number')
            ->where('rto_student_initial_payment_transaction.id', $paymentTransactionId)
            ->get([
                'rto_student_initial_payment_transaction.*',
                'rto_student_initial_payment_details.commission_value',
                'rto_student_initial_payment_details.invoiced_start_date',
                'rto_student_initial_payment_details.agent_id',
                'rto_student_initial_payment_details.due_date',
                'rto_student_initial_payment_details.GST',
                'rto_student_initial_payment_details.id as edit_id',
                'rto_student_initial_payment_details.upfront_fee_to_pay',
                'rto_student_agent_commission.commission_payable',
                'rto_student_agent_commission.paid_date',
                'rto_student_agent_commission.commission_paid',
                'rto_student_agent_commission.mode'
            ]);
    }

    public function listStudentMiscellaneousPaymentPDF($collegeId, $studentId, $courseId, $miscId)
    {
        return StudentMiscellaneousPayment::from('rto_student_miscellaneous_payment')
            ->join('rto_courses as rc', 'rc.id', '=', 'rto_student_miscellaneous_payment.course_id')
            ->join('rto_users as ru', 'ru.id', '=', 'rto_student_miscellaneous_payment.updated_by')
            ->where([
                'rto_student_miscellaneous_payment.college_id'   => $collegeId,
                'rto_student_miscellaneous_payment.student_id'   => $studentId,
                'rto_student_miscellaneous_payment.course_id'    => $courseId,
                'rto_student_miscellaneous_payment.id'           => $miscId
            ])
            ->where('rto_student_miscellaneous_payment.refund', '>', '0')
            ->get(['rc.course_name', 'ru.name', 'rto_student_miscellaneous_payment.*'])
            ->first();
    }

    public function updateMiscellaneousTransactionReasonAndReceipt($reason, $receiptNo, $amount)
    {
        $depositedAmount = StudentInitialPaymentTransaction::select('deposited_amount', 'remarks')->where('receipt_no', $receiptNo)->first();
        if (isset($depositedAmount)) {
            if ($depositedAmount['remarks'] != '') {
                $reason = $depositedAmount['remarks'] . ',' . $reason;
            }
            $newDepositedAmount = (float)$depositedAmount['deposited_amount'] - (float)$amount;
            return StudentInitialPaymentTransaction::where('receipt_no', $receiptNo)->update(['remarks' => $reason, 'deposited_amount' => $newDepositedAmount]);
        }
    }

    public function getPaymentTransactionDetails($transactionId)
    {
        return StudentInitialPaymentTransaction::from('rto_student_initial_payment_transaction')
            ->join('rto_student_initial_payment_details', 'rto_student_initial_payment_details.id', '=', 'rto_student_initial_payment_transaction.initial_payment_detail_id')
            ->leftjoin('rto_student_agent_commission', 'rto_student_agent_commission.invoice_no', '=', 'rto_student_initial_payment_details.invoice_number')
            ->where('rto_student_initial_payment_transaction.id', $transactionId)
            ->get([
                'rto_student_initial_payment_details.upfront_fee_to_pay',
                'rto_student_initial_payment_transaction.*',
                'rto_student_agent_commission.commission_paid as paidCommission'
            ])
            ->toArray();
    }

    public function getStudentAgentList($studentCourseId)
    {
        return StudentCourses::join('rto_agents as ra', 'ra.id', '=', 'rto_student_courses.agent_id')
            ->where('rto_student_courses.id', $studentCourseId)
            ->groupBy('rto_student_courses.agent_id')
            ->select('ra.primary_email as Name', 'ra.id as Id')
            ->get()->toArray();
    }

    public function getEmailTemplateInfo($collegeId, $emailContentId)
    {
        return EmailTemplate::from('rto_email_template as ret')
            ->leftjoin('rto_email_template_documents as doc', 'doc.email_template_id', '=', 'ret.id')
            ->where('ret.college_id', $collegeId)
            ->where('ret.id', $emailContentId)
            ->where('ret.status', 1)
            ->select('ret.id', 'ret.email_subject', 'ret.content', DB::raw('GROUP_CONCAT(doc.file SEPARATOR "<br/> ") AS file_name'))
            ->get()
            ->ToArray();
    }

    public function getStudentCoursesById($studentCourseId, $studentIDs)
    {
        return Student::with(['studentCourses' => function ($query) use ($studentCourseId) {
            $query->where('id', $studentCourseId);
        }])->whereIn('id', $studentIDs)->get();
    }

    public function getStudentCoursesByCourse($courseId, $studentIDs)
    {
        return Student::with(['studentCourses' => function ($query) use ($courseId) {
            $query->where('course_id', '=', $courseId);
        }])->whereIn('id', $studentIDs)->get();
    }

    public function getStudentUpfrontFeeScheduleV2($studentId, $studentCourseId)
    {
        return StudentCourses::join('rto_courses as rc', 'rc.id', '=', 'rto_student_courses.course_id')
            ->where(['rto_student_courses.student_id' => $studentId, 'rto_student_courses.id' => $studentCourseId])
            ->get(['rc.course_code', 'rc.course_name', 'rto_student_courses.*'])->toArray();
    }

    public function getInitialPaymentTransactionDetail($transaction_number)
    {
        return StudentInitialPaymentTransaction::from('rto_student_initial_payment_transaction as rsipt')
            ->leftjoin('rto_student_initial_payment_details', 'rto_student_initial_payment_details.id', 'rsipt.initial_payment_detail_id')
            ->select('rto_student_initial_payment_details.commission_value', 'rto_student_initial_payment_details.GST', 'rsipt.*')
            ->where(['rsipt.transection_no' => $transaction_number])
            ->get()
            ->toArray();
    }

    public function getPaymentModeData($collegeId)
    {
        $result = array();
        $arrResult = PaymentMode::where('college_id', '=', $collegeId)->orderBy('id', 'DESC')->get()->toArray();
        for ($i = 0; $i < count($arrResult); $i++) {
            $result[$arrResult[$i]["id"]] = $arrResult[$i]["name"];
        }
        $selectArray[""] = "--Select Mode--";
        return $selectArray + $result;
    }

    public function getInitialPaymentTransactionV2($request, $collegeId, $studentId, $courseId,  $student_course_id, $countOnly = false)
    {
        $post = ($request->input()) ? $request->input() : [];
        $query = StudentInitialPaymentTransaction::from('rto_student_initial_payment_transaction as rsipt')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rsipt.updated_by')
            ->leftjoin('rto_student_initial_payment_details', 'rto_student_initial_payment_details.id', '=', 'rsipt.initial_payment_detail_id')
            ->leftjoin('rto_payment_mode as rpm', 'rpm.id', '=', 'rto_student_initial_payment_details.payment_mode')
            ->leftjoin('rto_student_agent_commission as rsac', 'rsac.invoice_no', '=', 'rto_student_initial_payment_details.invoice_number')
            ->where('rsipt.college_id', '=', $collegeId)
            ->where('rsipt.student_id', '=', $studentId)
            ->where('rsipt.course_id', '=', $courseId)
            ->where('rsipt.student_course_id', '=', $student_course_id)
            ->where('rsipt.is_delete', '=', 0)
            ->select('rsipt.*', 'rpm.name as payment_modes', 'rsac.is_approved', 'rsac.is_process', 'ru.name as userName', 'rto_student_initial_payment_details.invoice_number', DB::raw("DATE_FORMAT(bank_deposit_date, '%d-%m-%Y') as bank_deposit_date"));

        $this->gridDataSorting($query, $post);
        return $this->gridDataPagination($query, $post, $countOnly);
    }

    public function getAppliedStudentCourseV2($courseId, $studentId, $student_course_id)
    {
        return StudentCourses::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->where('rto_student_courses.student_id', $studentId)
            ->where('rto_student_courses.course_id', $courseId)
            ->where('rto_student_courses.id', $student_course_id)
            ->select('rto_campus.name as campus_name', 'rto_courses.cricos_code', 'rto_courses.course_code', 'rto_courses.work_placement_hour', 'rto_courses.work_placement', 'rto_courses.course_name', 'rto_student_courses.*', 'rto_agents.agency_name', 'rto_agents.office_address', 'rto_agents.office_city', 'rto_agents.office_state', 'rto_agents.office_postcode')
            ->get();
    }

    public function getAccountPayment($student_course_id, $student_id)
    {
        return StudentCourses::join('rto_agents as rc', 'rc.id', '=', 'rto_student_courses.agent_id')
            ->where(['rto_student_courses.id' => $student_course_id, 'rto_student_courses.student_id' => $student_id])
            ->get(['rc.agency_name', 'rc.id as agent_id', 'rto_student_courses.*'])->toArray();
    }

    public function saveStudentInitialPaymentV2($request)
    {
        return StudentInitialPayment::firstOrCreate(['student_id' => $request['student_id'], 'course_id' => $request['course_id'], 'student_course_id' => $request['student_course_id'], 'college_id' => $request['college_id']], $request);
    }

    public function studentPaymentDetailsGetV3($collegeId, $student_course_id, $studentId, $invoiceNo = '', $isData = false)
    {
        $query = StudentInitialPaymentDetails::from('rto_student_initial_payment_details')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rto_student_initial_payment_details.agent_id')
            ->where('rto_student_initial_payment_details.college_id', $collegeId)
            ->where('rto_student_initial_payment_details.student_id', $studentId);

        if (!empty($student_course_id)) {
            $query->where('rto_student_initial_payment_details.student_course_id', $student_course_id);
        }

        if (!empty($invoiceNo)) {
            $query->where('rto_student_initial_payment_details.invoice_number', $invoiceNo);
        }
        if ($isData) {
            return $query->select('rto_student_initial_payment_details.*')->get()->toArray();
        }
        return $query->sum('rto_student_initial_payment_details.upfront_fee_to_pay');
    }
    public function generateStudentInitialPaymentDetails($request)
    {
        $res = StudentInitialPaymentDetails::create($request);
        if($res){
            $this->editTransactionNumber();
            $this->updateInvoiceNumber($request['invoice_number']);
        }
    }
    public function getPaymentDetails($collegeId, $detailId)
    {
        $dataArray = StudentInitialPaymentDetails::join('rto_student_courses', 'rto_student_courses.id', '=', 'rto_student_initial_payment_details.student_course_id')
            ->leftjoin('rto_agents as agent', 'agent.id', '=', 'rto_student_initial_payment_details.agent_id')
            ->where(['rto_student_initial_payment_details.id' => $detailId, 'rto_student_initial_payment_details.college_id' => $collegeId])
            ->with(['xeroInvoice'])
            ->get([
                'agent.agency_name',
                'agent.primary_email',
                'agent.alertnet_email',
                'rto_student_courses.start_date as course_start_date',
                'rto_student_courses.finish_date as course_finish_date',
                'rto_student_initial_payment_details.*',
                DB::raw("CASE
                                WHEN invoiced_start_date < due_date THEN CONCAT(DATE_FORMAT(invoiced_start_date, '%d %b %Y'), ' - ', DATE_FORMAT(DATE_SUB(due_date, INTERVAL 1 DAY), '%d %b %Y'))
                                ELSE CONCAT(DATE_FORMAT(invoiced_start_date, '%d %b %Y'), ' - ', DATE_FORMAT(due_date, '%d %b %Y'))
                              END as fee_duration")
                //DB::raw("CONCAT(DATE_FORMAT(invoiced_start_date, '%d %b %Y'),' - ',DATE_FORMAT(due_date, '%d %b %Y')) as fee_duration")
            ])->toArray();
        $dataArray[0]['xero_connect'] = Xero::isConnected() ? 1 : 0;
        return $dataArray;
    }

    public function gridDataPaginationWithTotal($query, $post, $type='')
    {
        $clonedQuery = clone $query;
        if($type == 'Schedule'){
            $totals = $clonedQuery->selectRaw("
                SUM(rto_student_initial_payment_details.upfront_fee_pay) as total_paid,
                SUM(rto_student_initial_payment_details.upfront_fee_to_pay - rto_student_initial_payment_details.upfront_fee_pay - rto_student_initial_payment_details.invoice_credit) as total_due,
                SUM(
                    CASE
                        WHEN CURDATE() > rto_student_initial_payment_details.due_date
                        THEN (rto_student_initial_payment_details.upfront_fee_to_pay - rto_student_initial_payment_details.upfront_fee_pay - rto_student_initial_payment_details.invoice_credit)
                        ELSE 0
                    END
                ) as total_overdue
            ")->first();

        } elseif ($type == 'Miscellaneous'){
            $totals = $clonedQuery->selectRaw("
                SUM(rto_student_miscellaneous_payment.paid_amount) as total_paid,
                SUM(rto_student_miscellaneous_payment.amount - rto_student_miscellaneous_payment.paid_amount) as total_due,
                SUM(
                    CASE
                        WHEN CURDATE() > rto_student_miscellaneous_payment.due_date THEN (rto_student_miscellaneous_payment.amount - rto_student_miscellaneous_payment.paid_amount)
                        ELSE 0
                    END
                ) as total_overdue
            ")->first();

        } elseif ($type == 'Service'){
            $totals = $clonedQuery->selectRaw("
                SUM(rto_student_service_payment.paid_amount) as total_paid,
                SUM(rto_student_service_payment.amount - rto_student_service_payment.paid_amount) as total_due,
                SUM(
                    CASE
                        WHEN CURDATE() > rto_student_service_payment.due_date THEN (rto_student_service_payment.amount - rto_student_service_payment.paid_amount)
                        ELSE 0
                    END
                ) as total_overdue
            ")->first();
        }


        // Fetch paginated data
        $result = $query->paginate($post['take']);

        // Return response
        return [
            'total'         => $result->total(),
            'data'          => @$result->toArray()['data'],
            'amountData'    => [
                'total_paid'    => $totals->total_paid ?? 0,
                'total_due'     => $totals->total_due ?? 0,
                'total_overdue' => $totals->total_overdue ?? 0
            ]
        ];
    }
}
