<?php

namespace App\ProcessTask\StudentProfile\CourseStatus;

use App\Exceptions\ApplicationException;
use App\Model\v2\Student;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentCourseStatusHistory;
use App\Model\v2\StudentInitialPaymentDetails;
use App\Traits\SendNotificationTrait;
use Closure;
use Illuminate\Support\Facades\DB;
use Notifications\Types\DTOs\StudentCourseCompletionNotificationDTO;
use Notifications\Types\DTOs\StudentWithdrawalNotificationDTO;
use Notifications\Types\NotificationType;

class UpdateStatusWithLog
{
    use SendNotificationTrait;

    public function handle($data, Closure $next)
    {
        $res = [];
        DB::beginTransaction();
        try {
            $loginData = (object) [];
            // check this process runing from profile or command
            if (auth()->user()) {
                $loginData = auth()->user();
            } else {
                // $loginData->college_id = $data['college_id'];
                $loginData->id = $data['created_by'];
            }

            $newStatus = $data['newStatus'];
            $resData = StudentCourses::with('student', 'course')->where('id', $data['studCourseId'])->first();
            // $resData = StudentCourses::find($data['studCourseId']);

            if ($resData) {
                $oldStatus = $resData->status;
                $resData->status = $newStatus;
                $isUpdate = $resData->save();

                if ($isUpdate) {
                    $dataArr = [
                        // 'college_id'    => $loginData->college_id,
                        'college_id' => $resData->student->college_id,
                        'student_id' => $resData->student_id,
                        'course_id' => $data['studCourseId'], // student Course Id.
                        'status_from' => empty($oldStatus) ? 'New Application Request' : $oldStatus,
                        'status_to' => $newStatus,
                        'today_date' => date('d/m/Y h:i:s A'),
                        'status' => ($oldStatus == '') ? ("Inserted record with status : $newStatus") : ("Convert from $oldStatus to $newStatus"),
                        'created_at' => date('Y-m-d H:i:s'),
                        'created_by' => $loginData->id,
                        'updated_by' => $loginData->id,
                    ];

                    if ($this->shouldTriggerCancellation($newStatus, $oldStatus)) {
                        $dataArr['is_cancel'] = 1;
                        $dataArr['cancel_reason'] = $data['cancelledReason'];
                    }
                    // $res = StudentCourseStatusHistory::insert($dataArr);
                    $res = StudentCourseStatusHistory::create($dataArr);

                    $student = Student::with('associatedUserAccount')->find($resData->student_id);
                    $studentUser = $student->associatedUserAccount;
                    if ($newStatus == 'Withdrawn') {

                        // Send STUDENT_WITHDRAWAL notification to student

                        $tempArr = StudentInitialPaymentDetails::where(['student_course_id' => $data['studCourseId']])
                            ->select([
                                DB::raw('(SUM(CASE WHEN due_date < NOW() THEN upfront_fee_to_pay ELSE 0 END)) as upfront_fee_to_pay_due'),
                                DB::raw('(SUM(CASE WHEN due_date < NOW() THEN upfront_fee_pay ELSE 0 END)) as upfront_fee_pay_due'),
                                DB::raw('(SUM(upfront_fee_to_pay)) as upfront_fee_to_pay'),
                                DB::raw('(SUM(upfront_fee_pay)) as upfront_fee_pay'),
                            ])
                            ->first();
                        $totalCoursePaidAmount = (isset($tempArr->upfront_fee_pay)) ? $tempArr->upfront_fee_pay : 0;

                        $totalRemainingFee = $resData->course_fee - $totalCoursePaidAmount;

                        $notificationData = StudentWithdrawalNotificationDTO::LazyFromArray([
                            'courseId' => $resData->course_id,
                            'courseName' => $resData->course->course_name,
                            'withdrawnAt' => now()->format('d M Y'),
                            'amount' => $totalRemainingFee,
                            'enrolmentUrl' => '',
                        ]);
                        $this->sendNotification(
                            $studentUser,
                            NotificationType::STUDENT_WITHDRAWAL,
                            $notificationData
                        );
                    }

                    if ($newStatus == 'Completed') {

                        // Send STUDENT_COURSE_COMPLETION notification to student

                        $notificationData = StudentCourseCompletionNotificationDTO::LazyFromArray([
                            'courseId' => $resData->course_id,
                            'courseName' => $resData->course->course_name,
                            'certificateIssueDate' => now()->format('d M Y'),
                            'graduationDetailsUrl' => '',
                            'furtherStudyUrl' => '',
                        ]);

                        $this->sendNotification(
                            $studentUser, // Send notification to the student
                            NotificationType::STUDENT_COURSE_COMPLETION,
                            $notificationData,
                            true
                        );
                    }
                }
            }
            $studentDetail = Student::find($resData->student_id);
            $studentDetail->updated_at = date('Y-m-d H:i:s');
            $studentDetail->save();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }

        return $next(['data' => $data, 'res' => $res]);
        // return $next($data);
    }

    public function shouldTriggerCancellation($newStatus, $oldStatus)
    {
        $triggerStatuses = [
            StudentCourses::STATUS_CANCELLED,
            StudentCourses::STATUS_DEFERRED,
            StudentCourses::STATUS_WITHDRAWN,
            StudentCourses::STATUS_SUSPENDED,
        ];

        return ! in_array($oldStatus, $triggerStatuses) && in_array($newStatus, $triggerStatuses);
    }
}
