<div class="tw-course-grid grid grid-cols-12 {{ isset($isAjax) && $isAjax ? '' : 'hidden'}}">
    <div class="col-span-12 bg-gray-100">
        <div class="grid grid-cols-1 md:flex items-center px-6 md:px-8 py-4 justify-between flex-wrap">
            <div class="course-list justify-start">
                <select class="header_course_list" style="display: none;"></select>
            </div>
            <x-v2.skeleton.buttongroup count="4" />
            <x-v2.templates.profile-buttons>
                @include('v2.sadmin.student.partials.course-action-dropdown')
            </x-v2.templates.profile-buttons>
        </div>
        <div class="w-full px-6 md:px-8 pb-4 bg-gray-100 grid grid-cols-12 gap-5">
            <div class="w-full col-span-12">
                <div id="studCourseTab" class="w-full">
                    <x-v2.skeleton.card section="course" height="275px" data-id="studCourseTab" />
                </div>
                <div id="currentCourse" class="currentCourseSummary getGridOfCurrentCourseSummary mt-5">
                    <x-v2.skeleton.card section="strips" height="275px" data-id="studCourseTab" />
                </div>
                <div id="resultList" class="mt-5">
                </div>
                <div
                    class="flex flex-col space-y-6 items-start justify-start px-4 pt-6 bg-white border rounded-md border-gray-200">
                    <div class="inline-flex space-x-6 items-center justify-between w-full">
                        <div class="inline-flex space-x-1 items-center justify-start">
                            <p class="text-lg font-medium leading-normal text-gray-900">Course Progress</p>
                        </div>
                    </div>
                    <div class="flex w-full" id="studCourseProgress"></div>
                    <div class="w-full">
                        {{-- <div class="flex" id="courseTimeline"></div> --}}
                        <div class="tw-h-timeline">
                            <div class="horizontal-timeline" id="courseTimeline">
                            </div>
                        </div>
                    </div>
                    <div class="flex w-full overflow-x-auto">
                        <div id="unitList" class="tw-table tw-table__borderless tw-table__header--borderless"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="unitSyncToMoodleFromCourseModal" style="display: none;"></div>

<div id="enrollStudentSyncToMoodleFromCourseModal" style="display: none;"></div>

{{-- TEMPLATE: course-progress --}}
@include('v2.sadmin.student.templates.common.course-details')
@include('v2.sadmin.student.templates.common.course-progress')
@include('v2.sadmin.student.templates.course.current-course-summary')
@include('v2.sadmin.student.templates.course.course-summary-detail')
@include('v2.sadmin.student.templates.course.current-course-history')
@include('v2.sadmin.student.templates.course.academic-summary')
@include('v2.sadmin.student.templates.course.enroll-subject')
@include('v2.sadmin.student.templates.course.training-plan-action')
@include('v2.sadmin.student.templates.course.get-document')
@include('v2.sadmin.student.templates.course.preview-document')
@include('v2.sadmin.student.templates.course.course-timeline')