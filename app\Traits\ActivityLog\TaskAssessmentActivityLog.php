<?php

namespace App\Traits\ActivityLog;


use Domains\Moodle\Facades\Moodle;

trait TaskAssessmentActivityLog
{
    public function getCustomDescriptionForEvent($eventName,$detailData,$activity): string
    {
        $userName = (auth()->user())?auth()->user()->name:$this->getMoodleUserName();
        $routeName = (request()->route())?request()->route()->getName():'';

        $action = request()->input('action');
        
        if(isset($detailData)){
            $studentName = $detailData['studentName'];
            $taskName = $detailData['assessmentTask'];
            $studentId = $detailData['studentId'];
        }
        if ($routeName === 'task-results-entry-ajaxAction' && $action) {
            switch ($action) {
                case 'saveStudentList':
                    return "Task {$taskName} been {$eventName} successfully for {$studentName} ({$studentId})  by {$userName}";
                case 'lockUnlockAssessment':
                    if(isset($activity->properties['attributes']['is_locked']) && $activity->properties['attributes']['is_locked'] == 0){
                        return "Task {$taskName} been unlocked for {$studentName} ({$studentId})  by {$userName}";
                    } elseif(isset($activity->properties['attributes']['is_locked']) && $activity->properties['attributes']['is_locked'] == 1){
                        return "Task {$taskName} been locked for {$studentName} ({$studentId}) by {$userName}";
                    }
                case 'approveAssessment':
                    if(isset($activity->properties['attributes']['is_approved'])){
                        if($activity->properties['attributes']['is_approved'] == 0){
                            return "Task {$taskName} been unapproved for {$studentName}({$studentId})  by {$userName}";
                        } elseif($activity->properties['attributes']['is_approved'] == 1){
                            return "Task {$taskName} been approved for {$studentName}({$studentId}) by {$userName}";
                        }
                    }
                case 'deleteStudentTasks':
                    return "Task been reset by {$userName}";

            }
        }elseif ($routeName === 'teacher-results-entry-ajaxAction' && $action) {
            switch ($action) {
                case 'lockUnlockAssessment':
                    $attributes = $activity->properties['attributes'] ?? [];
                    if (array_key_exists('is_locked', $attributes)) {
                        if ($attributes['is_locked'] == 0) {
                            return "Task {$taskName} has been unlocked for {$studentName} ({$studentId}) by {$userName}";
                        } elseif ($attributes['is_locked'] == 1) {
                            return "Task {$taskName} has been locked for {$studentName} ({$studentId}) by {$userName}";
                        }
                    }
                    break;

                case 'approveAssessment':
                    $attributes = $activity->properties['attributes'] ?? [];

                    if (isset($attributes['is_approved'])) {
                        return "Task {$taskName} has been approved for {$studentName} ({$studentId}) by {$userName}";
                    }
                    break;
            }
        }else if ($routeName === 'update-student-result-semester-data') {
            return "{$studentName}'s ({$studentId}) task ({$taskName}) {$eventName} by {$userName}.";

        }else{
            if ($eventName == 'updated') {
                if(isset($activity->properties['attributes']['is_locked'])){
                    if($activity->properties['attributes']['is_locked'] == 0){
                        $activity->description = 'Task Result has been Unlocked.';
                    } elseif($activity->properties['attributes']['is_locked'] == 1){
                        $activity->description = 'Task Result has been Locked.';
                    }
                }
                if(isset($activity->properties['attributes']['is_approved'])){
                    if($activity->properties['attributes']['is_approved'] == 0){
                        $activity->description = 'Task Result has been Unapproved.';
                    } elseif($activity->properties['attributes']['is_approved'] == 1){
                        $activity->description = 'Task Result has been Approved.';
                    }
                }
            }elseif ($eventName == 'deleted') {
                $activity->description = 'Task Result has been Reset.';
            }
        }
        return "{$studentName}'s ({$studentId}) task ({$taskName}) {$eventName} by {$userName}.";
    }

    public function getCustomLogName(): string
    {
        return " Student Enrollment";
    }

    public function getMoodleUserName()
    {
        $loginMoodleUserId = request()->input('userid');
        $moodleUser = Moodle::users()->getByField('id', $loginMoodleUserId)->first();

        return $moodleUser
            ? "MoodleUser - " . ucfirst($moodleUser->fullname) . " (" . $moodleUser->email . ")"
            : "MoodleUser";
    }
}
