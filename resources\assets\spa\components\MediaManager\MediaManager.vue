<template lang="">
    <div class="p-4 md:p-6">
        <Card :pt="{ root: 'p-0 md:p-0', header: 'p-0' }">
            <template #header>
                <div
                    class="flex flex-col items-start justify-between gap-4 p-4 md:items-center md:px-6 md:pb-4 md:pt-6 lg:flex-row"
                >
                    <div
                        class="flex flex-col gap-4 md:flex-row md:items-center md:gap-6"
                    >
                        <IconInput
                            v-model.lazy="keyword"
                            :debounce="300"
                            :autocomplete="'off'"
                            :pt="{ root: 'w-64 md:w-80 h-10' }"
                        />
                        <div class="flex items-center gap-2">
                            <Switch
                                @change="handleShowBookmark"
                                :checked="isShowBookMark"
                            />
                            <span class="text-gray-700">Show Bookmarked</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-end gap-3 lg:gap-4">
                        <template v-for="(button, key) in actionButtons">
                            <Button
                                :variant="button.variant || 'secondary'"
                                class="font-medium"
                                :class="
                                    button.label == 'Delete Files'
                                        ? 'border-red-400 [&_svg]:text-red-400'
                                        : ''
                                "
                                :key="key"
                                @click="handleHeaderAction(key)"
                                v-if="button.visible"
                            >
                                <span
                                    :class="
                                        button.variant === 'primary'
                                            ? 'text-white'
                                            : 'text-gray-400'
                                    "
                                >
                                    <file-icon
                                        :name="button.icon"
                                        v-if="button.icon"
                                    />
                                </span>
                                <span>{{ button.label }}</span></Button
                            >
                        </template>
                    </div>
                </div>
                <Breadcrumb
                    :breadcrumbs="breadcrumbs"
                    :show-home="true"
                    @breadcrumbClick="onBreadcrumbClick"
                    @homeClick="onHomeClick"
                />
            </template>
            <template #content>
                <div
                    class="grid h-[80vh]"
                    :class="showPreview ? ' grid-cols-2' : 'grid-cols-1'"
                >
                    <MediaGrid
                        :data="files"
                        :columns="columns"
                        :loginType="loginType"
                        :currentTab="currentTab"
                        :selectedRowIndex="selectedItemIndex"
                        @dataItemAction="handleGridActions"
                        @selectionChange="handleSelectItems"
                        @sortChange="handleSortChange"
                    />
                    <Transition name="slide-fade">
                        <div
                            class="preview-container fixed inset-0 top-14 z-50 col-span-2 h-full w-full overflow-hidden border-l border-gray-200 md:relative md:top-0 md:col-span-1"
                            v-if="showPreview"
                        >
                            <div
                                class="sticky top-0 flex h-10 items-center justify-between border-b border-gray-200 bg-white px-4"
                            >
                                <div class="flex items-center gap-6">
                                    <Button
                                        variant="icon"
                                        size="auto"
                                        @click="handleNavigation('prev')"
                                    >
                                        <file-icon name="prev-arrow" />
                                    </Button>
                                    <Button
                                        variant="icon"
                                        size="auto"
                                        class="-scale-x-100"
                                        @click="handleNavigation('next')"
                                    >
                                        <file-icon name="prev-arrow" />
                                    </Button>
                                </div>
                                <div
                                    class="flex items-center justify-end gap-2"
                                >
                                    <div
                                        class="flex max-w-80 items-center gap-1"
                                    >
                                        <icon
                                            v-if="!isDirectory && !extension"
                                            :name="'invalid_file_type'"
                                            :width="'18'"
                                            :height="'18'"
                                        />
                                        <file-icon
                                            v-else
                                            :name="getIconName"
                                            class="flex-10"
                                        />
                                        <span class="text-grayk-800 truncate">{{
                                            dataItem.name
                                        }}</span>
                                    </div>
                                    <Button
                                        variant="icon"
                                        size="auto"
                                        class="block -scale-x-100 md:hidden"
                                        @click="handlePreviewClose"
                                    >
                                        <icon name="cross" />
                                    </Button>
                                </div>
                            </div>
                            <button
                                class="sticky left-0 top-1/2 z-10 flex h-16 w-8 -translate-y-1/2 items-center justify-center rounded-r-xl bg-gradient-to-r from-[#1E93FF] to-[#06B6D4] transition-all hover:bg-gradient-to-t md:w-6"
                                @click="handlePreviewClose"
                            >
                                <file-icon name="chevron" />
                            </button>
                            <div
                                class="view-document-custom-pdf -mt-16 h-full overflow-y-auto bg-gray-50"
                            >
                                <div class="h-full" v-if="documentLoading">
                                    <Spinner />
                                </div>
                                <template v-else>
                                    <div
                                        v-if="documentError"
                                        class="flex h-full flex-col items-center justify-center gap-4 p-4 text-center"
                                    >
                                        <div
                                            class="flex flex-col items-center justify-center gap-2"
                                        >
                                            <icon
                                                v-if="
                                                    !isDirectory && !extension
                                                "
                                                :name="'invalid_file_type'"
                                                :width="'64'"
                                                :height="'51'"
                                            />
                                            <file-icon
                                                :name="getIconName"
                                                width="84"
                                                height="84"
                                                v-else
                                            />
                                            <p class="text-sm text-gray-700">
                                                {{ dataItem.name }}
                                            </p>
                                        </div>

                                        <p
                                            class="text-base text-gray-700"
                                            v-html="documentError"
                                        ></p>
                                        <Button
                                            variant="secondary"
                                            @click="handlePreviewClose"
                                            v-if="noPreview"
                                        >
                                            <file-icon name="download" />
                                            <span>Download File</span></Button
                                        >
                                    </div>
                                </template>
                                <div
                                    class="tw-media-manager__image"
                                    v-show="previewType === 'image'"
                                ></div>
                                <div
                                    class="tw-filemanager tw-media-manager"
                                    ref="previewRef"
                                    v-show="
                                        !documentError && previewType === 'pdf'
                                    "
                                ></div>
                            </div>
                        </div>
                    </Transition>
                </div>
                <div
                    class="tw-media-manager__pagination"
                    v-if="pager.total > 10"
                >
                    <Pager
                        :skip="pager.skip"
                        :take="pager.take"
                        :total="pager.total"
                        :button-count="pager.butttonCount"
                        :info="pager.info"
                        :type="pager.type"
                        :page-sizes="
                            pager.pageSizes ? pager.pageSizeDefs : undefined
                        "
                        :previous-next="pager.previousNext"
                        @pagechange="handlePageChange"
                    />
                </div>
            </template>
        </Card>
    </div>
    <MediaActionsModal
        :visible="showActionModal"
        :action-type="actionType"
        :folders="folders"
        :dataItem="dataItem"
        @close-modal="handleCloseModal"
        @submit="handleSubmitActions"
        :decryptItParentId="decryptItParentId"
    />
</template>
<script setup>
import {
    ref,
    computed,
    onMounted,
    nextTick,
    watch,
    onBeforeUnmount,
    reactive,
} from "vue";

import Card from "@spa/components/Card/Card.vue";
import IconInput from "@spa/components/IconInput.vue";
import { Switch } from "@progress/kendo-vue-inputs";
import Button from "@spa/components/Buttons/Button.vue";
import Breadcrumb from "@spa/components/Breadcrumb/Breadcrumb.vue";
import MediaGrid from "@spa/components/MediaManager/MediaGrid.vue";
import MediaActionsModal from "@spa/components/MediaManager/MediaActionsModal.vue";
import { Transition } from "vue";
import useConfirm from "@spa/services/useConfirm";
import { router, usePage } from "@inertiajs/vue3";
import { useDocumentStore } from "@spa/stores/modules/document.store";
import { Pager } from "@progress/kendo-vue-data-tools";
import Spinner from "@spa/components/Loader/Spinner.vue";

const documentStore = useDocumentStore();

const confirm = useConfirm();

// Props
const props = defineProps({
    parentId: {
        type: String,
        required: true,
    },
    decryptItParentId: {
        type: String,
        required: true,
    },
    loginType: {
        type: String,
        required: true,
    },
    currentTab: {
        type: String,
        required: true,
    },
    files: {
        type: Array,
        required: true,
    },
    folders: {
        type: Array,
    },
    actionLabels: {
        type: Object,
        default: () => ({
            createFolder: "Create New Folder",
            upload: "Upload Documents",
            bulkMove: "Move Files",
            bulkDelete: "Delete Files",
            // viewAccess: "Provide View Access",
            bulkStudentAccess: "Student Bulk Access",
            bulkTeacherAccess: "Teacher Bulk Access",
        }),
    },
    breadcrumbs: {
        type: Array,
        default: () => [],
    },
    buttonVariants: {
        type: Object,
        default: () => ({
            upload: "primary",
            bulkDelete: "secondary",
            bulkStudentAccess: "primary",
            bulkTeacherAccess: "primary",
        }),
    },
    buttonIcons: {
        type: Object,
        default: () => ({
            upload: "upload",
            bulkMove: "move",
            bulkDelete: "trash",
            createFolder: "folder-outline",
            // bulkBookmark: "bookmark",
            viewAccess: "accessibility",
            bulkStudentAccess: "student",
            bulkTeacherAccess: "teacher",
        }),
    },
    viewAccess: {
        type: Boolean,
        default: false,
    },
    isShowBookMarkSwich: {
        type: Boolean,
        default: false,
    },
    columns: {
        type: Array,
        default: () => [],
    },
    pagination: {
        type: Object,
        default: () => ({}),
    },
});

// Emit
const emit = defineEmits([
    "upload",
    "move",
    "bulkMove",
    "delete",
    "bulkDelete",
    "openFolder",
    "rename",
    "bookmark",
    "isShowBookmark",
    "download",
    "viewDetails",
    "createFolder",
    "breadcrumbClick",
    "changePage",
]);

// Mounted
onMounted(() => {
    window.addEventListener("keydown", handleKeyboardEvent);
});

// Data
// const breadcrumbs = ref(props.breadcrumbs);
const keyword = ref("");
const keywordStore = computed(() => documentStore.getSearchKeywordStore);
const actionPopup = computed(() => documentStore.getCloseActionPopup);
const resetSelectedItem = computed(() => documentStore.getResetSelectedItem);
const selectedFiles = ref([]);
const showPreview = ref(false);
const previewRef = ref(null);
const previewFile = ref(null);
const showActionModal = ref(false);
const actionType = ref(null);
const dataItem = ref(null);
const selectedItemIndex = ref(-1);
const parentId = computed(() => props.parentId);
// const propsSkip = computed(() => props.pagination.skip);
const totalItems = computed(() => props.pagination.totalItems);
const breadcrumbs = computed(() => props.breadcrumbs);
const isShowBookMarkSwich = computed(() => props.isShowBookMarkSwich);
const decryptItParentId = computed(() => props.decryptItParentId);
const pager = reactive({
    skip: props.pagination.skip || 0,
    take: props.pagination.pageSizeValue || 0,
    buttonCount: 5,
    type: "numeric",
    info: true,
    pageSizes: true,
    previousNext: true,
    total: totalItems || 0,
    pageSizeDefs: props.pagination.pageSizes,
});
const documentError = ref(null);
const documentLoading = ref(false);
const noPreview = ref(false);
const previewType = ref("pdf");

// Computed
const actionButtons = computed(() =>
    Object.entries(props.actionLabels).reduce((acc, [key, label]) => {
        acc[key] = {
            label,
            variant: props.buttonVariants[key] || "secondary",
            icon: props.buttonIcons[key] || null,
            visible: determineVisibility(key),
        };
        return acc;
    }, {}),
);

const getIconName = computed(() => {
    let extension = dataItem.value.extension;
    let extensionMapping = {
        ".pdf": "pdf",
        ".xlsx": "xlsx",
        ".xls": "xlsx",
        ".png": "image",
        ".jpg": "image",
        ".jpeg": "image",
        ".docx": "docx",
    };

    return extensionMapping[extension] || "image";
});

const selectedFilesFolderIds = computed(() => {
    return selectedFiles.value.map((item) => item.folder_id);
});

// Watch
watch(keyword, (newKeyword, oldKeyword) => {
    handleKeywordSearch(newKeyword);
});

watch(keywordStore, (newKeyword, oldKeyword) => {
    keyword.value = newKeyword;
});

watch(actionPopup, (newKeyword, oldKeyword) => {
    if (newKeyword) {
        handleCloseModal();
    }
});
watch(resetSelectedItem, (newKeyword, oldKeyword) => {
    resetItem();
    documentStore.setResetSelectedItem(false);
});

// Methods
const handleKeywordSearch = (keyword) => {
    let data = {
        searchKeyword: keyword,
    };
    emit("keywordSearch", data);
};

const resetItem = () => {
    selectedFiles.value = [];

    document.querySelectorAll('input[type="checkbox"]').forEach((checkbox) => {
        checkbox.checked = false;
    });
};
const onBreadcrumbClick = (item) => {
    selectedFiles.value = [];
    item.isBookmark = isShowBookMarkSwich.value;
    document.querySelectorAll('input[type="checkbox"]').forEach((checkbox) => {
        checkbox.checked = false;
    });
    emit("breadcrumbClick", item);
};

const handleSelectItems = (selected) => {
    selectedFiles.value = selected;
};

// let selectedItemIndex.value = -1; // Start with no selection
const handleNavigation = (navigation) => {
    let data = props.files;
    const selectableItems = data.filter((item) => !item.isDirectory);

    if (navigation === "next") {
        // Move to the next element, ensuring it doesn't exceed the array length
        if (selectedItemIndex.value < selectableItems.length - 1) {
            selectedItemIndex.value++;
        }
    } else if (navigation === "prev") {
        // Move to the previous element, ensuring it doesn't go below 0
        if (selectedItemIndex.value > 0) {
            selectedItemIndex.value--;
        }
    }

    // Log the currently selected element
    if (
        selectedItemIndex.value >= 0 &&
        selectedItemIndex.value < selectableItems.length
    ) {
        handlePreview(selectableItems[selectedItemIndex.value]);
    } else {
        console.log("No selection.");
    }
};

const handlePreviewClose = () => {
    showPreview.value = false;
    selectedItemIndex.value = -1;
};

const handleHeaderAction = (action) => {
    actionType.value = action;
    if ("bulkDelete" !== action) {
        showActionModal.value = true;
    } else {
        let data = {
            // documentIds: selectedFiles.value,
            documentIds: selectedFilesFolderIds.value,
        };
        // selectedFiles.value = [];
        emit("bulkDelete", data);
    }
};

// Single File Methods

const handleGridActions = (action, data, type = "") => {
    dataItem.value = data;
    let requireModel = ["viewDetail", "rename", "move"];

    if (action === "openFolder") {
        handlePreviewClose();
    }

    if (requireModel.includes(action)) {
        showActionModal.value = true;
        actionType.value = action;
    }

    if (action === "preview") {
        handlePreview(data);
    }

    const actionHandlers = {
        openFolder: () => ({
            folderId: data.folder_id,
            isBookmark: isShowBookMarkSwich.value,
        }),
        download: () => ({
            folder_id: data.folder_id,
        }),
        dragMove: () => ({
            documentIds: [data.document_id],
            folderId: data.folder_id,
        }),
        delete: () => ({
            documentIds: [data.folder_id],
        }),
        bookmark: () => ({
            documentId: data.folder_id,
            type: type,
        }),
        changeSwitch: () => ({
            documentIds: [data.folder_id],
            student: data.student,
            teacher: data.teacher,
        }),
    };

    // console.log("Grid Actions", data, actionHandlers[action]());

    let requireEmits = [
        "openFolder",
        "download",
        "dragMove",
        "delete",
        "changeSwitch",
        "bookmark",
    ];
    if (requireEmits.includes(action)) {
        if (actionHandlers[action]) {
            selectedFiles.value = [];
            document
                .querySelectorAll('input[type="checkbox"]')
                .forEach((checkbox) => {
                    checkbox.checked = false;
                });
            let confirmMessage = {};
            if (action == "delete" && data.isDirectory) {
                confirmMessage = {
                    header: "Are you sure you want to delete this folder?",
                    message:
                        "This folder contains documents. Deleting it will permanently remove all of its contents. Are you sure you want to proceed?",
                };
            }
            emit(action, actionHandlers[action](), confirmMessage);
        }
    }
};

const handleSubmitActions = (data) => {
    console.log("Action", actionType.value);
    if (actionType.value === "rename2") {
        emit("rename", dataItem.value.id, data.data);
    } else {
        const actionsMap = {
            bulkMove: () => ({
                folderId: data.data.folder_id,
                // documentIds:
                //     selectedFiles.value?.length > 0
                //         ? selectedFiles.value
                //         : null,
                documentIds:
                    selectedFiles.value?.length > 0
                        ? selectedFilesFolderIds.value
                        : null,
            }),
            move: () => ({
                folderId: data.data.folder_id,
                documentIds: [dataItem.value.folder_id],
            }),
            createFolder: () => ({
                folder_or_file: data.data.folder_name,
                parent_id: parentId.value,
            }),
            upload: () => ({
                files: data.data.files,
                folderId: data.data.folder_id,
                newFolderName: data.data.folder_name,
            }),
            rename: () => ({
                folder_or_file: data.data.name,
                documentId: dataItem.value.id,
            }),
            viewAccess: () => ({
                // documentIds:
                //     selectedFiles.value?.length > 0
                //         ? selectedFiles.value
                //         : null,
                documentIds:
                    selectedFiles.value?.length > 0
                        ? selectedFilesFolderIds.value
                        : null,
                student: data.data.student_view ? true : false,
                teacher: data.data.teacher_view ? true : false,
            }),
            bulkStudentAccess: () => ({
                documentIds:
                    selectedFiles.value?.length > 0
                        ? selectedFilesFolderIds.value
                        : null,
                student: data.data.student_view ? true : false,
            }),
            bulkTeacherAccess: () => ({
                documentIds:
                    selectedFiles.value?.length > 0
                        ? selectedFilesFolderIds.value
                        : null,
                teacher: data.data.student_view ? true : false,
            }),
        };

        const actionHandler = actionsMap[actionType.value];
        if (actionHandler) {
            const data = actionHandler();
            emit(actionType.value, data);
        }
    }
};

// Modal Methods
const handlePreview = (data) => {
    dataItem.value = data;
    showPreview.value = true;
    // Find Index and assign it to var
    const selectableItems = props.files.filter((item) => !item.isDirectory);
    selectedItemIndex.value = selectableItems.findIndex(
        (item) => item.id == data.id,
    );
    if (data.extension == ".pdf") {
        previewType.value = "pdf";
        nextTick(() => {
            previewDocument(data);
        });
    } else if ([".jpg", ".jpeg", ".png"].includes(data.extension)) {
        previewType.value = "image";
        previewImage(data);
    } else {
        previewType.value = "noPreview";
        documentError.value = "Preview not available for this file type.";
        noPreview.value = true;
    }
};

const handleCloseModal = () => {
    showActionModal.value = false;
};

// Determine visibility of buttons
const determineVisibility = (action) => {
    if (props.loginType == "students") {
        // Always hide action button of student login for course and subject tab
        if (props.currentTab == "miscellaneous") {
            if (action == "bulkMove") {
                return selectedFiles.value.length > 0;
            } else {
                return false;
            }
        } else if (
            props.currentTab == "course" ||
            props.currentTab == "subject"
        ) {
            return false;
        }
    }
    if (["bulkDelete", "bulkMove", "bulkBookmark"].includes(action)) {
        return (
            selectedFiles.value.length > 0 &&
            !hasAutoGeneratedTrue(selectedFiles.value)
        );
    }
    if (["createFolder", "upload"].includes(action)) {
        return selectedFiles.value.length == 0;
    }

    if (
        ["viewAccess", "bulkStudentAccess", "bulkTeacherAccess"].includes(
            action,
        )
    ) {
        return props.viewAccess && selectedFiles.value.length > 0;
    }
    return true;
};

const handleShowBookmark = (e) => {
    const data = {
        isBookmark: e.value,
        parentId: parentId.value,
    };
    emit("isShowBookmark", data);
};

const previewDocument = (data) => {
    noPreview.value = false;
    documentLoading.value = true;
    if (!previewRef.value) {
        console.error("studentDocument ref is undefined.");
        return;
    }
    const componentRef = previewRef.value;
    const fileUrl = encodeURI(data.download_path);

    // Destroy existing Kendo PDF Viewer instance if it exists
    const existingPdfViewer = $(componentRef).data("kendoPDFViewer");
    if (existingPdfViewer) {
        existingPdfViewer.destroy();
        $(componentRef).empty(); // Clear the DOM element to avoid appending new instances
    }

    $.when(
        $.getScript(
            "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.js",
        ),
        $.getScript(
            "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js",
        ),
    )
        .done(function () {
            window.pdfjsLib.GlobalWorkerOptions.workerSrc =
                "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js";
            setupKendoPDFViewer(fileUrl);
        })
        .fail((err) => {
            console.error("Failed to load PDF.js scripts:", err);
        });
};

const setupKendoPDFViewer = (fileUrl) => {
    const componentRef = previewRef.value;

    // Prevent re-initialization
    const existing = $(componentRef).data("kendoPDFViewer");
    if (existing) {
        existing.destroy();
        $(componentRef).empty();
        console.warn("PDF Viewer already initialized.");
        return;
    }

    const pdfView = $(componentRef)
        .kendoPDFViewer({
            pdfjsProcessing: {
                file: fileUrl,
            },
            width: "100%",
            height: "100%",
            render: function (e) {
                documentError.value = null;
                documentLoading.value = false;
            },
            error: function (e) {
                e.preventDefault();
                documentLoading.value = false;
                documentError.value = e.message;
            },
        })
        .data("kendoPDFViewer");

    if (!pdfView) {
        console.error("Failed to initialize Kendo PDF Viewer.");
    }
};

const handleKeyboardEvent = (event) => {
    if (event.key === "ArrowRight") {
        handleNavigation("next");
    } else if (event.key === "ArrowLeft") {
        handleNavigation("prev");
    }
};

const previewImage = (data) => {
    documentError.value = null;
    documentLoading.value = true;
    noPreview.value = false;
    const img = new Image();
    img.src = data.download_path;
    img.className = 'h-full w-full object-contain';
    img.onload = () => {
        document.querySelector(".tw-media-manager__image").innerHTML = '';
        document.querySelector(".tw-media-manager__image").append(img);

            // `<img src="${data.download_path}" class="h-full w-full object-contain" />`;
        documentLoading.value = false;
    };
};

const handlePageChange = (e) => {
    let skip = e.skip || 0;
    let take = parseInt(e.take || 0);
    pager.skip = skip;
    pager.take = take;
    let pageNumber = 1;
    if (!isNaN(pager.take) && pager.take > 0) {
        pageNumber = Math.floor(pager.skip / pager.take) + 1;
    }
    if (
        pageNumber != props.pagination.currentPage ||
        take != props.pagination.pageSizeValue
    ) {
        emit("changePage", pageNumber, pager.take);
    }
};

const handleSortChange = (sort) => {
    emit("sortChange", sort);
};

const hasAutoGeneratedTrue = (arr) => {
    return arr.some((item) => item.is_autogenerated == true);
};

// On BeforeUnmount
onBeforeUnmount(() => {
    window.removeEventListener("keydown", handleKeyboardEvent);
});
</script>
<style lang=""></style>
